{% if dbType == 'postgresql' %}
-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('{{ functionName }}', '{{ parentMenuId }}', '1', '{{ businessName }}', '{{ moduleName }}/{{ businessName }}/index', 1, 0, 'C', '0', '0', '{{ permissionPrefix }}:list', '#', 'admin', current_timestamp, '', null, '{{ functionName }}菜单');

-- 按钮父菜单ID
select max(menu_id) from sys_menu;

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('{{ functionName }}查询', max(menu_id), '1',  '#', '', 1, 0, 'F', '0', '0', '{{ permissionPrefix }}:query',        '#', 'admin', current_timestamp, '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('{{ functionName }}新增', max(menu_id), '2',  '#', '', 1, 0, 'F', '0', '0', '{{ permissionPrefix }}:add',          '#', 'admin', current_timestamp, '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('{{ functionName }}修改', max(menu_id), '3',  '#', '', 1, 0, 'F', '0', '0', '{{ permissionPrefix }}:edit',         '#', 'admin', current_timestamp, '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('{{ functionName }}删除', max(menu_id), '4',  '#', '', 1, 0, 'F', '0', '0', '{{ permissionPrefix }}:remove',       '#', 'admin', current_timestamp, '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('{{ functionName }}导出', max(menu_id), '5',  '#', '', 1, 0, 'F', '0', '0', '{{ permissionPrefix }}:export',       '#', 'admin', current_timestamp, '', null, '');
{% else %}
-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('{{ functionName }}', '{{ parentMenuId }}', '1', '{{ businessName }}', '{{ moduleName }}/{{ businessName }}/index', 1, 0, 'C', '0', '0', '{{ permissionPrefix }}:list', '#', 'admin', sysdate(), '', null, '{{ functionName }}菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('{{ functionName }}查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', '{{ permissionPrefix }}:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('{{ functionName }}新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', '{{ permissionPrefix }}:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('{{ functionName }}修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', '{{ permissionPrefix }}:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('{{ functionName }}删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', '{{ permissionPrefix }}:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('{{ functionName }}导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', '{{ permissionPrefix }}:export',       '#', 'admin', sysdate(), '', null, '');
{% endif %}