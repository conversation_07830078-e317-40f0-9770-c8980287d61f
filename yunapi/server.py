import os
from contextlib import asynccontextmanager
from fastapi import Fast<PERSON><PERSON>
from fastapi.openapi.utils import get_openapi
from fastapi.openapi.docs import get_swagger_ui_html
from config.env import AppConfig
from config.get_db import init_create_table
from config.get_redis import RedisUtil
from config.get_scheduler import SchedulerUtil
from exceptions.handle import handle_exception
from middlewares.handle import handle_middleware
from module_admin.controller.cache_controller import cacheController
from module_admin.controller.captcha_controller import captchaController
from module_admin.controller.common_controller import commonController
from module_admin.controller.config_controller import configController
from module_admin.controller.dept_controller import deptController
from module_admin.controller.dict_controller import dictController
from module_admin.controller.log_controller import logController
from module_admin.controller.login_controller import loginController
from module_admin.controller.job_controller import jobController
from module_admin.controller.menu_controller import menuController
from module_admin.controller.notice_controller import noticeController
from module_admin.controller.online_controller import onlineController
from module_admin.controller.post_controler import postController
from module_admin.controller.role_controller import roleController
from module_admin.controller.server_controller import serverController
from module_admin.controller.user_controller import userController
from module_admin.controller.internal.internal_role_controller import router as internalRoleController
from module_admin.controller.experience_table_controller import experienceTableController
from module_admin.controller.company_controller import companyController
from module_admin.controller.store_controller import storeController
from module_admin.controller.ccuser_controller import ccuserController
from module_admin.controller.company_transaction_controller import companyTransactionController
from module_admin.controller.recharge_management_controller import rechargeManagementController
from module_admin.controller.company_withdrawal_controller import companyWithdrawalController
from module_admin.controller.training_categories_controller import trainingCategoriesController
from module_admin.controller.training_courses_controller import trainingCoursesController
from module_admin.controller.product_sync_controller import productSyncController, productSyncPublicController
from module_admin.controller.order_controller import orderController
from module_generator.controller.gen_controller import genController
from sub_applications.handle import handle_sub_applications
from utils.common_util import worship
from utils.log_util import logger
from module_admin.controller.internal.internal_menu_controller import internalMenuController


# 生命周期事件
@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.info(f'{AppConfig.app_name}开始启动')

    # 设置系统时区为中国标准时间 (UTC+8)
    os.environ['TZ'] = 'Asia/Shanghai'
    logger.info('已设置系统时区为 Asia/Shanghai (UTC+8)')

    worship()
    await init_create_table()
    app.state.redis = await RedisUtil.create_redis_pool()
    await RedisUtil.init_sys_dict(app.state.redis)
    await RedisUtil.init_sys_config(app.state.redis)
    await SchedulerUtil.init_system_scheduler()
    logger.info(f'{AppConfig.app_name}启动成功')
    yield
    await RedisUtil.close_redis_pool(app)
    await SchedulerUtil.close_system_scheduler()


def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema

    openapi_schema = get_openapi(
        title=AppConfig.app_name,
        version=AppConfig.app_version,
        description=f'{AppConfig.app_name}接口文档',
        routes=app.routes,
    )

    # 自定义 Swagger UI 配置
    openapi_schema["info"]["x-logo"] = {
        "url": "https://fastapi.tiangolo.com/img/logo-margin/logo-teal.png"
    }
    openapi_schema["info"]["contact"] = {
        "name": "API Support",
        "url": "http://example.com/contact/",
        "email": "<EMAIL>",
    }
    openapi_schema["info"]["license"] = {
        "name": "Apache 2.0",
        "url": "https://www.apache.org/licenses/LICENSE-2.0.html",
    }

    app.openapi_schema = openapi_schema
    return app.openapi_schema


# 初始化FastAPI对象
app = FastAPI(
    title=AppConfig.app_name,
    description=f'{AppConfig.app_name}接口文档',
    version=AppConfig.app_version,
    lifespan=lifespan,
    docs_url=None,  # 禁用默认的 docs_url
    redoc_url=None,  # 禁用默认的 redoc_url
)

# 设置自定义 OpenAPI schema
app.openapi = custom_openapi

# 挂载子应用
handle_sub_applications(app)
# 加载中间件处理方法
handle_middleware(app)
# 加载全局异常处理方法
handle_exception(app)


# 加载路由列表
controller_list = [
    {'router': loginController, 'tags': ['登录模块']},
    {'router': captchaController, 'tags': ['验证码模块']},
    {'router': userController, 'tags': ['系统管理-用户管理']},
    {'router': roleController, 'tags': ['系统管理-角色管理']},
    {'router': menuController, 'tags': ['系统管理-菜单管理']},
    {'router': deptController, 'tags': ['系统管理-部门管理']},
    {'router': postController, 'tags': ['系统管理-岗位管理']},
    {'router': dictController, 'tags': ['系统管理-字典管理']},
    {'router': configController, 'tags': ['系统管理-参数管理']},
    {'router': noticeController, 'tags': ['系统管理-通知公告管理']},
    {'router': logController, 'tags': ['系统管理-日志管理']},
    {'router': onlineController, 'tags': ['系统监控-在线用户']},
    {'router': jobController, 'tags': ['系统监控-定时任务']},
    {'router': serverController, 'tags': ['系统监控-菜单管理']},
    {'router': cacheController, 'tags': ['系统监控-缓存监控']},
    {'router': commonController, 'tags': ['通用模块']},
    {'router': genController, 'tags': ['代码生成']},
    {'router': internalRoleController, 'tags': ['内部角色管理']},
    {'router': internalMenuController, 'tags': ['内部菜单管理']},
    {'router': experienceTableController, 'tags': ['商户管理']},
    {'router': companyController, 'tags': ['公司管理']},
    {'router': storeController, 'tags': ['门店管理']},
    {'router': ccuserController, 'tags': ['客户管理']},
    {'router': companyTransactionController, 'tags': ['财务管理-资金流水']},
    {'router': rechargeManagementController, 'tags': ['财务管理-充值管理']},
    {'router': companyWithdrawalController, 'tags': ['财务管理-提现管理']},
    {'router': trainingCategoriesController, 'tags': ['培训中心-分类管理']},
    {'router': trainingCoursesController, 'tags': ['培训中心-课程管理']},
    {'router': productSyncController, 'tags': ['产品管理-产品同步']},
    {'router': productSyncPublicController, 'tags': ['产品管理-产品同步(公共接口)']},
    {'router': orderController, 'tags': ['订单管理']},
]

for controller in controller_list:
    app.include_router(router=controller.get('router'), tags=controller.get('tags'))

# 自定义 Swagger UI 路由
@app.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    return get_swagger_ui_html(
        openapi_url=f"/openapi.json",
        title=f"{AppConfig.app_name} - Swagger UI",
        oauth2_redirect_url=None,
        swagger_js_url="https://unpkg.com/swagger-ui-dist@5.9.0/swagger-ui-bundle.js",
        swagger_css_url="https://unpkg.com/swagger-ui-dist@5.9.0/swagger-ui.css",
        swagger_favicon_url="https://fastapi.tiangolo.com/img/favicon.png",
        swagger_ui_parameters={
            "docExpansion": "none",
            "defaultModelsExpandDepth": -1,
            "displayOperationId": True,
            "filter": True,
            "deepLinking": True,
            "persistAuthorization": True,
        }
    )
