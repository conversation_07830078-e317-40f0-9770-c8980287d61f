import os
import random
from datetime import datetime
from typing import Optional, Tuple
from fastapi import UploadFile
import oss2
from config.env import OssConfig
from utils.log_util import logger
from exceptions.exception import ServiceException


class OssUtil:
    """
    阿里云OSS工具类
    """

    @classmethod
    def get_oss_client(cls) -> oss2.Bucket:
        """
        获取OSS客户端

        :return: OSS Bucket实例
        """
        try:
            # 创建认证对象
            auth = oss2.Auth(OssConfig.oss_access_key_id, OssConfig.oss_access_key_secret)
            
            # 创建Bucket对象
            bucket = oss2.Bucket(auth, OssConfig.oss_endpoint, OssConfig.oss_bucket_name)
            
            return bucket
        except Exception as e:
            logger.error(f"创建OSS客户端失败: {str(e)}")
            raise ServiceException(message="OSS服务连接失败")

    @classmethod
    def generate_random_number(cls) -> str:
        """
        生成3位数字构成的字符串

        :return: 3位数字构成的字符串
        """
        random_number = random.randint(1, 999)
        return f'{random_number:03}'

    @classmethod
    def generate_object_key(cls, file: UploadFile) -> str:
        """
        生成OSS对象键（文件路径）

        :param file: 上传文件对象
        :return: 对象键
        """
        # 获取文件扩展名
        file_extension = file.filename.rsplit('.', 1)[-1] if '.' in file.filename else ''
        
        # 生成时间戳
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        
        # 生成随机数
        random_num = cls.generate_random_number()
        
        # 生成文件名（不包含原始文件名，避免中文问题）
        filename = f"upload_{timestamp}_{random_num}.{file_extension}"
        
        # 生成对象键（按年/月/日分目录）
        date_path = datetime.now().strftime("%Y/%m/%d")
        object_key = f"{OssConfig.oss_directory}/upload/{date_path}/{filename}"

        return object_key

    @classmethod
    async def upload_file_to_oss(cls, file: UploadFile) -> Tuple[str, str]:
        """
        上传文件到OSS

        :param file: 上传文件对象
        :return: (对象键, 完整URL)
        """
        try:
            # 记录配置信息（不记录敏感信息）
            logger.info(f"OSS配置 - Bucket: {OssConfig.oss_bucket_name}, Endpoint: {OssConfig.oss_endpoint}")

            # 获取OSS客户端
            bucket = cls.get_oss_client()

            # 生成对象键
            object_key = cls.generate_object_key(file)
            logger.info(f"生成对象键: {object_key}")

            # 读取文件内容
            file_content = await file.read()
            logger.info(f"文件大小: {len(file_content)} bytes")

            # 上传文件到OSS
            result = bucket.put_object(
                object_key,
                file_content,
                headers={'Content-Type': file.content_type or 'application/octet-stream'}
            )

            logger.info(f"OSS响应状态: {result.status}")

            # 检查上传结果
            if result.status == 200:
                # 生成完整的访问URL
                file_url = f"{OssConfig.oss_url_prefix}/{object_key}"
                logger.info(f"文件上传成功: {object_key}")
                return object_key, file_url
            else:
                logger.error(f"文件上传失败: HTTP {result.status}")
                raise ServiceException(message=f"文件上传失败: HTTP {result.status}")
                
        except Exception as e:
            logger.error(f"上传文件到OSS失败: {str(e)}")
            if isinstance(e, ServiceException):
                raise e
            else:
                raise ServiceException(message="文件上传失败，请重试")

    @classmethod
    def delete_file_from_oss(cls, object_key: str) -> bool:
        """
        从OSS删除文件

        :param object_key: 对象键
        :return: 删除是否成功
        """
        try:
            # 获取OSS客户端
            bucket = cls.get_oss_client()
            
            # 删除文件
            result = bucket.delete_object(object_key)
            
            # 检查删除结果
            if result.status == 204:
                logger.info(f"文件删除成功: {object_key}")
                return True
            else:
                logger.error(f"文件删除失败: HTTP {result.status}")
                return False
                
        except Exception as e:
            logger.error(f"从OSS删除文件失败: {str(e)}")
            return False

    @classmethod
    def check_file_exists_in_oss(cls, object_key: str) -> bool:
        """
        检查文件在OSS中是否存在

        :param object_key: 对象键
        :return: 文件是否存在
        """
        try:
            # 获取OSS客户端
            bucket = cls.get_oss_client()
            
            # 检查文件是否存在
            exists = bucket.object_exists(object_key)
            
            return exists
            
        except Exception as e:
            logger.error(f"检查OSS文件是否存在失败: {str(e)}")
            return False

    @classmethod
    def extract_object_key_from_url(cls, file_url: str) -> Optional[str]:
        """
        从完整URL中提取对象键

        :param file_url: 完整的文件URL
        :return: 对象键，如果URL格式不正确则返回None
        """
        try:
            if file_url.startswith(OssConfig.oss_url_prefix):
                # 移除域名部分，获取对象键
                object_key = file_url.replace(f"{OssConfig.oss_url_prefix}/", "")
                return object_key
            return None
        except Exception as e:
            logger.error(f"提取对象键失败: {str(e)}")
            return None
