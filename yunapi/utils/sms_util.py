import json
import hashlib
import hmac
import base64
import urllib.parse
from datetime import datetime, timezone
import uuid
import aiohttp
from config.env import SmsConfig
from utils.log_util import logger


class SmsUtil:
    """
    阿里云短信发送工具类
    """

    @staticmethod
    def _percent_encode(s: str) -> str:
        """
        URL编码（阿里云API要求）

        :param s: 待编码字符串
        :return: 编码后的字符串
        """
        res = urllib.parse.quote(str(s), safe='')
        res = res.replace('+', '%20')
        res = res.replace('*', '%2A')
        res = res.replace('%7E', '~')
        return res

    @staticmethod
    def _generate_signature(params: dict, secret: str) -> str:
        """
        生成阿里云API签名

        :param params: 请求参数
        :param secret: AccessKey Secret
        :return: 签名字符串
        """
        # 1. 按照参数名称的字典顺序排序
        sorted_params = sorted(params.items(), key=lambda x: x[0])

        # 2. 构建规范化请求字符串
        canonicalized_query_string = ""
        for k, v in sorted_params:
            canonicalized_query_string += "&" + SmsUtil._percent_encode(k) + "=" + SmsUtil._percent_encode(v)

        # 3. 构建待签名字符串
        string_to_sign = "GET&%2F&" + SmsUtil._percent_encode(canonicalized_query_string[1:])

        # 4. 计算签名
        key = secret + "&"
        hmac_obj = hmac.new(key.encode('utf-8'), string_to_sign.encode('utf-8'), hashlib.sha1)
        signature = base64.b64encode(hmac_obj.digest()).decode('utf-8')

        return signature

    @staticmethod
    def _get_iso_timestamp() -> str:
        """
        获取ISO 8601格式的UTC时间戳

        :return: ISO 8601格式的UTC时间戳
        """
        # 使用timezone-aware的方式生成UTC时间戳
        return datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ")

    @staticmethod
    async def send_sms(phone: str, template_code: str, template_params: dict = None) -> dict:
        """
        发送短信

        :param phone: 手机号
        :param template_code: 短信模板代码
        :param template_params: 模板参数
        :return: 发送结果
        """
        try:
            logger.info(f"开始发送短信: 手机号={phone}, 模板={template_code}, 参数={template_params}")
            # 构建公共参数
            common_params = {
                "AccessKeyId": SmsConfig.sms_access_key_id,
                "Action": "SendSms",
                "Format": "JSON",
                "RegionId": "cn-hangzhou",
                "SignatureMethod": "HMAC-SHA1",
                "SignatureNonce": str(uuid.uuid4()),
                "SignatureVersion": "1.0",
                "Timestamp": SmsUtil._get_iso_timestamp(),
                "Version": "2017-05-25"
            }

            # 构建业务参数
            business_params = {
                "PhoneNumbers": phone,
                "SignName": SmsConfig.sms_sign_name,
                "TemplateCode": template_code
            }

            # 合并参数
            params = {**common_params, **business_params}

            # 添加模板参数
            if template_params:
                params['TemplateParam'] = json.dumps(template_params)

            # 生成签名
            signature = SmsUtil._generate_signature(params, SmsConfig.sms_access_key_secret)
            params['Signature'] = signature

            logger.info(f"短信请求参数: {params}")

            # 构建请求URL
            query_params = []
            for k, v in params.items():
                query_params.append(f"{k}={urllib.parse.quote(str(v))}")
            request_url = f"{SmsConfig.sms_api_url}?{'&'.join(query_params)}"

            # 发送请求
            async with aiohttp.ClientSession() as session:
                async with session.get(request_url) as response:
                    result = await response.json()
                    logger.info(f"短信API响应: {result}")

                    if response.status != 200 or result.get("Code") != "OK":
                        logger.error(f"阿里云短信API返回错误: {result}")
                        return {'success': False, 'message': f"短信发送失败: {result.get('Message', '未知错误')}", 'data': result}

                    return {'success': True, 'message': '短信发送成功', 'data': result}

        except Exception as e:
            logger.error(f"短信发送异常: 手机号={phone}, 模板={template_code}, 异常={str(e)}")
            return {'success': False, 'message': f"短信发送异常: {str(e)}", 'data': None}

    @staticmethod
    async def send_verification_code(phone: str, code: str) -> dict:
        """
        发送验证码短信

        :param phone: 手机号
        :param code: 验证码
        :return: 发送结果
        """
        template_params = {'code': code}
        return await SmsUtil.send_sms(phone, SmsConfig.sms_verification_template_code, template_params)

    @staticmethod
    async def send_audit_success_sms(phone: str, username: str, password: str) -> dict:
        """
        发送审核通过短信

        :param phone: 手机号
        :param username: 用户名
        :param password: 密码
        :return: 发送结果
        """
        template_params = {
            'userno': username,
            'pwd': password
        }
        return await SmsUtil.send_sms(phone, SmsConfig.sms_audit_success_template_code, template_params)
