# Python虚拟环境
venv/
env/
ENV/

# Python缓存文件
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# 层相关文件夹
yunapi-layer/

# 日志文件
logs/
*.log

# 本地配置文件
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE相关
.idea/
.vscode/
*.swp
*.swo

# 系统文件
.DS_Store
Thumbs.db

# 其他
.pytest_cache/
coverage.xml
*.cover
.coverage
htmlcov/
.tox/
.nox/

# 阿里云函数计算临时文件
.s/ 