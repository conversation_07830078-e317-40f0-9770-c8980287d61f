from fastapi import APIRouter, Depends, Request
from sqlalchemy.ext.asyncio import AsyncSession
from config.enums import BusinessType
from config.get_db import get_db
from module_admin.annotation.log_annotation import Log
from module_admin.aspect.interface_auth import CheckUserInterfaceAuth
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.vo.store_vo import StoreModel, StorePageQueryModel, DeleteStoreModel
from module_admin.service.store_service import StoreService
from module_admin.service.login_service import LoginService, CurrentUserModel
from utils.log_util import logger
from utils.page_util import PageResponseModel
from utils.response_util import ResponseUtil


storeController = APIRouter(prefix='/merchant/store', dependencies=[Depends(LoginService.get_current_user)])


@storeController.get(
    '/list', response_model=PageResponseModel, dependencies=[Depends(CheckUserInterfaceAuth('merchant:store:list'))]
)
async def get_merchant_store_list(
    request: Request,
    store_page_query: StorePageQueryModel = Depends(StorePageQueryModel.as_query),
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取门店列表
    """
    # 获取分页数据
    store_page_query_result = await StoreService.get_store_list_services(query_db, store_page_query, is_page=True)
    logger.info('获取成功')

    return ResponseUtil.success(model_content=store_page_query_result)


@storeController.get('/{store_id}', dependencies=[Depends(CheckUserInterfaceAuth('merchant:store:query'))])
async def query_merchant_store_detail(
    request: Request, store_id: int, query_db: AsyncSession = Depends(get_db)
):
    """
    获取门店详细信息
    """
    store_detail_result = await StoreService.store_detail_services(query_db, store_id)
    logger.info(f'获取store_id为{store_id}的信息成功')

    return ResponseUtil.success(data=store_detail_result)


@storeController.post('', dependencies=[Depends(CheckUserInterfaceAuth('merchant:store:add'))])
@Log(title='门店管理', business_type=BusinessType.INSERT)
async def add_merchant_store(
    request: Request,
    add_store: StoreModel,
    query_db: AsyncSession = Depends(get_db),
):
    """
    新增门店
    """
    add_store_result = await StoreService.add_store_services(query_db, add_store)
    logger.info(add_store_result.message)

    return ResponseUtil.success(msg=add_store_result.message)


@storeController.put('', dependencies=[Depends(CheckUserInterfaceAuth('merchant:store:edit'))])
@Log(title='门店管理', business_type=BusinessType.UPDATE)
async def edit_merchant_store(
    request: Request,
    edit_store: StoreModel,
    query_db: AsyncSession = Depends(get_db),
):
    """
    修改门店
    """
    edit_store_result = await StoreService.edit_store_services(query_db, edit_store)
    logger.info(edit_store_result.message)

    return ResponseUtil.success(msg=edit_store_result.message)


@storeController.delete('/{ids}', dependencies=[Depends(CheckUserInterfaceAuth('merchant:store:remove'))])
@Log(title='门店管理', business_type=BusinessType.DELETE)
async def delete_merchant_store(request: Request, ids: str, query_db: AsyncSession = Depends(get_db)):
    """
    删除门店
    """
    delete_store = DeleteStoreModel(ids=ids)
    delete_store_result = await StoreService.delete_store_services(query_db, delete_store)
    logger.info(delete_store_result.message)

    return ResponseUtil.success(msg=delete_store_result.message)
