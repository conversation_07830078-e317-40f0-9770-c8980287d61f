from fastapi import APIRouter, Depends, Request, Form
from sqlalchemy.ext.asyncio import AsyncSession
from config.enums import BusinessType
from config.get_db import get_db
from module_admin.annotation.log_annotation import Log
from module_admin.aspect.interface_auth import CheckUserInterfaceAuth
from module_admin.entity.vo.recharge_management_vo import (
    RechargeManagementModel,
    RechargeManagementPageQueryModel,
    RechargeManagementResponseModel
)
from module_admin.service.recharge_management_service import RechargeManagementService
from module_admin.service.login_service import LoginService, CurrentUserModel
from utils.common_util import bytes2file_response
from utils.log_util import logger
from utils.page_util import PageResponseModel
from utils.response_util import ResponseUtil


rechargeManagementController = APIRouter(
    prefix='/finance/recharge',
    dependencies=[Depends(LoginService.get_current_user)]
)


@rechargeManagementController.get(
    '/list', 
    response_model=PageResponseModel, 
    dependencies=[Depends(CheckUserInterfaceAuth('finance:recharge:list'))]
)
async def get_recharge_list(
    request: Request,
    recharge_page_query: RechargeManagementPageQueryModel = Depends(),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """
    获取充值管理列表
    
    :param request: 请求对象
    :param recharge_page_query: 分页查询参数
    :param query_db: 数据库会话
    :param current_user: 当前用户
    :return: 充值管理列表
    """
    try:
        # 获取分页数据
        recharge_page_query_result = await RechargeManagementService.get_recharge_list_services(
            query_db, recharge_page_query, is_page=True
        )
        logger.info('获取充值管理列表成功')

        return ResponseUtil.success(model_content=recharge_page_query_result)
    except Exception as e:
        logger.error(f'获取充值管理列表失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取充值管理列表失败: {str(e)}')


@rechargeManagementController.post(
    '/export',
    dependencies=[Depends(CheckUserInterfaceAuth('finance:recharge:export'))]
)
@Log(title='充值管理', business_type=BusinessType.EXPORT)
async def export_recharge_list(
    request: Request,
    recharge_query: RechargeManagementPageQueryModel = Form(),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """
    导出充值管理列表

    :param request: 请求对象
    :param recharge_query: 查询参数
    :param query_db: 数据库会话
    :param current_user: 当前用户
    :return: 导出数据
    """
    # 检查是否有前端处理好的导出数据
    if recharge_query.export_data:
        import json
        # 使用前端传递的数据
        recharge_list_result = json.loads(recharge_query.export_data)
    else:
        # 获取全部数据（不分页）
        recharge_list_result = await RechargeManagementService.get_recharge_list_services(
            query_db, recharge_query, is_page=False
        )

    # 导出为Excel格式
    recharge_export_result = await RechargeManagementService.export_recharge_list_services(recharge_list_result)
    logger.info('导出充值管理列表成功')

    return ResponseUtil.streaming(data=bytes2file_response(recharge_export_result))


@rechargeManagementController.get(
    '/{transaction_id}',
    dependencies=[Depends(CheckUserInterfaceAuth('finance:recharge:query'))]
)
async def get_recharge_detail(
    request: Request,
    transaction_id: int,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """
    获取充值详情
    
    :param request: 请求对象
    :param transaction_id: 流水ID
    :param query_db: 数据库会话
    :param current_user: 当前用户
    :return: 充值详情
    """
    try:
        recharge_detail_result = await RechargeManagementService.get_recharge_detail_services(
            query_db, transaction_id
        )
        
        if recharge_detail_result is None:
            return ResponseUtil.error(msg='充值记录不存在或不是余额充值记录')
            
        logger.info(f'获取充值详情成功，流水ID: {transaction_id}')

        return ResponseUtil.success(data=recharge_detail_result)
    except Exception as e:
        logger.error(f'获取充值详情失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取充值详情失败: {str(e)}')
