from datetime import datetime
from fastapi import APIRouter, Depends, Form, Request
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic_validation_decorator import <PERSON>idate<PERSON>ields
from config.get_db import get_db
from config.enums import BusinessType
from module_admin.annotation.log_annotation import Log
from module_admin.aspect.interface_auth import CheckUserInterfaceAuth
from module_admin.entity.vo.order_vo import (
    AddOrderModel,
    DeleteOrderModel,
    EditOrderModel,
    OrderModel,
    OrderPageQueryModel,
    UpdateOrderRemarkModel,
)
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from module_admin.service.order_service import OrderService
from utils.common_util import bytes2file_response
from utils.log_util import logger
from utils.page_util import PageResponseModel
from utils.response_util import ResponseUtil


orderController = APIRouter(prefix='/order', dependencies=[Depends(LoginService.get_current_user)])


@orderController.get(
    '/list', response_model=PageResponseModel, dependencies=[Depends(CheckUserInterfaceAuth('order:list'))]
)
async def get_order_list(
    request: Request,
    order_page_query: OrderPageQueryModel = Depends(OrderPageQueryModel.as_query),
    query_db: AsyncSession = Depends(get_db),
):
    # 获取分页数据
    order_page_query_result = await OrderService.get_order_list_services(query_db, order_page_query, is_page=True)
    logger.info('获取成功')

    return ResponseUtil.success(model_content=order_page_query_result)


@orderController.get('/store-names', dependencies=[Depends(CheckUserInterfaceAuth('order:list'))])
async def get_store_names(
    request: Request,
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取所有存在订单记录的门店名称列表
    """
    store_names = await OrderService.get_store_names_services(query_db)
    logger.info('获取门店名称列表成功')

    return ResponseUtil.success(data=store_names)


@orderController.get('/statistics', dependencies=[Depends(CheckUserInterfaceAuth('order:list'))])
async def get_order_statistics(
    request: Request,
    order_page_query: OrderPageQueryModel = Depends(OrderPageQueryModel.as_query),
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取订单统计信息
    """
    statistics = await OrderService.get_order_statistics_services(query_db, order_page_query)
    logger.info('获取订单统计信息成功')

    return ResponseUtil.success(data=statistics)


@orderController.post('', dependencies=[Depends(CheckUserInterfaceAuth('order:add'))])
@ValidateFields(validate_model='add_order')
@Log(title='订单管理', business_type=BusinessType.INSERT)
async def add_order(
    request: Request,
    add_order: AddOrderModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    add_order_result = await OrderService.add_order_services(query_db, add_order)
    logger.info(add_order_result.message)

    return ResponseUtil.success(msg=add_order_result.message)


@orderController.put('', dependencies=[Depends(CheckUserInterfaceAuth('order:edit'))])
@ValidateFields(validate_model='edit_order')
@Log(title='订单管理', business_type=BusinessType.UPDATE)
async def edit_order(
    request: Request,
    edit_order: EditOrderModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    edit_order_result = await OrderService.edit_order_services(query_db, edit_order)
    logger.info(edit_order_result.message)

    return ResponseUtil.success(msg=edit_order_result.message)


@orderController.put('/remark', dependencies=[Depends(CheckUserInterfaceAuth('order:edit'))])
@Log(title='订单管理', business_type=BusinessType.UPDATE)
async def update_order_remark(
    request: Request,
    update_remark: UpdateOrderRemarkModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    update_result = await OrderService.update_order_remark_services(query_db, update_remark)
    logger.info(update_result.message)

    return ResponseUtil.success(msg=update_result.message)


@orderController.put('/cancel/{order_ids}', dependencies=[Depends(CheckUserInterfaceAuth('order:cancel'))])
@Log(title='订单管理', business_type=BusinessType.UPDATE)
async def cancel_order(
    request: Request,
    order_ids: str,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    cancel_order = DeleteOrderModel(ids=order_ids)  # 复用DeleteOrderModel，因为结构相同
    cancel_order_result = await OrderService.cancel_order_services(query_db, cancel_order)
    logger.info(cancel_order_result.message)

    return ResponseUtil.success(msg=cancel_order_result.message)


@orderController.get(
    '/{order_id}', dependencies=[Depends(CheckUserInterfaceAuth('order:query'))]
)
async def query_detail_order(
    request: Request,
    order_id: int,
    query_db: AsyncSession = Depends(get_db),
):
    order_detail_result = await OrderService.order_detail_services(query_db, order_id)
    logger.info(f'获取order_id为{order_id}的信息成功')

    return ResponseUtil.success(data=order_detail_result)


@orderController.post('/export', dependencies=[Depends(CheckUserInterfaceAuth('order:export'))])
@Log(title='订单管理', business_type=BusinessType.EXPORT)
async def export_order_list(
    request: Request,
    query_db: AsyncSession = Depends(get_db),
):
    # 解析请求体中的查询参数
    try:
        form_data = await request.form()
        # 构建查询参数对象
        query_params = {}
        for key, value in form_data.items():
            if value:  # 只添加非空值
                query_params[key] = value

        # 创建查询对象
        order_page_query = OrderPageQueryModel(**query_params)

        # 获取全量数据
        order_query_result = await OrderService.get_order_list_services(query_db, order_page_query, is_page=False)
        order_export_result = await OrderService.export_order_list_services(order_query_result)
        logger.info('导出成功')

        return ResponseUtil.streaming(data=bytes2file_response(order_export_result))
    except Exception as e:
        logger.error(f'导出失败: {str(e)}')
        raise e


@orderController.get(
    '/dispatch-staff/{order_id}', dependencies=[Depends(CheckUserInterfaceAuth('order:list'))]
)
async def get_available_dispatch_staff(
    request: Request,
    order_id: int,
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取可选派单人员列表
    """
    available_staff_result = await OrderService.get_available_dispatch_staff_services(query_db, order_id)
    logger.info(f'获取订单{order_id}的可选派单人员成功')

    return ResponseUtil.success(data=available_staff_result)


@orderController.put('/dispatch-staff', dependencies=[Depends(CheckUserInterfaceAuth('order:edit'))])
@Log(title='订单管理', business_type=BusinessType.UPDATE)
async def update_order_dispatch_staff(
    request: Request,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    更新订单派单人员
    """
    # 获取表单数据
    form_data = await request.form()
    order_number = form_data.get('order_number')
    staff_id = form_data.get('staff_id')
    service_date = form_data.get('service_date')  # 可选参数
    service_hour = form_data.get('service_hour')  # 可选参数

    if not order_number or not staff_id:
        return ResponseUtil.error(msg='参数不完整')

    try:
        staff_id = int(staff_id)
    except ValueError:
        return ResponseUtil.error(msg='员工ID格式错误')

    update_result = await OrderService.update_order_dispatch_staff_services(
        query_db, order_number, staff_id, service_date, service_hour
    )
    logger.info(update_result.message)

    return ResponseUtil.success(msg=update_result.message)
