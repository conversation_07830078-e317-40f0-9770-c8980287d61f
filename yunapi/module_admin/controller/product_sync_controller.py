from datetime import datetime
from fastapi import APIRouter, Depends, Request
from pydantic_validation_decorator import <PERSON><PERSON>teFields
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
from config.enums import BusinessType
from config.get_db import get_db
from module_admin.annotation.log_annotation import Log
from module_admin.aspect.interface_auth import CheckUserInterfaceAuth
from module_admin.entity.vo.product_sync_vo import (
    ProductSyncModel, 
    ProductSyncPageQueryModel, 
    ProductSyncDetailModel,
    ProductSyncExportModel,
    DeleteProductSyncModel
)
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from module_admin.service.product_sync_service import ProductSyncService
from utils.common_util import bytes2file_response
from utils.log_util import logger
from utils.page_util import PageResponseModel
from utils.response_util import ResponseUtil


productSyncController = APIRouter(prefix='/merchant/productSync', dependencies=[Depends(LoginService.get_current_user)])

# 创建一个不需要认证的独立路由用于统计接口
productSyncPublicController = APIRouter(prefix='/public/productSync')


@productSyncController.get(
    '/list', response_model=PageResponseModel, dependencies=[Depends(CheckUserInterfaceAuth('merchant:productSync:list'))]
)
async def get_merchant_product_sync_list(
    request: Request,
    product_sync_page_query: ProductSyncPageQueryModel = Depends(ProductSyncPageQueryModel.as_query),
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取产品同步列表
    """
    # 获取分页数据
    product_sync_page_query_result = await ProductSyncService.get_product_sync_list_services(
        query_db, product_sync_page_query, is_page=True
    )
    logger.info('获取产品同步列表成功')

    return ResponseUtil.success(model_content=product_sync_page_query_result)


@productSyncController.get(
    '/{product_id}', 
    response_model=ProductSyncDetailModel, 
    dependencies=[Depends(CheckUserInterfaceAuth('merchant:productSync:query'))]
)
async def query_detail_merchant_product_sync(
    request: Request, 
    product_id: int, 
    query_db: AsyncSession = Depends(get_db)
):
    """
    获取产品同步详细信息
    """
    product_sync_detail_result = await ProductSyncService.product_sync_detail_services(query_db, product_id)
    logger.info(f'获取产品ID为{product_id}的详细信息成功')

    return ResponseUtil.success(data=product_sync_detail_result)


@productSyncController.post(
    '/export', 
    dependencies=[Depends(CheckUserInterfaceAuth('merchant:productSync:export'))]
)
@Log(title='产品同步管理', business_type=BusinessType.EXPORT)
async def export_merchant_product_sync_list(
    request: Request,
    product_sync_export: ProductSyncExportModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    导出产品同步列表
    """
    export_result = await ProductSyncService.export_product_sync_list_services(query_db, product_sync_export)
    logger.info('导出产品同步列表成功')

    return ResponseUtil.streaming(data=bytes2file_response(export_result))


@productSyncPublicController.get('/statistics')
async def get_merchant_product_sync_statistics(
    request: Request,
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取产品同步统计信息（无需认证）
    """
    try:
        statistics_result = await ProductSyncService.get_product_sync_statistics_services(query_db)
        logger.info('获取产品同步统计信息成功')
        return ResponseUtil.success(data=statistics_result)
    except Exception as e:
        logger.error(f'获取产品同步统计信息失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取统计信息失败: {str(e)}')


@productSyncController.post(
    '/sync', 
    dependencies=[Depends(CheckUserInterfaceAuth('merchant:productSync:sync'))]
)
@Log(title='产品同步管理', business_type=BusinessType.UPDATE)
async def sync_merchant_product_data(
    request: Request,
    delete_product_sync: DeleteProductSyncModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    同步产品数据
    """
    # 解析产品ID列表
    product_ids = [int(id.strip()) for id in delete_product_sync.product_ids.split(',') if id.strip()]
    
    if not product_ids:
        return ResponseUtil.failure(msg='请选择要同步的产品')
    
    sync_result = await ProductSyncService.sync_product_data_services(query_db, product_ids)
    logger.info(f'产品数据同步完成: {sync_result["message"]}')

    return ResponseUtil.success(data=sync_result, msg=sync_result['message'])


@productSyncController.get(
    '/all', 
    response_model=List[ProductSyncModel], 
    dependencies=[Depends(CheckUserInterfaceAuth('merchant:productSync:list'))]
)
async def get_all_merchant_product_sync(
    request: Request,
    product_sync_query: ProductSyncPageQueryModel = Depends(ProductSyncPageQueryModel.as_query),
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取所有产品同步数据（不分页）
    """
    # 获取不分页数据
    product_sync_all_result = await ProductSyncService.get_product_sync_list_services(
        query_db, product_sync_query, is_page=False
    )
    logger.info('获取所有产品同步数据成功')

    return ResponseUtil.success(data=product_sync_all_result)


@productSyncController.post(
    '/batch-sync', 
    dependencies=[Depends(CheckUserInterfaceAuth('merchant:productSync:sync'))]
)
@Log(title='产品同步管理', business_type=BusinessType.UPDATE)
async def batch_sync_merchant_product_data(
    request: Request,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    批量同步所有产品数据
    """
    # 获取所有需要同步的产品ID
    all_products = await ProductSyncService.get_product_sync_list_services(
        query_db, ProductSyncPageQueryModel(), is_page=False
    )
    
    if not all_products:
        return ResponseUtil.failure(msg='没有找到需要同步的产品')
    
    # 提取产品ID列表
    product_ids = [product.id for product in all_products if hasattr(product, 'id')]
    
    sync_result = await ProductSyncService.sync_product_data_services(query_db, product_ids)
    logger.info(f'批量产品数据同步完成: {sync_result["message"]}')

    return ResponseUtil.success(data=sync_result, msg=sync_result['message'])
