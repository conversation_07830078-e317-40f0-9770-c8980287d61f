from fastapi import APIRouter, Depends, Request
from sqlalchemy.ext.asyncio import AsyncSession
from config.get_db import get_db
from module_admin.service.ccuser_service import CcuserService
from module_admin.entity.vo.ccuser_vo import CcuserModel, CcuserPageQueryModel, DeleteCcuserModel
from utils.response_util import ResponseUtil
from utils.page_util import PageResponseModel
from utils.log_util import logger
from module_admin.service.login_service import LoginService
from module_admin.aspect.interface_auth import CheckUserInterfaceAuth
from module_admin.annotation.log_annotation import Log
from config.enums import BusinessType


ccuserController = APIRouter(prefix='/merchant/ccuser', dependencies=[Depends(LoginService.get_current_user)])


@ccuserController.get(
    '/list', response_model=PageResponseModel, dependencies=[Depends(CheckUserInterfaceAuth('merchant:ccuser:list'))]
)
async def get_merchant_ccuser_list(
    request: Request,
    ccuser_page_query: CcuserPageQueryModel = Depends(CcuserPageQueryModel.as_query),
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取客户列表
    """
    # 获取分页数据
    ccuser_page_query_result = await CcuserService.get_ccuser_list_services(query_db, ccuser_page_query, is_page=True)
    logger.info('获取成功')

    return ResponseUtil.success(model_content=ccuser_page_query_result)


@ccuserController.get('/{ccuser_id}', dependencies=[Depends(CheckUserInterfaceAuth('merchant:ccuser:query'))])
async def get_merchant_ccuser_detail(request: Request, ccuser_id: int, query_db: AsyncSession = Depends(get_db)):
    """
    获取客户详细信息
    """
    ccuser_detail_result = await CcuserService.get_ccuser_detail_services(query_db, ccuser_id)
    logger.info(f'获取ccuser_id为{ccuser_id}的信息成功')

    return ResponseUtil.success(data=ccuser_detail_result)


@ccuserController.post('', dependencies=[Depends(CheckUserInterfaceAuth('merchant:ccuser:add'))])
@Log(title='客户管理', business_type=BusinessType.INSERT)
async def add_merchant_ccuser(
    request: Request,
    add_ccuser: CcuserModel,
    query_db: AsyncSession = Depends(get_db),
):
    """
    新增客户
    """
    add_ccuser_result = await CcuserService.add_ccuser_services(query_db, add_ccuser)
    logger.info(add_ccuser_result.message)

    return ResponseUtil.success(msg=add_ccuser_result.message)


@ccuserController.put('', dependencies=[Depends(CheckUserInterfaceAuth('merchant:ccuser:edit'))])
@Log(title='客户管理', business_type=BusinessType.UPDATE)
async def edit_merchant_ccuser(
    request: Request,
    edit_ccuser: CcuserModel,
    query_db: AsyncSession = Depends(get_db),
):
    """
    修改客户
    """
    edit_ccuser_result = await CcuserService.edit_ccuser_services(query_db, edit_ccuser)
    logger.info(edit_ccuser_result.message)

    return ResponseUtil.success(msg=edit_ccuser_result.message)


@ccuserController.delete('/{ids}', dependencies=[Depends(CheckUserInterfaceAuth('merchant:ccuser:remove'))])
@Log(title='客户管理', business_type=BusinessType.DELETE)
async def delete_merchant_ccuser(request: Request, ids: str, query_db: AsyncSession = Depends(get_db)):
    """
    删除客户
    """
    delete_ccuser = DeleteCcuserModel(ids=ids)
    delete_ccuser_result = await CcuserService.delete_ccuser_services(query_db, delete_ccuser)
    logger.info(delete_ccuser_result.message)

    return ResponseUtil.success(msg=delete_ccuser_result.message)
