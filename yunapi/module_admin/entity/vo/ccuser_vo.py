from datetime import datetime
from decimal import Decimal
from typing import Optional
from pydantic import BaseModel, Field, ConfigDict


class CcuserModel(BaseModel):
    """
    客户信息模型
    """
    model_config = ConfigDict(alias_generator=None, from_attributes=True, populate_by_name=True)

    id: Optional[int] = Field(default=None, description="主键ID")
    store_uuid: Optional[str] = Field(default=None, alias="storeUuid", description="门店UUID")
    uuid: Optional[str] = Field(default=None, description="客户唯一标识")
    number: Optional[str] = Field(default=None, description="客户编号")
    name: Optional[str] = Field(default=None, description="客户姓名")
    mobile: Optional[str] = Field(default=None, description="客户手机号")
    wechat_number: Optional[str] = Field(default=None, alias="wechatNumber", description="客户微信号")
    wechat_nickname: Optional[str] = Field(default=None, alias="wechatNickname", description="客户微信昵称")
    id_number: Optional[str] = Field(default=None, alias="idNumber", description="客户身份证号")
    id_number_name: Optional[str] = Field(default=None, alias="idNumberName", description="客户身份证姓名")
    id_number_sex: Optional[str] = Field(default=None, alias="idNumberSex", description="客户身份证性别")
    id_number_birthday: Optional[datetime] = Field(default=None, alias="idNumberBirthday", description="客户身份证生日")
    sex: Optional[str] = Field(default=None, description="客户性别")
    user_uuid: Optional[int] = Field(default=None, alias="userUuid", description="创建用户的唯一标识符")
    after_user_uuid: Optional[int] = Field(default=None, alias="afterUserUuid", description="最后修改用户的唯一标识符")
    user_name: Optional[str] = Field(default=None, alias="userName", description="创建用户姓名")
    after_user_name: Optional[str] = Field(default=None, alias="afterUserName", description="最后修改用户姓名")
    source: Optional[str] = Field(default=None, description="客户来源")
    status: Optional[str] = Field(default="1", description="客户状态")
    status_name: Optional[str] = Field(default="正常", alias="statusName", description="客户状态名称")
    create_time: Optional[datetime] = Field(default=None, alias="createTime", description="创建时间")
    create_time_stamp: Optional[int] = Field(default=None, alias="createTimeStamp", description="创建时间戳")
    update_time: Optional[datetime] = Field(default=None, alias="updateTime", description="更新时间")
    create_user_name: Optional[str] = Field(default=None, alias="createUserName", description="创建用户姓名")
    common_status: Optional[str] = Field(default=None, alias="commonStatus", description="通用状态")
    is_delete: Optional[str] = Field(default="0", alias="isDelete", description="是否删除")
    created_by: Optional[str] = Field(default=None, alias="createdBy", description="创建人")
    updated_by: Optional[str] = Field(default=None, alias="updatedBy", description="修改人")
    created_at: Optional[datetime] = Field(default=None, alias="createdAt", description="创建时间")
    updated_at: Optional[datetime] = Field(default=None, alias="updatedAt", description="修改时间")
    headimg: Optional[str] = Field(default=None, description="头像")
    level: Optional[int] = Field(default=0, description="会员等级")
    amount: Optional[Decimal] = Field(default=None, description="金额")
    passwd: Optional[str] = Field(default=None, description="密码")


class CcuserPageQueryModel(BaseModel):
    """
    客户分页查询模型
    """
    model_config = ConfigDict(alias_generator=None, populate_by_name=True)

    page_num: int = Field(default=1, alias="pageNum", description="当前页码")
    page_size: int = Field(default=10, alias="pageSize", description="每页条数")
    name: Optional[str] = Field(default=None, description="客户姓名")
    mobile: Optional[str] = Field(default=None, description="客户手机号")
    status: Optional[str] = Field(default=None, description="客户状态")
    source: Optional[str] = Field(default=None, description="客户来源")
    store_uuid: Optional[str] = Field(default=None, alias="storeUuid", description="门店UUID")
    begin_time: Optional[str] = Field(default=None, alias="beginTime", description="开始时间")
    end_time: Optional[str] = Field(default=None, alias="endTime", description="结束时间")

    @classmethod
    def as_query(cls,
                 pageNum: int = 1,
                 pageSize: int = 10,
                 name: str = None,
                 mobile: str = None,
                 status: str = None,
                 source: str = None,
                 storeUuid: str = None,
                 beginTime: str = None,
                 endTime: str = None):
        return cls(
            page_num=pageNum,
            page_size=pageSize,
            name=name,
            mobile=mobile,
            status=status,
            source=source,
            store_uuid=storeUuid,
            begin_time=beginTime,
            end_time=endTime
        )


class DeleteCcuserModel(BaseModel):
    """
    删除客户模型
    """
    model_config = ConfigDict(alias_generator=None)

    ids: str = Field(description="客户id列表，用逗号分隔")
