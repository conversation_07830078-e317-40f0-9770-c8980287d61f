from datetime import datetime
from decimal import Decimal
from pydantic import BaseModel, ConfigDict, Field
from pydantic.alias_generators import to_camel
from pydantic_validation_decorator import NotBlank
from typing import List, Optional
from module_admin.annotation.pydantic_annotation import as_query


class ExperienceTableModel(BaseModel):
    """
    商户表对应pydantic模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: Optional[int] = Field(default=None, description='商户ID')
    company_name: Optional[str] = Field(default=None, description='公司名称')
    license_image: Optional[str] = Field(default=None, description='营业执照图片')
    license_name: Optional[str] = Field(default=None, description='营业执照名称')
    license_code: Optional[str] = Field(default=None, description='营业执照代码')
    legal_address: Optional[str] = Field(default=None, description='法定地址')
    legal_person_name: Optional[str] = Field(default=None, description='法人姓名')
    phone_number: Optional[str] = Field(default=None, description='电话号码')
    selected_address: Optional[str] = Field(default=None, description='选择的地址')
    detail_address: Optional[str] = Field(default=None, description='详细地址')
    referrer: Optional[str] = Field(default=None, description='推荐人')
    selected_products: Optional[str] = Field(default=None, description='选择的产品')
    total_price: Optional[Decimal] = Field(default=None, description='总价')
    is_all_selected: Optional[int] = Field(default=None, description='是否全选')
    period_type: Optional[str] = Field(default=None, description='周期类型')
    license_expiry_date: Optional[datetime] = Field(default=None, description='营业执照到期日期')
    status: Optional[int] = Field(default=0, description='状态(0待审核 1通过 2未通过)')

    @NotBlank(field_name='company_name', message='公司名称不能为空')
    def get_company_name(self):
        return self.company_name


class ExperienceTableQueryModel(BaseModel):
    """
    商户不分页查询对应pydantic模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    company_name: Optional[str] = Field(default=None, description='公司名称')
    license_name: Optional[str] = Field(default=None, description='营业执照名称')
    license_code: Optional[str] = Field(default=None, description='营业执照代码')
    legal_person_name: Optional[str] = Field(default=None, description='法人姓名')
    phone_number: Optional[str] = Field(default=None, description='电话号码')
    begin_time: Optional[str] = Field(default=None, description='开始时间')
    end_time: Optional[str] = Field(default=None, description='结束时间')
    status: Optional[int] = Field(default=None, description='状态(0待审核 1通过 2未通过 3全部)')


@as_query
class ExperienceTablePageQueryModel(ExperienceTableQueryModel):
    """
    商户分页查询对应pydantic模型
    """

    page_num: int = Field(default=1, description='当前页码')
    page_size: int = Field(default=10, description='每页条数')


class DeleteExperienceTableModel(BaseModel):
    """
    删除商户对应pydantic模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    ids: str = Field(description='商户ID字符串，多个以逗号分隔')
