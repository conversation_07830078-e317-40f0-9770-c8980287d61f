from datetime import datetime
from decimal import Decimal
from typing import Optional, Literal, List
from pydantic import BaseModel, Field, ConfigDict
from pydantic.alias_generators import to_camel
from module_admin.annotation.pydantic_annotation import as_query


class CompanyModel(BaseModel):
    """
    公司管理模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: Optional[str] = Field(default=None, description='公司ID')
    name: str = Field(..., description='公司名称')
    city_id: Optional[str] = Field(default=None, description='城市ID')
    city: Optional[str] = Field(default=None, description='城市名称')
    address: Optional[str] = Field(default=None, description='公司地址')
    address_desc: Optional[str] = Field(default=None, description='地址描述')
    version_names: Optional[List[str]] = Field(default=[], description='版本名称列表')
    balance: Optional[Decimal] = Field(default=None, description='公司余额')
    status: Optional[Literal['0', '1']] = Field(default='1', description='状态(1:正常,0:冻结)')
    is_delete: Optional[Literal['0', '1']] = Field(default='0', description='是否删除(1:已删除,0:未删除)')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    update_time: Optional[datetime] = Field(default=None, description='更新时间')


class CompanyQueryModel(BaseModel):
    """
    公司不分页查询模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    name: Optional[str] = Field(default=None, description='公司名称')
    city: Optional[str] = Field(default=None, description='城市名称')
    status: Optional[Literal['0', '1']] = Field(default=None, description='状态(1:正常,0:冻结)')
    begin_time: Optional[str] = Field(default=None, description='开始时间')
    end_time: Optional[str] = Field(default=None, description='结束时间')


@as_query
class CompanyPageQueryModel(CompanyQueryModel):
    """
    公司管理分页查询模型
    """

    page_num: int = Field(default=1, description='当前页码')
    page_size: int = Field(default=10, description='每页条数')


class DeleteCompanyModel(BaseModel):
    """
    删除公司模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    ids: str = Field(description='需要删除的公司ID')


class CompanyRechargeModel(BaseModel):
    """
    公司充值模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    company_id: Optional[str] = Field(default=None, description='公司ID')
    business_type: Optional[str] = Field(default='RECHARGE', description='业务类型')
    amount: Decimal = Field(..., gt=0, description='充值金额，必须大于0')
    pay_type: Optional[str] = Field(default=None, description='支付方式')
    external_transaction_id: Optional[str] = Field(default=None, description='外部交易ID')
    description: Optional[str] = Field(default=None, description='交易描述')
    remark: Optional[str] = Field(default=None, description='备注')


class CompanyRechargeResponseModel(BaseModel):
    """
    公司充值响应模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    order_id: str = Field(description='订单ID')
    order_no: str = Field(description='订单号')
    company_id: str = Field(description='公司ID')
    amount: Decimal = Field(description='充值金额')
    balance_before: Decimal = Field(description='充值前余额')
    balance_after: Decimal = Field(description='充值后余额')
    create_time: datetime = Field(description='创建时间')


class BatchRenewCompanyVersionsModel(BaseModel):
    """
    批量续费公司版本模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    company_ids: List[str] = Field(..., description='公司ID列表')
    extend_months: int = Field(..., gt=0, le=120, description='延长月数，必须大于0且小于等于120个月')
    operation_type: Literal['EXTEND', 'RESET'] = Field(default='EXTEND', description='操作类型：EXTEND-延长，RESET-重置')
    custom_expire_time: Optional[str] = Field(default=None, description='自定义到期时间（YYYY-MM-DD HH:MM:SS格式）')
    remark: Optional[str] = Field(default=None, description='操作备注')


class BatchRenewResponseModel(BaseModel):
    """
    批量续费响应模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    operation_id: str = Field(description='操作ID，用于撤销操作')
    total_companies: int = Field(description='总公司数')
    success_count: int = Field(description='成功续费公司数')
    failed_count: int = Field(description='失败公司数')
    failed_details: Optional[List[dict]] = Field(default=[], description='失败详情')
    create_time: datetime = Field(description='操作时间')


class UndoBatchRenewModel(BaseModel):
    """
    撤销批量续费模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    operation_id: str = Field(..., description='要撤销的操作ID')
