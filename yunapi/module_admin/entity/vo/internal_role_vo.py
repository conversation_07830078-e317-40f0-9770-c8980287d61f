from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field


class InternalRoleModel(BaseModel):
    """内部角色信息"""
    roleId: Optional[int] = Field(None, description="角色ID")
    parentId: Optional[int] = Field(0, description="父角色ID")
    roleName: Optional[str] = Field(None, description="角色名称")
    roleKey: Optional[str] = Field(None, description="角色权限字符串")
    orderNum: Optional[int] = Field(0, description="显示顺序")
    status: Optional[str] = Field("0", description="角色状态")
    createTime: Optional[datetime] = Field(None, description="创建时间")
    updateTime: Optional[datetime] = Field(None, description="更新时间")
    remark: Optional[str] = Field(None, description="备注")
    children: Optional[List['InternalRoleModel']] = Field(None, description="子角色")
    menuIds: Optional[List[int]] = Field(None, description="菜单ID列表")


class InternalRolePageQueryModel(BaseModel):
    """内部角色分页查询"""
    pageNum: int = Field(1, description="页码")
    pageSize: int = Field(10, description="每页数量")
    roleName: Optional[str] = Field(None, description="角色名称")
    roleKey: Optional[str] = Field(None, description="权限字符")
    status: Optional[str] = Field(None, description="状态")
    beginTime: Optional[str] = Field(None, description="开始时间")
    endTime: Optional[str] = Field(None, description="结束时间")


class InternalRoleResponseModel(BaseModel):
    """内部角色响应"""
    total: int = Field(0, description="总记录数")
    rows: List[InternalRoleModel] = Field([], description="内部角色列表")


class DeleteInternalRoleModel(BaseModel):
    """删除内部角色"""
    roleIds: str = Field(..., description="角色ID串")
    updateTime: Optional[datetime] = Field(None, description="更新时间") 