from datetime import datetime
from decimal import Decimal
from typing import Optional, Literal
from pydantic import BaseModel, Field, ConfigDict
from pydantic.alias_generators import to_camel
from module_admin.annotation.pydantic_annotation import as_query


@as_query
class RechargeManagementPageQueryModel(BaseModel):
    """
    充值管理分页查询模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    page_num: int = Field(default=1, description='当前页码')
    page_size: int = Field(default=10, description='每页数量')
    company_uuid: Optional[str] = Field(default=None, description='公司UUID')
    transaction_no: Optional[str] = Field(default=None, description='交易流水号')
    business_type: Optional[str] = Field(default=None, description='业务类型')
    transaction_type: Optional[int] = Field(default=None, description='交易类型：1-收入，2-支出')
    operator_name: Optional[str] = Field(default=None, description='操作人姓名')
    transaction_status: Optional[Literal['PENDING', 'SUCCESS', 'FAILED', 'CANCELLED']] = Field(default=None, description='交易状态：PENDING-待处理，SUCCESS-成功，FAILED-失败，CANCELLED-已取消')
    begin_time: Optional[str] = Field(default=None, description='开始时间')
    end_time: Optional[str] = Field(default=None, description='结束时间')
    ids: Optional[str] = Field(default=None, description='流水ID列表，逗号分隔')
    export_data: Optional[str] = Field(default=None, description='前端处理好的导出数据JSON字符串')


@as_query
class RechargeManagementQueryModel(BaseModel):
    """
    充值管理查询模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    company_uuid: Optional[str] = Field(default=None, description='公司UUID')
    transaction_no: Optional[str] = Field(default=None, description='交易流水号')
    business_type: Optional[str] = Field(default=None, description='业务类型')
    transaction_type: Optional[int] = Field(default=None, description='交易类型：1-收入，2-支出')
    operator_name: Optional[str] = Field(default=None, description='操作人姓名')
    transaction_status: Optional[Literal['PENDING', 'SUCCESS', 'FAILED', 'CANCELLED']] = Field(default=None, description='交易状态：PENDING-待处理，SUCCESS-成功，FAILED-失败，CANCELLED-已取消')
    begin_time: Optional[str] = Field(default=None, description='开始时间')
    end_time: Optional[str] = Field(default=None, description='结束时间')


class RechargeManagementModel(BaseModel):
    """
    充值管理模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: Optional[int] = Field(default=None, description='流水ID')
    company_uuid: Optional[str] = Field(default=None, description='公司UUID')
    transaction_no: Optional[str] = Field(default=None, description='交易流水号')
    business_type: Optional[str] = Field(default=None, description='业务类型')
    business_type_name: Optional[str] = Field(default=None, description='业务类型名称')
    transaction_type: Optional[int] = Field(default=None, description='交易类型：1-收入，2-支出')
    amount: Optional[Decimal] = Field(default=None, description='交易金额')
    balance_before: Optional[Decimal] = Field(default=None, description='交易前余额')
    balance_after: Optional[Decimal] = Field(default=None, description='交易后余额')
    related_order_no: Optional[str] = Field(default=None, description='关联订单号')
    pay_type: Optional[str] = Field(default=None, description='支付方式类型')
    external_transaction_id: Optional[str] = Field(default=None, description='外部交易ID')
    operator_id: Optional[str] = Field(default=None, description='操作人ID')
    operator_name: Optional[str] = Field(default=None, description='操作人姓名')
    description: Optional[str] = Field(default=None, description='交易描述')
    remark: Optional[str] = Field(default=None, description='备注信息')
    transaction_time: Optional[datetime] = Field(default=None, description='交易时间')
    created_by: Optional[str] = Field(default=None, description='创建人')
    created_at: Optional[datetime] = Field(default=None, description='创建时间')


class RechargeManagementResponseModel(BaseModel):
    """
    充值管理响应模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: int = Field(description='流水ID')
    company_uuid: str = Field(description='公司UUID')
    transaction_no: str = Field(description='交易流水号')
    business_type: str = Field(description='业务类型')
    business_type_name: Optional[str] = Field(default=None, description='业务类型名称')
    transaction_type: int = Field(description='交易类型：1-收入，2-支出')
    transaction_type_name: str = Field(description='交易类型名称')
    amount: Decimal = Field(description='交易金额')
    balance_before: Decimal = Field(description='交易前余额')
    balance_after: Decimal = Field(description='交易后余额')
    related_order_no: Optional[str] = Field(default=None, description='关联订单号')
    pay_type: Optional[str] = Field(default=None, description='支付方式类型')
    pay_type_name: str = Field(description='支付方式名称')
    external_transaction_id: Optional[str] = Field(default=None, description='外部交易ID')
    operator_id: str = Field(description='操作人ID')
    operator_name: str = Field(description='操作人姓名')
    description: str = Field(description='交易描述')
    remark: Optional[str] = Field(default=None, description='备注信息')
    transaction_time: datetime = Field(description='交易时间')
    created_by: str = Field(description='创建人')
    created_at: datetime = Field(description='创建时间')
