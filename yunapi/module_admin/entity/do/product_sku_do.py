from datetime import datetime
from sqlalchemy import Column, DateTime, Integer, String, DECIMAL
from config.database import Base


class ProductSku(Base):
    """
    产品SKU表
    """

    __tablename__ = 'product_sku'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='SKU ID')
    productid = Column(Integer, nullable=False, comment='产品ID')
    name = Column(String(255), nullable=False, comment='SKU名称')
    now_price = Column(DECIMAL(10, 2), nullable=True, comment='现价')
    vip_price = Column(DECIMAL(10, 2), nullable=True, comment='VIP价格')
    duration = Column(Integer, nullable=True, comment='时长')
    type_price_unit = Column(String(50), nullable=True, comment='价格单位类型')
    define_commission = Column(DECIMAL(10, 2), nullable=True, default=0.00, comment='定义佣金')
    create_time = Column(DateTime, nullable=False, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, nullable=False, default=datetime.now, comment='更新时间')
    commission_type = Column(Integer, nullable=True, default=0, comment='佣金类型')
