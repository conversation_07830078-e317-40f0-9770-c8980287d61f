from datetime import datetime
from sqlalchemy import Column, DateTime, Integer, String, Text, DECIMAL
from config.database import Base


class Product(Base):
    """
    产品表
    """

    __tablename__ = 'product'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='产品ID')
    product_name = Column(String(100), nullable=False, comment='产品名称')
    company_uuid = Column(String(100), nullable=True, comment='公司UUID')
    img_id = Column(String(255), nullable=True, comment='图片ID')
    serve_type_name = Column(String(50), nullable=True, comment='服务类型名称')
    service_skill_id = Column(String(20), nullable=False, comment='服务技能ID')
    service_skill_name = Column(String(50), nullable=False, comment='服务技能名称')
    service_skill_main_id = Column(String(20), nullable=True, comment='主服务技能ID')
    service_skill_main_name = Column(String(50), nullable=True, comment='主服务技能名称')
    online_store_num = Column(Integer, nullable=False, default=0, comment='在线门店数量')
    is_all_support_store = Column(Integer, nullable=True, default=0, comment='是否所有门店支持')
    sum_num = Column(Integer, nullable=False, default=0, comment='总数量')
    is_delete = Column(Integer, nullable=False, default=0, comment='是否删除')
    is_open_service_phone = Column(Integer, nullable=False, default=0, comment='是否开启服务电话')
    type = Column(String(20), nullable=False, comment='类型')
    type_name = Column(String(50), nullable=False, comment='类型名称')
    display_edit = Column(Integer, nullable=True, default=1, comment='显示编辑')
    display_delete = Column(Integer, nullable=True, default=1, comment='显示删除')
    display_detail = Column(Integer, nullable=True, default=1, comment='显示详情')
    display_edit_product_detail = Column(Integer, nullable=True, default=1, comment='显示编辑产品详情')
    is_gaode_line = Column(Integer, nullable=True, default=0, comment='是否高德线路')
    op_user_name = Column(String(50), nullable=True, comment='操作用户名')
    op_time = Column(DateTime, nullable=True, comment='操作时间')
    create_time = Column(DateTime, nullable=False, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, nullable=False, default=datetime.now, comment='更新时间')
    product_status = Column(Integer, nullable=True, default=0, comment='产品状态')
    details = Column(String(2000), nullable=True, comment='详情')
    min_number = Column(Integer, nullable=False, default=0, comment='最小数量')
    max_number = Column(Integer, nullable=False, default=0, comment='最大数量')
    video_id = Column(String(64), nullable=True, comment='视频ID')
    uuid = Column(String(64), nullable=True, comment='UUID')
    is_vip_use = Column(Integer, nullable=True, default=0, comment='是否VIP使用')
    is_bj = Column(Integer, nullable=True, default=0, comment='是否北京')
    buy_notes = Column(Text, nullable=True, comment='购买说明')
    buy_agreement = Column(Text, nullable=True, comment='购买协议')
    rating_weight = Column(Integer, nullable=True, default=0, comment='评分权重')
    proximity_weight = Column(Integer, nullable=True, default=0, comment='距离权重')
    wait_time_weight = Column(Integer, nullable=True, default=0, comment='等待时间权重')
    ishot = Column(Integer, nullable=True, default=0, comment='是否热门')
