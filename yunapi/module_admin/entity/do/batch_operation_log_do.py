from datetime import datetime
from sqlalchemy import Column, DateTime, String, Integer, Text, SmallInteger, BigInteger
from config.database import Base


class BatchOperationLog(Base):
    """
    批量操作日志表
    """

    __tablename__ = 'batch_operation_log'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='ID')
    operation_id = Column(String(36), nullable=False, unique=True, comment='操作ID')
    operation_type = Column(String(50), nullable=False, comment='操作类型')
    operation_data = Column(Text, nullable=True, comment='操作数据JSON')
    rollback_data = Column(Text, nullable=True, comment='回滚数据JSON')
    total_count = Column(Integer, nullable=False, default=0, comment='总数量')
    success_count = Column(Integer, nullable=False, default=0, comment='成功数量')
    failed_count = Column(Integer, nullable=False, default=0, comment='失败数量')
    status = Column(SmallInteger, nullable=False, default=1, comment='状态：1-成功，0-失败，2-已撤销')
    create_time = Column(DateTime, nullable=False, default=datetime.now(), comment='创建时间')
    update_time = Column(DateTime, nullable=False, default=datetime.now(), onupdate=datetime.now(), comment='更新时间')
    create_by = Column(String(64), nullable=True, comment='创建者')
    update_by = Column(String(64), nullable=True, comment='更新者')
    remark = Column(String(500), nullable=True, comment='备注')
