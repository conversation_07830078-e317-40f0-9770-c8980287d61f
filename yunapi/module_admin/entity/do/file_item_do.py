from datetime import datetime
from sqlalchemy import Column, DateTime, String, BigInteger
from config.database import Base


class FileItem(Base):
    """
    文件子表
    """

    __tablename__ = 'file_item'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    main_id = Column(BigInteger, nullable=False, comment='主表ID')
    file_name = Column(String(255), nullable=False, comment='文件名称')
    file_type = Column(String(32), nullable=False, comment='文件类型')
    file_size = Column(BigInteger, nullable=False, comment='文件大小(字节)')
    file_url = Column(String(500), nullable=False, comment='文件URL')
    created_at = Column(DateTime, nullable=False, default=datetime.now, comment='创建时间')
