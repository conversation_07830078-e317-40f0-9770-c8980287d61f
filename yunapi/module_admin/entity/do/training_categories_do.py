from datetime import datetime
from sqlalchemy import Column, String, Text, Integer, DateTime, SmallInteger
from config.database import Base


class TrainingCategories(Base):
    """
    培训分类表
    """
    __tablename__ = 'training_categories'

    id = Column(String(64), primary_key=True, comment='分类ID')
    uuid = Column(String(64), nullable=False, comment='分类唯一标识')
    name = Column(String(100), nullable=False, comment='分类名称')
    description = Column(Text, comment='分类描述')
    icon = Column(String(255), comment='分类图标')
    parent_id = Column(String(64), comment='父分类ID')
    sort_order = Column(Integer, default=0, comment='排序权重')
    status = Column(SmallInteger, default=1, comment='状态(1:启用,0:禁用)')
    create_time = Column(DateTime, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
