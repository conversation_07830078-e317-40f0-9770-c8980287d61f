from datetime import datetime
from sqlalchemy import Column, DateTime, String, DECIMAL, Integer, BigInteger, Text
from config.database import Base


class CompanyTransaction(Base):
    """
    公司资金流水表
    """

    __tablename__ = 'company_transaction'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='流水ID')
    company_uuid = Column(String(64), nullable=False, comment='公司UUID')
    transaction_no = Column(String(64), nullable=False, comment='交易流水号')
    business_type = Column(String(30), nullable=False, comment='业务类型')
    business_type_name = Column(String(50), nullable=True, comment='业务类型名称')
    transaction_type = Column(Integer, nullable=False, comment='交易类型(1:充值,2:消费,3:退款)')
    amount = Column(DECIMAL(15, 2), nullable=False, comment='交易金额')
    balance_before = Column(DECIMAL(15, 2), nullable=False, comment='交易前余额')
    balance_after = Column(DECIMAL(15, 2), nullable=False, comment='交易后余额')
    related_order_no = Column(String(64), nullable=True, comment='关联订单号')
    pay_type = Column(String(30), nullable=True, comment='支付类型')
    external_transaction_id = Column(String(100), nullable=True, comment='外部交易ID')
    operator_id = Column(String(64), nullable=False, comment='操作员ID')
    operator_name = Column(String(100), nullable=False, comment='操作员名称')
    description = Column(String(500), nullable=False, comment='交易描述')
    remark = Column(Text, nullable=True, comment='备注')
    transaction_time = Column(DateTime, nullable=False, default=datetime.now, comment='交易时间')
    created_by = Column(String(64), nullable=False, comment='创建人')
    created_at = Column(DateTime, nullable=False, default=datetime.now, comment='创建时间')
    transaction_status = Column(String(20), nullable=True, default='PENDING', comment='交易状态：PENDING-待处理，SUCCESS-成功，FAILED-失败，CANCELLED-已取消')
