from sqlalchemy import Column, Integer, String, DateTime, Text, SmallInteger
from sqlalchemy.orm import relationship
from config.get_db import Base


class SysInternalMenu(Base):
    """内部员工菜单表"""
    __tablename__ = 'sys_internal_menu'

    menu_id = Column(Integer, primary_key=True, autoincrement=True, comment='菜单ID')
    parent_id = Column(Integer, default=0, comment='父菜单ID，0表示一级菜单')
    menu_name = Column(String(50), nullable=False, comment='菜单名称')
    menu_type = Column(String(1), nullable=False, comment='菜单类型: M目录 C菜单 F按钮')
    client_type = Column(Integer, default=0, comment='客户端类型: 0 sys网页端 1 store门店端')
    icon = Column(String(100), comment='菜单图标')
    order_num = Column(Integer, default=0, comment='显示顺序')
    path = Column(String(200), default='', comment='路由地址')
    component = Column(String(255), comment='组件路径')
    is_new = Column(SmallInteger, comment='是否新上菜单')
    is_cache = Column(SmallInteger, default=0, comment='是否缓存: 0缓存 1不缓存')
    visible = Column(String(1), default='0', comment='菜单状态: 0显示 1隐藏')
    status = Column(String(1), default='0', comment='菜单状态: 0正常 1停用')
    perms = Column(String(100), comment='权限标识')
    create_time = Column(DateTime, comment='创建时间')
    update_time = Column(DateTime, comment='更新时间')
    remark = Column(String(500), default='', comment='备注')

    # 关联关系
    roles = relationship('SysInternalRole', secondary='sys_internal_role_menu') 