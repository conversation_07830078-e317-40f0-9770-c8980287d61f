from datetime import datetime
from decimal import Decimal
from sqlalchemy import Column, Integer, String, DateTime, BigInteger, Date, DECIMAL, Text
from config.database import Base


class Ccuser(Base):
    """
    客户基础信息表
    """
    __tablename__ = 'ccuser'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    store_uuid = Column(String(64), comment='门店UUID')
    uuid = Column(String(64), nullable=False, unique=True, comment='客户唯一标识')
    number = Column(String(20), comment='客户编号')
    name = Column(String(50), comment='客户姓名')
    mobile = Column(String(20), comment='客户手机号')
    wechat_number = Column(String(50), comment='客户微信号')
    wechat_nickname = Column(String(50), comment='客户微信昵称')
    id_number = Column(String(20), comment='客户身份证号')
    id_number_name = Column(String(50), comment='客户身份证姓名')
    id_number_sex = Column(String(10), comment='客户身份证性别')
    id_number_birthday = Column(Date, comment='客户身份证生日')
    sex = Column(String(10), comment='客户性别')
    user_uuid = Column(BigInteger, comment='创建用户的唯一标识符')
    after_user_uuid = Column(BigInteger, comment='最后修改用户的唯一标识符')
    user_name = Column(String(50), comment='创建用户姓名')
    after_user_name = Column(String(50), comment='最后修改用户姓名')
    source = Column(String(50), comment='客户来源')
    status = Column(String(20), default='1', comment='客户状态')
    status_name = Column(String(50), default='正常', comment='客户状态名称')
    create_time = Column(DateTime, comment='创建时间')
    create_time_stamp = Column(BigInteger, comment='创建时间戳')
    update_time = Column(DateTime, comment='更新时间')
    create_user_name = Column(String(50), comment='创建用户姓名')
    common_status = Column(String(20), comment='通用状态')
    is_delete = Column(String(10), default='0', comment='是否删除')
    created_by = Column(String(64), nullable=False, comment='创建人')
    updated_by = Column(String(64), comment='修改人')
    created_at = Column(DateTime, nullable=False, default=datetime.now, comment='创建时间')
    updated_at = Column(DateTime, nullable=False, default=datetime.now, onupdate=datetime.now, comment='修改时间')
    headimg = Column(String(255), comment='头像')
    level = Column(Integer, default=0, comment='会员等级')
    amount = Column(DECIMAL(12, 2), comment='金额')
    passwd = Column(String(255), comment='密码')
