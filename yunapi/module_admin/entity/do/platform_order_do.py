from datetime import datetime
from sqlalchemy import Column, DateTime, String, DECIMAL, Integer, BigInteger, Text
from config.database import Base


class PlatformOrder(Base):
    """
    平台订单表
    """

    __tablename__ = 'platform_order'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='订单ID')
    order_no = Column(String(64), nullable=False, unique=True, comment='订单号')
    company_uuid = Column(String(64), nullable=False, comment='公司UUID')
    company_name = Column(String(100), nullable=False, comment='公司名称')
    business_type = Column(String(30), nullable=False, comment='业务类型')
    business_type_name = Column(String(50), nullable=False, comment='业务类型名称')
    product_type = Column(String(30), nullable=False, comment='产品类型')
    product_id = Column(String(64), nullable=True, comment='产品ID')
    product_name = Column(String(100), nullable=False, comment='产品名称')
    product_spec = Column(Text, nullable=True, comment='产品规格')
    quantity = Column(Integer, nullable=False, default=1, comment='数量')
    period_months = Column(Integer, nullable=True, comment='周期月数')
    effective_date = Column(DateTime, nullable=True, comment='生效日期')
    expire_date = Column(DateTime, nullable=True, comment='过期日期')
    unit_price = Column(DECIMAL(15, 2), nullable=False, comment='单价')
    total_amount = Column(DECIMAL(15, 2), nullable=False, comment='总金额')
    discount_amount = Column(DECIMAL(15, 2), nullable=False, default=0.00, comment='折扣金额')
    actual_amount = Column(DECIMAL(15, 2), nullable=False, comment='实际金额')
    pay_type = Column(String(30), nullable=True, comment='支付类型')
    pay_type_name = Column(String(50), nullable=True, comment='支付类型名称')
    payment_status = Column(Integer, nullable=False, default=1, comment='支付状态')
    payment_time = Column(DateTime, nullable=True, comment='支付时间')
    external_payment_id = Column(String(100), nullable=True, comment='外部支付ID')
    order_status = Column(Integer, nullable=False, default=1, comment='订单状态')
    order_status_name = Column(String(20), nullable=False, comment='订单状态名称')
    transaction_id = Column(BigInteger, nullable=True, comment='交易ID')
    parent_order_no = Column(String(64), nullable=True, comment='父订单号')
    operator_id = Column(String(64), nullable=False, comment='操作员ID')
    operator_name = Column(String(100), nullable=False, comment='操作员名称')
    operator_type = Column(String(20), nullable=False, default='MERCHANT', comment='操作员类型')
    remark = Column(Text, nullable=True, comment='备注')
    cancel_reason = Column(String(500), nullable=True, comment='取消原因')
    created_by = Column(String(64), nullable=False, comment='创建人')
    updated_by = Column(String(64), nullable=True, comment='更新人')
    created_at = Column(DateTime, nullable=False, default=datetime.now, comment='创建时间')
    updated_at = Column(DateTime, nullable=False, default=datetime.now, onupdate=datetime.now, comment='更新时间')
