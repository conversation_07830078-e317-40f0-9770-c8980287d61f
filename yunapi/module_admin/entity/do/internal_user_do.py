from datetime import datetime
from sqlalchemy import Column, DateTime, Integer, String
from config.database import Base


class InternalUser(Base):
    """
    内部用户表
    """

    __tablename__ = 'internal_user'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='用户ID')
    uuid = Column(String(64), nullable=False, comment='UUID')
    mobile = Column(String(20), nullable=False, comment='手机号')
    password = Column(String(100), comment='密码')
    number = Column(String(20), comment='编号')
    name = Column(String(50), nullable=False, comment='姓名')
    nick_name = Column(String(50), comment='昵称')
    avatar = Column(String(255), comment='头像')
    sex = Column(String(2), default='0', comment='性别（0女 1男）')
    sex_name = Column(String(10), default='女', comment='性别名称')
    company_id = Column(String(64), nullable=False, comment='公司ID')
    company_name = Column(String(100), comment='公司名称')
    store_id = Column(String(64), nullable=False, comment='门店ID')
    store_uuid = Column(String(64), comment='门店UUID')
    store_name = Column(String(100), comment='门店名称')
    role_id = Column(String(2), nullable=False, comment='角色ID')
    role_name = Column(String(20), comment='角色名称')
    sub_role_id = Column(String(2), default='0', comment='子角色ID')
    title = Column(String(50), comment='职称')
    status = Column(String(2), default='1', comment='状态（1正常 0停用）')
    status_name = Column(String(10), comment='状态名称')
    last_login_time = Column(DateTime, comment='最后登录时间')
    last_login_device = Column(String(255), comment='最后登录设备')
    last_login_ip = Column(String(20), comment='最后登录IP')
    remark = Column(String(500), comment='备注')
    jpush_registration_id = Column(String(64), comment='极光推送ID')
    mobile_system = Column(Integer, default=0, comment='移动系统')
    city_id = Column(String(20), comment='城市ID')
    city = Column(String(50), comment='城市')
    address = Column(String(255), comment='地址')
    address_desc = Column(String(255), comment='地址描述')
    lng = Column(String(20), comment='经度')
    lat = Column(String(20), comment='纬度')
    service_count = Column(Integer, default=0, comment='服务次数')
    id_number = Column(String(18), comment='身份证号')
    birthday = Column(String(20), comment='生日')
    age = Column(String(10), comment='年龄')
    is_bind_wx = Column(String(2), default='0', comment='是否绑定微信')
    create_time = Column(DateTime, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
