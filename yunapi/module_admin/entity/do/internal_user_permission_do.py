from datetime import datetime
from sqlalchemy import Column, DateTime, Integer, String, Text
from config.database import Base


class InternalUserPermission(Base):
    """
    内部用户权限表
    """

    __tablename__ = 'internal_user_permission'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='ID')
    user_id = Column(String(64), nullable=False, comment='用户ID')
    assistant = Column(String(2), default='0', comment='是否助手')
    is_private = Column(String(2), default='0', comment='是否私密')
    is_listen_tape = Column(String(2), default='0', comment='是否听录音')
    auth_pay = Column(String(2), default='1', comment='支付授权')
    is_lock = Column(String(2), default='0', comment='是否锁定')
    store_balance_auth = Column(String(2), default='1', comment='门店余额授权')
    is_collection_salary = Column(String(2), default='1', comment='是否收集薪资')
    action_ids = Column(Text, comment='操作ID')
    action_menu = Column(Text, comment='操作菜单')
    qrcode_status = Column(String(2), default='0', comment='二维码状态')
    is_view_other_resume = Column(String(2), default='1', comment='是否查看其他简历')
    is_edit_other_resume = Column(String(2), default='1', comment='是否编辑其他简历')
    is_training_course_push = Column(String(2), default='2', comment='是否培训课程推送')
    is_use_star_clue = Column(String(2), default='1', comment='是否使用星级线索')
    is_use_sign_electronic_contract = Column(String(2), default='1', comment='是否使用电子签约')
    is_edit_contract_content = Column(String(2), default='1', comment='是否编辑合同内容')
    is_delete_signed_contract = Column(String(2), default='0', comment='是否删除已签约合同')
    is_add_income_type = Column(String(2), default='1', comment='是否添加收入类型')
    is_view_store_contract = Column(String(2), default='1', comment='是否查看门店合同')
    is_use_insurance = Column(String(2), default='1', comment='是否使用保险')
    is_use_change_insurance = Column(String(2), default='1', comment='是否使用变更保险')
    is_use_balance_insurance = Column(String(2), default='1', comment='是否使用余额保险')
    is_use_physical_examination = Column(String(2), default='1', comment='是否使用体检')
    is_use_balance_examination = Column(String(2), default='1', comment='是否使用余额体检')
    is_view_examination_income = Column(String(2), default='0', comment='是否查看体检收入')
    is_use_certificate = Column(String(2), default='1', comment='是否使用证书')
    is_view_certificate_price = Column(String(2), default='1', comment='是否查看证书价格')
    is_use_balance_certificate = Column(String(2), default='1', comment='是否使用余额证书')
    is_use_train = Column(String(2), default='0', comment='是否使用培训')
    is_open_record_course = Column(String(2), default='1', comment='是否开放记录课程')
    is_add_record_course = Column(String(2), default='1', comment='是否添加记录课程')
    is_give_record_course = Column(String(2), default='1', comment='是否赠送记录课程')
    is_adjust_course_price = Column(String(2), default='1', comment='是否调整课程价格')
    is_can_record_earnings = Column(String(2), default='1', comment='是否可以记录收益')
    is_can_course_setting = Column(String(2), default='1', comment='是否可以课程设置')
    is_control_record_cost_price = Column(String(2), default='1', comment='是否控制记录成本价')
    is_ai_red_envelope = Column(String(2), default='1', comment='是否AI红包')
    is_can_earnings_manage = Column(String(2), default='1', comment='是否可以收益管理')
    is_allow_update_password = Column(String(2), default='1', comment='是否允许更新密码')
    is_use_combined_order = Column(String(2), default='1', comment='是否使用组合订单')
    is_servicer = Column(String(2), default='0', comment='是否服务人员')
    card_type = Column(String(2), default='1', comment='卡类型')
    is_can_customize_record = Column(String(2), default='0', comment='是否可以自定义记录')
    is_can_shop = Column(String(2), default='1', comment='是否可以购物')
    is_show_increment_coupon = Column(String(2), default='1', comment='是否显示增量优惠券')
    is_view_store_fund = Column(String(2), default='1', comment='是否查看门店资金')
    is_edit_website = Column(String(2), default='1', comment='是否编辑网站')
    is_use_college = Column(String(2), default='1', comment='是否使用学院')
    is_use_combined_nearby = Column(String(2), default='1', comment='是否使用组合附近')
    is_edit_gaode_product = Column(String(2), default='1', comment='是否编辑高德产品')
    is_show_bj = Column(String(2), default='1', comment='是否显示北京')
    is_show_bj_wxapp = Column(String(2), default='0', comment='是否显示北京微信小程序')
    is_export_bj_order = Column(String(2), default='1', comment='是否导出北京订单')
    is_view_home_module = Column(String(2), default='1', comment='是否查看首页模块')
    is_release_job = Column(String(2), default='1', comment='是否发布工作')
    create_time = Column(DateTime, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
