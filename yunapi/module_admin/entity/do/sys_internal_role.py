from sqlalchemy import Column, Integer, String, DateTime, Text
from sqlalchemy.orm import relationship
from config.get_db import Base


class SysInternalRole(Base):
    """内部角色表"""
    __tablename__ = 'sys_internal_role'

    role_id = Column(Integer, primary_key=True, autoincrement=True, comment='角色ID')
    parent_id = Column(Integer, default=0, comment='父角色ID，0表示一级角色')
    role_name = Column(String(30), nullable=False, comment='角色名称')
    role_key = Column(String(100), nullable=False, comment='角色权限字符串（待定）')
    order_num = Column(Integer, default=0, comment='显示顺序')
    status = Column(String(1), default='0', comment='角色状态: 0正常 1停用')
    create_time = Column(DateTime, comment='创建时间')
    update_time = Column(DateTime, comment='更新时间')
    remark = Column(String(500), comment='备注')

    # 关联关系
    menus = relationship('SysInternalMenu', secondary='sys_internal_role_menu') 