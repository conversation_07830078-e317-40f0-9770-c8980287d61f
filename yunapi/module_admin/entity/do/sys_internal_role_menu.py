from sqlalchemy import Colum<PERSON>, <PERSON>te<PERSON>, Foreign<PERSON><PERSON>
from sqlalchemy.orm import relationship
from config.get_db import Base


class SysInternalRoleMenu(Base):
    """内部角色和菜单关联表"""
    __tablename__ = 'sys_internal_role_menu'

    role_id = <PERSON>umn(Integer, ForeignKey('sys_internal_role.role_id'), primary_key=True, comment='角色ID')
    menu_id = Column(Integer, ForeignKey('sys_internal_menu.menu_id'), primary_key=True, comment='菜单ID')

    # 关联关系
    role = relationship('SysInternalRole', backref='role_menus')
    menu = relationship('SysInternalMenu', backref='menu_roles') 