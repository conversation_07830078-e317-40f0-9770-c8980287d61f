from datetime import datetime
from decimal import Decimal
from sqlalchemy import Column, DateTime, String, DECIMAL, Integer, BigInteger, Text
from config.database import Base


class CompanyWithdrawal(Base):
    """
    公司提现申请表
    """

    __tablename__ = 'company_withdrawal'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='提现申请ID')
    company_uuid = Column(String(64), nullable=False, comment='公司UUID')
    withdrawal_no = Column(String(64), nullable=False, unique=True, comment='提现申请单号')
    withdrawal_type = Column(Integer, nullable=False, comment='提现类型：1-自行开票，2-零工提现')
    withdrawal_type_name = Column(String(50), nullable=True, comment='提现类型名称')
    apply_amount = Column(DECIMAL(15, 2), nullable=False, comment='申请提现金额')
    fee_rate = Column(DECIMAL(5, 4), nullable=True, default=0.0000, comment='手续费率')
    fee_amount = Column(DECIMAL(15, 2), nullable=True, default=0.00, comment='手续费金额')
    actual_amount = Column(DECIMAL(15, 2), nullable=False, comment='实际到账金额')
    bank_name = Column(String(100), nullable=True, comment='开户银行')
    bank_account = Column(String(50), nullable=True, comment='银行账号')
    account_holder = Column(String(100), nullable=True, comment='开户人姓名')
    invoice_info = Column(Text, nullable=True, comment='开票信息（JSON格式）')
    apply_reason = Column(String(500), nullable=True, comment='申请原因')
    status = Column(String(20), nullable=False, default='PENDING', comment='申请状态：PENDING-待审核，APPROVED-已通过，REJECTED-已拒绝，PROCESSING-处理中，COMPLETED-已完成，CANCELLED-已取消')
    applicant_id = Column(String(64), nullable=False, comment='申请人ID')
    applicant_name = Column(String(100), nullable=False, comment='申请人姓名')
    apply_time = Column(DateTime, nullable=False, default=datetime.now, comment='申请时间')
    reviewer_id = Column(String(64), nullable=True, comment='审核人ID')
    reviewer_name = Column(String(100), nullable=True, comment='审核人姓名')
    review_time = Column(DateTime, nullable=True, comment='审核时间')
    review_comment = Column(String(500), nullable=True, comment='审核意见')
    processor_id = Column(String(64), nullable=True, comment='处理人ID')
    processor_name = Column(String(100), nullable=True, comment='处理人姓名')
    process_time = Column(DateTime, nullable=True, comment='处理时间')
    completion_time = Column(DateTime, nullable=True, comment='完成时间')
    transaction_id = Column(String(100), nullable=True, comment='交易流水号')
    remark = Column(Text, nullable=True, comment='备注')
    created_by = Column(String(64), nullable=False, comment='创建人')
    created_at = Column(DateTime, nullable=False, default=datetime.now, comment='创建时间')
    updated_by = Column(String(64), nullable=True, comment='更新人')
    updated_at = Column(DateTime, nullable=True, comment='更新时间')
