from datetime import datetime
from sqlalchemy import Column, DateTime, Integer, String, Text, SmallInteger, BigInteger
from config.database import Base


class Store(Base):
    """
    门店信息表
    """

    __tablename__ = 'store'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='门店ID')
    store_uuid = Column(String(255), nullable=False, comment='门店UUID')
    company_id = Column(String(255), nullable=True, comment='公司ID')
    name = Column(String(100), nullable=False, comment='门店名称')
    phone = Column(String(20), nullable=True, comment='电话')
    mobile = Column(String(20), nullable=True, comment='手机')
    address = Column(String(64), nullable=True, comment='地址')
    business_hours = Column(String(255), nullable=True, comment='营业时间')
    manager = Column(String(50), nullable=True, comment='管理员')
    status = Column(SmallInteger, nullable=True, default=1, comment='状态')
    store_status = Column(SmallInteger, nullable=True, default=1, comment='门店状态')
    remark = Column(String(255), nullable=True, comment='备注')
    introduce = Column(Text, nullable=True, comment='介绍')
    email = Column(String(100), nullable=True, comment='邮箱')
    is_new = Column(SmallInteger, nullable=True, comment='是否新店')
    level = Column(String(10), nullable=True, comment='等级')
    flag = Column(SmallInteger, nullable=True, default=0, comment='标记')
    is_show_wxapp = Column(SmallInteger, nullable=True, default=1, comment='是否在小程序显示')
    is_delete = Column(SmallInteger, nullable=True, default=0, comment='是否删除')
    created_by = Column(String(64), nullable=True, comment='创建者')
    updated_by = Column(String(64), nullable=True, comment='更新者')
    create_time = Column(DateTime, nullable=False, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, nullable=False, default=datetime.now, onupdate=datetime.now, comment='更新时间')
