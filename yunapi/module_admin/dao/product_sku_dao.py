from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from module_admin.entity.do.product_sku_do import ProductSku


class ProductSkuDao:
    """
    产品SKU数据库操作层
    """

    @classmethod
    async def get_product_sku_by_product_id(cls, db: AsyncSession, product_id: int):
        """
        根据产品ID获取产品SKU信息

        :param db: orm对象
        :param product_id: 产品ID
        :return: 产品SKU信息对象
        """
        query_product_sku_info = (
            (await db.execute(select(ProductSku).where(ProductSku.productid == product_id)))
            .scalars()
            .first()
        )

        return query_product_sku_info

    @classmethod
    async def get_product_sku_by_id(cls, db: AsyncSession, sku_id: int):
        """
        根据SKU ID获取产品SKU详细信息

        :param db: orm对象
        :param sku_id: SKU ID
        :return: 产品SKU信息对象
        """
        query_product_sku_info = (
            (await db.execute(select(ProductSku).where(ProductSku.id == sku_id)))
            .scalars()
            .first()
        )

        return query_product_sku_info
