from sqlalchemy import and_, or_, select, update, func
from sqlalchemy.ext.asyncio import AsyncSession
from module_admin.entity.do.ccuser_do import Ccuser
from module_admin.entity.vo.ccuser_vo import CcuserPageQueryModel, DeleteCcuserModel
from utils.page_util import PageUtil


class CcuserDao:
    """
    客户管理模块数据库操作层
    """

    @classmethod
    async def get_ccuser_list(cls, query_db: AsyncSession, query_object: CcuserPageQueryModel, is_page: bool = False):
        """
        根据查询参数获取客户列表信息

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 客户列表信息对象
        """
        query = select(Ccuser).where(Ccuser.is_delete == '0')
        
        # 构建查询条件
        if query_object.name:
            query = query.where(Ccuser.name.like(f'%{query_object.name}%'))
        if query_object.mobile:
            query = query.where(Ccuser.mobile.like(f'%{query_object.mobile}%'))
        if query_object.status:
            query = query.where(Ccuser.status == query_object.status)
        if query_object.source:
            query = query.where(Ccuser.source.like(f'%{query_object.source}%'))
        if query_object.store_uuid:
            query = query.where(Ccuser.store_uuid == query_object.store_uuid)
        if query_object.begin_time and query_object.end_time:
            query = query.where(
                and_(
                    Ccuser.create_time >= query_object.begin_time,
                    Ccuser.create_time <= query_object.end_time
                )
            )
        
        # 排序：优先使用create_time，如果为空则使用created_at，最后按id倒序确保稳定性
        query = query.order_by(
            func.coalesce(Ccuser.create_time, Ccuser.created_at).desc(),
            Ccuser.id.desc()
        )
        
        if is_page:
            # 分页查询
            return await PageUtil.paginate(query_db, query, query_object.page_num, query_object.page_size, True)
        else:
            # 不分页查询
            result = await query_db.execute(query)
            return result.scalars().all()

    @classmethod
    async def get_ccuser_detail_by_id(cls, query_db: AsyncSession, ccuser_id: int):
        """
        根据客户id获取客户详细信息

        :param query_db: orm对象
        :param ccuser_id: 客户id
        :return: 客户信息对象
        """
        query = select(Ccuser).where(
            and_(Ccuser.id == ccuser_id, Ccuser.is_delete == '0')
        )
        result = await query_db.execute(query)
        return result.scalars().first()

    @classmethod
    async def add_ccuser_dao(cls, query_db: AsyncSession, ccuser_data: dict):
        """
        新增客户数据库操作

        :param query_db: orm对象
        :param ccuser_data: 客户数据
        :return: 新增校验结果
        """
        new_ccuser = Ccuser(**ccuser_data)
        query_db.add(new_ccuser)
        await query_db.flush()
        return new_ccuser

    @classmethod
    async def edit_ccuser_dao(cls, query_db: AsyncSession, ccuser: Ccuser):
        """
        编辑客户数据库操作

        :param query_db: orm对象
        :param ccuser: 客户对象
        :return: 编辑校验结果
        """
        await query_db.merge(ccuser)

    @classmethod
    async def delete_ccuser_dao(cls, query_db: AsyncSession, delete_ccuser: DeleteCcuserModel):
        """
        删除客户数据库操作

        :param query_db: orm对象
        :param delete_ccuser: 删除客户对象
        :return: 删除校验结果
        """
        ccuser_ids = delete_ccuser.ids.split(',')
        query = update(Ccuser).where(Ccuser.id.in_(ccuser_ids)).values(is_delete='1')
        await query_db.execute(query)

    @classmethod
    async def get_ccuser_detail_by_uuid(cls, query_db: AsyncSession, ccuser_uuid: str):
        """
        根据客户UUID获取客户详细信息

        :param query_db: orm对象
        :param ccuser_uuid: 客户UUID
        :return: 客户信息对象
        """
        query = select(Ccuser).where(
            and_(Ccuser.uuid == ccuser_uuid, Ccuser.is_delete == '0')
        )
        result = await query_db.execute(query)
        return result.scalars().first()

    @classmethod
    async def get_ccuser_detail_by_mobile(cls, query_db: AsyncSession, mobile: str):
        """
        根据手机号获取客户详细信息

        :param query_db: orm对象
        :param mobile: 手机号
        :return: 客户信息对象
        """
        query = select(Ccuser).where(
            and_(Ccuser.mobile == mobile, Ccuser.is_delete == '0')
        )
        result = await query_db.execute(query)
        return result.scalars().first()
