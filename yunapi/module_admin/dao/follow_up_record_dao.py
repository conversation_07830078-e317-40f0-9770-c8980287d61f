from datetime import datetime, time
from sqlalchemy import delete, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from module_admin.entity.do.follow_up_record_do import FollowUpRecord
from module_admin.entity.vo.follow_up_record_vo import FollowUpRecordModel, FollowUpRecordPageQueryModel
from utils.page_util import PageUtil


class FollowUpRecordDao:
    """
    跟进记录模块数据库操作层
    """

    @classmethod
    async def get_follow_up_record_detail_by_id(cls, db: AsyncSession, id: int):
        """
        根据跟进记录id获取跟进记录详细信息

        :param db: orm对象
        :param id: 跟进记录id
        :return: 跟进记录信息对象
        """
        follow_up_record_info = (await db.execute(select(FollowUpRecord).where(FollowUpRecord.id == id))).scalars().first()

        return follow_up_record_info

    @classmethod
    async def get_follow_up_records_by_merchant_id(cls, db: AsyncSession, merchant_id: int, query_object=None, is_page: bool = False):
        """
        根据商户ID获取跟进记录列表

        :param db: orm对象
        :param merchant_id: 商户ID
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 跟进记录列表信息对象
        """
        query = select(FollowUpRecord).where(FollowUpRecord.merchant_id == merchant_id)
        
        # 如果有查询对象，添加额外的过滤条件
        if query_object:
            query = query.where(
                FollowUpRecord.follow_up_person.like(f'%{query_object.follow_up_person}%')
                if query_object.follow_up_person
                else True,
                FollowUpRecord.follow_up_time.between(
                    datetime.combine(datetime.strptime(query_object.begin_time, '%Y-%m-%d'), time(00, 00, 00)),
                    datetime.combine(datetime.strptime(query_object.end_time, '%Y-%m-%d'), time(23, 59, 59)),
                )
                if query_object.begin_time and query_object.end_time
                else True,
            )
        
        # 按跟进时间降序排序
        query = query.order_by(FollowUpRecord.follow_up_time.desc())
        
        # 分页处理
        if is_page and query_object:
            follow_up_record_list = await PageUtil.paginate(db, query, query_object.page_num, query_object.page_size, is_page)
        else:
            follow_up_record_list = (await db.execute(query)).scalars().all()

        return follow_up_record_list

    @classmethod
    async def add_follow_up_record_dao(cls, db: AsyncSession, follow_up_record: FollowUpRecordModel):
        """
        新增跟进记录数据库操作

        :param db: orm对象
        :param follow_up_record: 跟进记录对象
        :return: 新增的跟进记录对象
        """
        db_follow_up_record = FollowUpRecord(**follow_up_record.model_dump(exclude_none=True))
        db.add(db_follow_up_record)
        await db.flush()

        return db_follow_up_record

    @classmethod
    async def edit_follow_up_record_dao(cls, db: AsyncSession, follow_up_record_dict: dict):
        """
        编辑跟进记录数据库操作

        :param db: orm对象
        :param follow_up_record_dict: 跟进记录字典
        :return:
        """
        await db.execute(
            update(FollowUpRecord)
            .where(FollowUpRecord.id == follow_up_record_dict.get('id'))
            .values(**follow_up_record_dict)
        )

    @classmethod
    async def delete_follow_up_record_dao(cls, db: AsyncSession, follow_up_record: FollowUpRecordModel):
        """
        删除跟进记录数据库操作

        :param db: orm对象
        :param follow_up_record: 跟进记录对象
        :return:
        """
        await db.execute(delete(FollowUpRecord).where(FollowUpRecord.id == follow_up_record.id))
