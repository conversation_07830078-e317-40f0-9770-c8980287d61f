from sqlalchemy import and_, select, update, delete
from sqlalchemy.ext.asyncio import AsyncSession
from module_admin.entity.do.store_do import Store
from module_admin.entity.vo.store_vo import StorePageQueryModel, DeleteStoreModel
from utils.page_util import PageUtil


class StoreDao:
    """
    门店管理模块数据库访问层
    """

    @classmethod
    async def get_store_list(cls, query_db: AsyncSession, query_object: StorePageQueryModel, is_page: bool = False):
        """
        根据查询参数获取门店列表信息

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 门店列表信息对象
        """
        query = select(Store).where(Store.is_delete == 0)
        
        # 构建查询条件
        if query_object.name:
            query = query.where(Store.name.like(f'%{query_object.name}%'))
        if query_object.manager:
            query = query.where(Store.manager.like(f'%{query_object.manager}%'))
        if query_object.status is not None:
            query = query.where(Store.status == query_object.status)
        if query_object.company_id:
            query = query.where(Store.company_id == query_object.company_id)
        if query_object.begin_time and query_object.end_time:
            query = query.where(
                and_(
                    Store.create_time >= query_object.begin_time,
                    Store.create_time <= query_object.end_time
                )
            )
        
        # 排序：按ID倒序，最新的门店在前面
        query = query.order_by(Store.id.desc())
        
        if is_page:
            # 分页查询
            return await PageUtil.paginate(query_db, query, query_object.page_num, query_object.page_size, True)
        else:
            # 不分页查询
            result = await query_db.execute(query)
            return result.scalars().all()

    @classmethod
    async def get_store_detail_by_id(cls, query_db: AsyncSession, store_id: int):
        """
        根据门店id获取门店详细信息

        :param query_db: orm对象
        :param store_id: 门店id
        :return: 门店信息对象
        """
        query = select(Store).where(
            and_(Store.id == store_id, Store.is_delete == 0)
        )
        result = await query_db.execute(query)
        return result.scalars().first()

    @classmethod
    async def add_store_dao(cls, query_db: AsyncSession, store_data: dict):
        """
        新增门店数据库操作

        :param query_db: orm对象
        :param store_data: 门店数据
        :return: 新增校验结果
        """
        new_store = Store(**store_data)
        query_db.add(new_store)
        await query_db.flush()
        return new_store

    @classmethod
    async def edit_store_dao(cls, query_db: AsyncSession, store: Store):
        """
        编辑门店数据库操作

        :param query_db: orm对象
        :param store: 门店对象
        :return: 编辑校验结果
        """
        await query_db.merge(store)

    @classmethod
    async def delete_store_dao(cls, query_db: AsyncSession, delete_store: DeleteStoreModel):
        """
        删除门店数据库操作

        :param query_db: orm对象
        :param delete_store: 删除门店对象
        :return: 删除校验结果
        """
        store_ids = delete_store.ids.split(',')
        query = update(Store).where(Store.id.in_(store_ids)).values(is_delete=1)
        await query_db.execute(query)

    @classmethod
    async def get_store_detail_by_name(cls, query_db: AsyncSession, store_name: str):
        """
        根据门店名称获取门店详细信息

        :param query_db: orm对象
        :param store_name: 门店名称
        :return: 门店信息对象
        """
        query = select(Store).where(
            and_(Store.name == store_name, Store.is_delete == 0)
        )
        result = await query_db.execute(query)
        return result.scalars().first()

    @classmethod
    async def get_store_detail_by_uuid(cls, query_db: AsyncSession, store_uuid: str):
        """
        根据门店UUID获取门店详细信息

        :param query_db: orm对象
        :param store_uuid: 门店UUID
        :return: 门店信息对象
        """
        query = select(Store).where(
            and_(Store.store_uuid == store_uuid, Store.is_delete == 0)
        )
        result = await query_db.execute(query)
        return result.scalars().first()
