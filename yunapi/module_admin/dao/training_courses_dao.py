from datetime import datetime, time
from sqlalchemy import and_, select, update, delete, func, desc
from sqlalchemy.ext.asyncio import AsyncSession
from module_admin.entity.do.training_courses_do import TrainingCourses
from module_admin.entity.do.training_categories_do import TrainingCategories
from module_admin.entity.vo.training_courses_vo import TrainingCoursesPageQueryModel, DeleteTrainingCoursesModel
from utils.page_util import PageUtil
import re


class TrainingCoursesDao:
    """
    培训课程模块数据库操作层
    """

    @classmethod
    async def get_training_courses_list(cls, query_db: AsyncSession, query_object: TrainingCoursesPageQueryModel, is_page: bool = False):
        """
        根据查询参数获取培训课程列表信息

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 培训课程列表信息对象
        """
        # 联表查询，获取分类名称 - 使用id关联
        query = select(
            TrainingCourses,
            TrainingCategories.name.label('category_name')
        ).outerjoin(
            TrainingCategories,
            TrainingCourses.category_id == TrainingCategories.id
        )
        
        # 构建查询条件
        if query_object.title:
            query = query.where(TrainingCourses.title.like(f'%{query_object.title}%'))
        if query_object.category_id:
            query = query.where(TrainingCourses.category_id == query_object.category_id)
        if query_object.content_type is not None:
            query = query.where(TrainingCourses.content_type == query_object.content_type)
        if query_object.status is not None:
            query = query.where(TrainingCourses.status == query_object.status)
        if query_object.is_featured is not None:
            query = query.where(TrainingCourses.is_featured == query_object.is_featured)
        if query_object.creator_name:
            query = query.where(TrainingCourses.creator_name.like(f'%{query_object.creator_name}%'))
        if query_object.begin_time and query_object.end_time:
            query = query.where(
                and_(
                    TrainingCourses.create_time >= datetime.combine(datetime.strptime(query_object.begin_time, '%Y-%m-%d'), time(0, 0, 0)),
                    TrainingCourses.create_time <= datetime.combine(datetime.strptime(query_object.end_time, '%Y-%m-%d'), time(23, 59, 59))
                )
            )
        
        # 排序：按排序权重升序，然后按ID倒序
        query = query.order_by(TrainingCourses.sort_order.asc(), TrainingCourses.id.desc())
        
        if is_page:
            # 手动分页查询，确保返回正确的对象格式
            from sqlalchemy import func
            import math
            from utils.page_util import PageResponseModel
            from utils.common_util import CamelCaseUtil

            # 获取总数
            count_query = select(func.count()).select_from(query.subquery())
            total = (await query_db.execute(count_query)).scalar()

            # 分页查询
            offset = (query_object.page_num - 1) * query_object.page_size
            paginated_query = query.offset(offset).limit(query_object.page_size)
            result = await query_db.execute(paginated_query)

            # 处理联表查询结果
            query_result = result.fetchall()

            print("=== DAO层调试信息 ===")
            print(f"查询到的结果数量: {len(query_result)}")
            print(f"第一个结果: {query_result[0] if query_result else None}")

            courses_data = []
            for row in query_result:
                course = row[0]  # TrainingCourses对象
                category_name = row[1] if len(row) > 1 else None  # 分类名称

                print(f"课程ID: {course.id}, 分类ID: {course.category_id}, 分类名称: {category_name}")

                course_dict = {
                    'id': course.id,
                    'uuid': course.uuid,
                    'title': course.title,
                    'description': course.description,
                    'coverImage': course.cover_image,
                    'categoryId': course.category_id,
                    'categoryName': category_name,  # 添加分类名称
                    'contentType': course.content_type,
                    'contentData': course.content_data,
                    'duration': course.duration,
                    'views': course.views,
                    'sortOrder': course.sort_order,
                    'isFeatured': course.is_featured,
                    'status': course.status,
                    'creatorId': course.creator_id,
                    'creatorName': course.creator_name,
                    'createTime': course.create_time,
                    'updateTime': course.update_time
                }
                courses_data.append(course_dict)

            print(f"转换后的第一条数据: {courses_data[0] if courses_data else None}")
            print("====================")

            has_next = math.ceil(total / query_object.page_size) > query_object.page_num

            return PageResponseModel(
                rows=courses_data,
                pageNum=query_object.page_num,
                pageSize=query_object.page_size,
                total=total,
                hasNext=has_next,
            )
        else:
            # 不分页查询
            result = await query_db.execute(query)
            return result.scalars().all()

    @classmethod
    async def get_training_courses_detail_by_id(cls, query_db: AsyncSession, course_id: str):
        """
        根据课程id获取培训课程详细信息

        :param query_db: orm对象
        :param course_id: 课程id
        :return: 培训课程信息对象
        """
        query = select(TrainingCourses).where(TrainingCourses.id == course_id)
        result = await query_db.execute(query)
        return result.scalars().first()

    @classmethod
    async def get_training_courses_detail_with_category_by_id(cls, query_db: AsyncSession, course_id: str):
        """
        根据课程id获取培训课程详细信息（包含分类名称）

        :param query_db: orm对象
        :param course_id: 课程id
        :return: 培训课程信息字典（包含分类名称）
        """
        # 联表查询，获取分类名称
        query = select(
            TrainingCourses,
            TrainingCategories.name.label('category_name')
        ).outerjoin(
            TrainingCategories,
            TrainingCourses.category_id == TrainingCategories.id
        ).where(TrainingCourses.id == course_id)

        result = await query_db.execute(query)
        row = result.first()

        if row:
            course = row[0]  # TrainingCourses对象
            category_name = row[1] if len(row) > 1 else None  # 分类名称

            # 转换为字典格式，包含分类名称
            course_dict = {
                'id': course.id,
                'uuid': course.uuid,
                'title': course.title,
                'description': course.description,
                'coverImage': course.cover_image,
                'categoryId': course.category_id,
                'categoryName': category_name,  # 添加分类名称
                'contentType': course.content_type,
                'contentData': course.content_data,
                'duration': course.duration,
                'views': course.views,
                'sortOrder': course.sort_order,
                'isFeatured': course.is_featured,
                'status': course.status,
                'creatorId': course.creator_id,
                'creatorName': course.creator_name,
                'createTime': course.create_time,
                'updateTime': course.update_time
            }
            return course_dict

        return None

    @classmethod
    async def add_training_courses_dao(cls, query_db: AsyncSession, course_data: dict):
        """
        新增培训课程数据库操作

        :param query_db: orm对象
        :param course_data: 课程数据
        :return: 新增校验结果
        """
        new_course = TrainingCourses(**course_data)
        query_db.add(new_course)
        await query_db.flush()
        return new_course

    @classmethod
    async def edit_training_courses_dao(cls, query_db: AsyncSession, course: TrainingCourses):
        """
        编辑培训课程数据库操作

        :param query_db: orm对象
        :param course: 课程对象
        :return: 编辑校验结果
        """
        await query_db.merge(course)

    @classmethod
    async def delete_training_courses_dao(cls, query_db: AsyncSession, delete_course: DeleteTrainingCoursesModel):
        """
        删除培训课程数据库操作

        :param query_db: orm对象
        :param delete_course: 删除课程对象
        :return: 删除校验结果
        """
        course_ids = delete_course.ids.split(',')
        query = delete(TrainingCourses).where(TrainingCourses.id.in_(course_ids))
        await query_db.execute(query)

    @classmethod
    async def get_training_courses_detail_by_title(cls, query_db: AsyncSession, course_title: str, exclude_id: str = None):
        """
        根据课程标题获取培训课程详细信息

        :param query_db: orm对象
        :param course_title: 课程标题
        :param exclude_id: 排除的课程ID（用于编辑时检查重名）
        :return: 培训课程信息对象
        """
        query = select(TrainingCourses).where(TrainingCourses.title == course_title)
        if exclude_id:
            query = query.where(TrainingCourses.id != exclude_id)
        result = await query_db.execute(query)
        return result.scalars().first()

    @classmethod
    async def increment_views(cls, query_db: AsyncSession, course_id: str):
        """
        增加课程浏览次数

        :param query_db: orm对象
        :param course_id: 课程ID
        """
        query = update(TrainingCourses).where(TrainingCourses.id == course_id).values(
            views=TrainingCourses.views + 1
        )
        await query_db.execute(query)

    @classmethod
    async def get_next_course_id(cls, query_db: AsyncSession):
        """
        获取下一个课程ID

        :param query_db: orm对象
        :return: 下一个课程ID
        """
        # 查询所有以course_开头的ID，按数字部分降序排列
        query = select(TrainingCourses.id).where(TrainingCourses.id.like('course_%')).order_by(desc(TrainingCourses.id))
        result = await query_db.execute(query)
        course_ids = result.scalars().all()

        if not course_ids:
            return "course_001"

        # 提取数字部分并找到最大值
        max_num = 0
        for course_id in course_ids:
            match = re.search(r'course_(\d+)', course_id)
            if match:
                num = int(match.group(1))
                max_num = max(max_num, num)

        # 返回下一个ID
        next_num = max_num + 1
        return f"course_{next_num:03d}"

    @classmethod
    async def get_next_course_uuid(cls, query_db: AsyncSession):
        """
        获取下一个课程UUID

        :param query_db: orm对象
        :return: 下一个课程UUID
        """
        # 查询所有以course_uuid_开头的UUID，按数字部分降序排列
        query = select(TrainingCourses.uuid).where(TrainingCourses.uuid.like('course_uuid_%')).order_by(desc(TrainingCourses.uuid))
        result = await query_db.execute(query)
        course_uuids = result.scalars().all()

        if not course_uuids:
            return "course_uuid_001"

        # 提取数字部分并找到最大值
        max_num = 0
        for course_uuid in course_uuids:
            match = re.search(r'course_uuid_(\d+)', course_uuid)
            if match:
                num = int(match.group(1))
                max_num = max(max_num, num)

        # 返回下一个UUID
        next_num = max_num + 1
        return f"course_uuid_{next_num:03d}"
