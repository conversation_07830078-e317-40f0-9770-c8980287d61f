from sqlalchemy import and_, select, update, delete, func, desc
from sqlalchemy.ext.asyncio import AsyncSession
from module_admin.entity.do.training_categories_do import TrainingCategories
from module_admin.entity.do.training_courses_do import TrainingCourses
from module_admin.entity.vo.training_categories_vo import TrainingCategoriesPageQueryModel, DeleteTrainingCategoriesModel
from utils.page_util import PageUtil
import re


class TrainingCategoriesDao:
    """
    培训分类模块数据库操作层
    """

    @classmethod
    async def get_training_categories_list(cls, query_db: AsyncSession, query_object: TrainingCategoriesPageQueryModel, is_page: bool = False):
        """
        根据查询参数获取培训分类列表信息

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 培训分类列表信息对象
        """
        query = select(TrainingCategories)
        
        # 构建查询条件
        if query_object.name:
            query = query.where(TrainingCategories.name.like(f'%{query_object.name}%'))
        if query_object.status is not None:
            query = query.where(TrainingCategories.status == query_object.status)
        if query_object.parent_id:
            query = query.where(TrainingCategories.parent_id == query_object.parent_id)
        
        # 排序：按排序权重升序，然后按ID倒序
        query = query.order_by(TrainingCategories.sort_order.asc(), TrainingCategories.id.desc())
        
        if is_page:
            # 分页查询
            return await PageUtil.paginate(query_db, query, query_object.page_num, query_object.page_size, True)
        else:
            # 不分页查询
            result = await query_db.execute(query)
            return result.scalars().all()

    @classmethod
    async def get_training_categories_detail_by_id(cls, query_db: AsyncSession, category_id: str):
        """
        根据分类id获取培训分类详细信息

        :param query_db: orm对象
        :param category_id: 分类id
        :return: 培训分类信息对象
        """
        query = select(TrainingCategories).where(TrainingCategories.id == category_id)
        result = await query_db.execute(query)
        return result.scalars().first()

    @classmethod
    async def add_training_categories_dao(cls, query_db: AsyncSession, category_data: dict):
        """
        新增培训分类数据库操作

        :param query_db: orm对象
        :param category_data: 分类数据
        :return: 新增校验结果
        """
        new_category = TrainingCategories(**category_data)
        query_db.add(new_category)
        await query_db.flush()
        return new_category

    @classmethod
    async def edit_training_categories_dao(cls, query_db: AsyncSession, category: TrainingCategories):
        """
        编辑培训分类数据库操作

        :param query_db: orm对象
        :param category: 分类对象
        :return: 编辑校验结果
        """
        await query_db.merge(category)

    @classmethod
    async def delete_training_categories_dao(cls, query_db: AsyncSession, delete_category: DeleteTrainingCategoriesModel):
        """
        删除培训分类数据库操作

        :param query_db: orm对象
        :param delete_category: 删除分类对象
        :return: 删除校验结果
        """
        category_ids = delete_category.ids.split(',')
        query = delete(TrainingCategories).where(TrainingCategories.id.in_(category_ids))
        await query_db.execute(query)

    @classmethod
    async def get_training_categories_detail_by_name(cls, query_db: AsyncSession, category_name: str, exclude_id: str = None):
        """
        根据分类名称获取培训分类详细信息

        :param query_db: orm对象
        :param category_name: 分类名称
        :param exclude_id: 排除的分类ID（用于编辑时检查重名）
        :return: 培训分类信息对象
        """
        query = select(TrainingCategories).where(TrainingCategories.name == category_name)
        if exclude_id:
            query = query.where(TrainingCategories.id != exclude_id)
        result = await query_db.execute(query)
        return result.scalars().first()

    @classmethod
    async def get_children_categories(cls, query_db: AsyncSession, parent_id: str):
        """
        获取指定父分类下的子分类

        :param query_db: orm对象
        :param parent_id: 父分类ID
        :return: 子分类列表
        """
        query = select(TrainingCategories).where(TrainingCategories.parent_id == parent_id)
        result = await query_db.execute(query)
        return result.scalars().all()

    @classmethod
    async def get_all_categories_for_tree(cls, query_db: AsyncSession):
        """
        获取所有分类用于构建树形结构

        :param query_db: orm对象
        :return: 所有分类列表
        """
        query = select(TrainingCategories).where(TrainingCategories.status == 1).order_by(
            TrainingCategories.sort_order.asc(),
            TrainingCategories.id.asc()
        )
        result = await query_db.execute(query)
        return result.scalars().all()

    @classmethod
    async def get_courses_count_by_category(cls, query_db: AsyncSession, category_id: str):
        """
        获取指定分类下的课程数量

        :param query_db: orm对象
        :param category_id: 分类ID
        :return: 课程数量
        """
        query = select(func.count(TrainingCourses.id)).where(TrainingCourses.category_id == category_id)
        result = await query_db.execute(query)
        return result.scalar() or 0

    @classmethod
    async def get_next_category_id(cls, query_db: AsyncSession):
        """
        获取下一个分类ID

        :param query_db: orm对象
        :return: 下一个分类ID
        """
        # 查询所有以cat_开头的ID，按数字部分降序排列
        query = select(TrainingCategories.id).where(TrainingCategories.id.like('cat_%')).order_by(desc(TrainingCategories.id))
        result = await query_db.execute(query)
        category_ids = result.scalars().all()

        if not category_ids:
            return "cat_001"

        # 提取数字部分并找到最大值
        max_num = 0
        for category_id in category_ids:
            match = re.search(r'cat_(\d+)', category_id)
            if match:
                num = int(match.group(1))
                max_num = max(max_num, num)

        # 返回下一个ID
        next_num = max_num + 1
        return f"cat_{next_num:03d}"

    @classmethod
    async def get_next_category_uuid(cls, query_db: AsyncSession):
        """
        获取下一个分类UUID

        :param query_db: orm对象
        :return: 下一个分类UUID
        """
        # 查询所有以cat_uuid_开头的UUID，按数字部分降序排列
        query = select(TrainingCategories.uuid).where(TrainingCategories.uuid.like('cat_uuid_%')).order_by(desc(TrainingCategories.uuid))
        result = await query_db.execute(query)
        category_uuids = result.scalars().all()

        if not category_uuids:
            return "cat_uuid_001"

        # 提取数字部分并找到最大值
        max_num = 0
        for category_uuid in category_uuids:
            match = re.search(r'cat_uuid_(\d+)', category_uuid)
            if match:
                num = int(match.group(1))
                max_num = max(max_num, num)

        # 返回下一个UUID
        next_num = max_num + 1
        return f"cat_uuid_{next_num:03d}"
