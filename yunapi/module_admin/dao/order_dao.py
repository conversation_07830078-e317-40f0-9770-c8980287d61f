from datetime import datetime, time
from sqlalchemy import delete, desc, select, update, distinct, func
from sqlalchemy.ext.asyncio import AsyncSession
from module_admin.entity.do.order_do import Order
from module_admin.entity.do.order_waiter_do import OrderWaiter
from module_admin.entity.do.ccuser_do import Ccuser
from module_admin.entity.vo.order_vo import OrderModel, OrderPageQueryModel
from utils.page_util import PageUtil


class OrderDao:
    """
    订单管理模块数据库操作层
    """



    @classmethod
    async def get_order_list(
        cls, db: AsyncSession, query_object: OrderPageQueryModel, is_page: bool = False
    ):
        """
        根据查询参数获取订单列表信息

        :param db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 订单列表信息对象
        """
        # 构建查询条件列表
        conditions = []

        if query_object.id is not None:
            conditions.append(Order.id == query_object.id)
        if query_object.order_number:
            conditions.append(Order.order_number.like(f'%{query_object.order_number}%'))
        if query_object.serve_number:
            conditions.append(Order.serve_number.like(f'%{query_object.serve_number}%'))
        # 手机号搜索条件将在后面的关联查询中处理
        if query_object.store_id is not None:
            conditions.append(Order.store_id == query_object.store_id)
        if query_object.store_name:
            conditions.append(Order.store_name.like(f'%{query_object.store_name}%'))
        if query_object.product_name:
            conditions.append(Order.product_name.like(f'%{query_object.product_name}%'))
        if query_object.order_status is not None:
            conditions.append(Order.order_status_name == query_object.order_status)
        if query_object.service_type is not None:
            conditions.append(Order.service_type == query_object.service_type)
        if query_object.source:
            conditions.append(Order.source.like(f'%{query_object.source}%'))
        if query_object.begin_time and query_object.end_time:
            conditions.append(Order.create_time.between(
                datetime.combine(datetime.strptime(query_object.begin_time, '%Y-%m-%d'), time(00, 00, 00)),
                datetime.combine(datetime.strptime(query_object.end_time, '%Y-%m-%d'), time(23, 59, 59)),
            ))
        if query_object.service_address:
            conditions.append(Order.service_address.like(f'%{query_object.service_address}%'))

        # 先使用原来的查询逻辑，但添加手机号搜索支持
        if query_object.mobile:
            # 如果有手机号搜索，需要子查询获取匹配的user_id
            mobile_subquery = select(Ccuser.id).where(Ccuser.mobile.like(f'%{query_object.mobile}%'))
            conditions.append(Order.user_id.in_(mobile_subquery))

        # 如果有服务人员筛选条件，需要关联查询
        if query_object.service_name:
            query = (
                select(Order)
                .join(OrderWaiter, Order.order_number == OrderWaiter.order_number)
                .where(
                    *conditions,
                    OrderWaiter.service_name.like(f'%{query_object.service_name}%')
                )
                .order_by(desc(Order.create_time))
                .distinct()
            )
        else:
            if conditions:
                query = (
                    select(Order)
                    .where(*conditions)
                    .order_by(desc(Order.create_time))
                    .distinct()
                )
            else:
                query = (
                    select(Order)
                    .order_by(desc(Order.create_time))
                    .distinct()
                )

        # 使用原来的PageUtil方式
        order_list = await PageUtil.paginate(db, query, query_object.page_num, query_object.page_size, is_page)

        # 为每个订单添加手机号信息
        if is_page:
            # 分页结果
            for order_dict in order_list.rows:
                if 'userId' in order_dict and order_dict['userId']:
                    # 查询对应的手机号
                    mobile_query = select(Ccuser.mobile).where(Ccuser.id == order_dict['userId'])
                    mobile_result = await db.execute(mobile_query)
                    mobile = mobile_result.scalar()
                    order_dict['mobile'] = mobile
                else:
                    order_dict['mobile'] = None
        else:
            # 非分页结果
            for order_dict in order_list:
                if 'userId' in order_dict and order_dict['userId']:
                    # 查询对应的手机号
                    mobile_query = select(Ccuser.mobile).where(Ccuser.id == order_dict['userId'])
                    mobile_result = await db.execute(mobile_query)
                    mobile = mobile_result.scalar()
                    order_dict['mobile'] = mobile
                else:
                    order_dict['mobile'] = None

        return order_list

    @classmethod
    async def get_store_names_from_orders(cls, db: AsyncSession):
        """
        获取所有存在订单记录的门店名称列表（去重）

        :param db: orm对象
        :return: 门店名称列表
        """
        query = (
            select(distinct(Order.store_name))
            .where(Order.store_name.isnot(None), Order.store_name != '')
            .order_by(Order.store_name)
        )

        result = await db.execute(query)
        store_names = result.scalars().all()

        # 过滤掉None和空字符串，返回有效的门店名称列表
        return [name for name in store_names if name and name.strip()]

    @classmethod
    async def get_order_statistics(cls, db: AsyncSession, query_object: OrderPageQueryModel):
        """
        根据查询参数获取订单统计信息

        :param db: orm对象
        :param query_object: 查询参数对象
        :return: 统计信息字典 {'total_count': 总数量, 'total_amount': 总金额}
        """
        # 构建查询条件列表（与get_order_list方法保持一致）
        conditions = []

        if query_object.id is not None:
            conditions.append(Order.id == query_object.id)
        if query_object.order_number:
            conditions.append(Order.order_number.like(f'%{query_object.order_number}%'))
        if query_object.serve_number:
            conditions.append(Order.serve_number.like(f'%{query_object.serve_number}%'))
        if query_object.store_id is not None:
            conditions.append(Order.store_id == query_object.store_id)
        if query_object.store_name:
            conditions.append(Order.store_name.like(f'%{query_object.store_name}%'))
        if query_object.product_name:
            conditions.append(Order.product_name.like(f'%{query_object.product_name}%'))
        if query_object.order_status is not None:
            conditions.append(Order.order_status_name == query_object.order_status)
        if query_object.service_type is not None:
            conditions.append(Order.service_type == query_object.service_type)
        if query_object.source:
            conditions.append(Order.source.like(f'%{query_object.source}%'))
        if query_object.begin_time and query_object.end_time:
            conditions.append(Order.create_time.between(
                datetime.combine(datetime.strptime(query_object.begin_time, '%Y-%m-%d'), time(00, 00, 00)),
                datetime.combine(datetime.strptime(query_object.end_time, '%Y-%m-%d'), time(23, 59, 59)),
            ))
        if query_object.service_address:
            conditions.append(Order.service_address.like(f'%{query_object.service_address}%'))

        # 手机号搜索条件
        if query_object.mobile:
            mobile_subquery = select(Ccuser.id).where(Ccuser.mobile.like(f'%{query_object.mobile}%'))
            conditions.append(Order.user_id.in_(mobile_subquery))

        # 如果有服务人员筛选条件，需要关联查询
        if query_object.service_name:
            # 使用子查询获取符合服务人员条件的订单编号
            service_subquery = (
                select(OrderWaiter.order_number)
                .where(OrderWaiter.service_name.like(f'%{query_object.service_name}%'))
            )
            conditions.append(Order.order_number.in_(service_subquery))

        # 构建统计查询
        if conditions:
            stats_query = (
                select(
                    func.count(Order.id).label('total_count'),
                    func.coalesce(func.sum(Order.pay_actual), 0).label('total_amount')
                )
                .where(*conditions)
            )
        else:
            stats_query = (
                select(
                    func.count(Order.id).label('total_count'),
                    func.coalesce(func.sum(Order.pay_actual), 0).label('total_amount')
                )
            )

        result = await db.execute(stats_query)
        stats = result.first()

        return {
            'total_count': stats.total_count if stats else 0,
            'total_amount': float(stats.total_amount) if stats and stats.total_amount else 0.0
        }

    @classmethod
    async def get_order_detail_by_id(cls, db: AsyncSession, order_id: int):
        """
        根据订单id获取订单详细信息

        :param db: orm对象
        :param order_id: 订单id
        :return: 订单信息对象
        """
        query_order_info = (
            (await db.execute(select(Order).where(Order.id == order_id).distinct()))
            .scalars()
            .first()
        )

        # 如果找到订单，添加手机号信息
        if query_order_info and query_order_info.user_id:
            mobile_query = select(Ccuser.mobile).where(Ccuser.id == query_order_info.user_id)
            mobile_result = await db.execute(mobile_query)
            mobile = mobile_result.scalar()
            # 为订单对象添加mobile属性
            query_order_info.mobile = mobile
        elif query_order_info:
            query_order_info.mobile = None

        return query_order_info

    @classmethod
    async def get_order_by_order_number(cls, db: AsyncSession, order_number: str):
        """
        根据订单编号获取订单信息

        :param db: orm对象
        :param order_number: 订单编号
        :return: 订单信息对象
        """
        query_order_info = (
            (await db.execute(select(Order).where(Order.order_number == order_number).distinct()))
            .scalars()
            .first()
        )

        return query_order_info

    @classmethod
    async def add_order_dao(cls, db: AsyncSession, order: OrderModel):
        """
        新增订单数据库操作

        :param db: orm对象
        :param order: 订单对象
        :return: 新增的订单对象
        """
        db_order = Order(**order.model_dump(exclude_unset=True))
        db.add(db_order)
        await db.flush()

        return db_order

    @classmethod
    async def edit_order_dao(cls, db: AsyncSession, order: dict):
        """
        编辑订单数据库操作

        :param db: orm对象
        :param order: 需要更新的订单字典
        :return: 编辑校验结果
        """
        await db.execute(update(Order), [order])

    @classmethod
    async def cancel_order_dao(cls, db: AsyncSession, order_ids: list):
        """
        取消订单数据库操作

        :param db: orm对象
        :param order_ids: 订单id列表
        :return:
        """
        await db.execute(
            update(Order)
            .where(Order.id.in_(order_ids))
            .values(order_status=99, order_status_name='已取消')
        )

    @classmethod
    async def get_order_by_info(cls, db: AsyncSession, order: OrderModel):
        """
        根据订单参数获取订单信息

        :param db: orm对象
        :param order: 订单参数
        :return: 当前订单参数的订单信息对象
        """
        conditions = []
        if order.order_number:
            conditions.append(Order.order_number == order.order_number)
        if order.user_id:
            conditions.append(Order.user_id == order.user_id)
        if order.store_id:
            conditions.append(Order.store_id == order.store_id)

        if conditions:
            query_order_info = (
                (
                    await db.execute(
                        select(Order)
                        .where(*conditions)
                        .order_by(desc(Order.create_time))
                        .distinct()
                    )
                )
                .scalars()
                .first()
            )
        else:
            query_order_info = (
                (
                    await db.execute(
                        select(Order)
                        .order_by(desc(Order.create_time))
                        .distinct()
                    )
                )
                .scalars()
                .first()
            )

        return query_order_info

    @classmethod
    async def update_order_service_time(cls, db: AsyncSession, order_number: str, service_date: str, service_hour: str):
        """
        更新订单服务时间

        :param db: orm对象
        :param order_number: 订单编号
        :param service_date: 服务时间
        :param service_hour: 服务小时
        :return: 更新结果
        """
        await db.execute(
            update(Order)
            .where(Order.order_number == order_number)
            .values(service_date=service_date, service_hour=service_hour)
        )
