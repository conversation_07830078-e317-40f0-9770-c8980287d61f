from sqlalchemy import select, and_
from sqlalchemy.ext.asyncio import AsyncSession
from module_admin.entity.do.service_product_do import ServiceProduct


class ServiceProductDao:
    """
    服务产品关联数据库操作层
    """

    @classmethod
    async def get_staff_by_product_and_staff_ids(cls, db: AsyncSession, product_id: int, staff_ids: list):
        """
        根据产品ID和员工ID列表获取支持该产品的员工ID列表

        :param db: orm对象
        :param product_id: 产品ID
        :param staff_ids: 员工ID列表
        :return: 支持该产品的员工ID列表
        """
        if not staff_ids:
            return []
            
        query_result = (
            await db.execute(
                select(ServiceProduct.staff_id)
                .where(
                    and_(
                        ServiceProduct.productid == product_id,
                        ServiceProduct.staff_id.in_(staff_ids)
                    )
                )
            )
        ).scalars().all()

        return list(query_result)
