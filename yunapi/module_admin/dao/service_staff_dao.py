from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from module_admin.entity.do.service_staff_do import ServiceStaff


class ServiceStaffDao:
    """
    服务员工数据库操作层
    """

    @classmethod
    async def get_staff_by_store_uuid(cls, db: AsyncSession, store_uuid: str):
        """
        根据门店UUID获取员工列表

        :param db: orm对象
        :param store_uuid: 门店UUID
        :return: 员工列表
        """
        query_staff_info = (
            await db.execute(
                select(ServiceStaff)
                .where(ServiceStaff.store_uuid == store_uuid, ServiceStaff.status == '1')
                .order_by(ServiceStaff.user_name)
            )
        ).scalars().all()

        return query_staff_info

    @classmethod
    async def get_staff_by_id(cls, db: AsyncSession, staff_id: int):
        """
        根据员工ID获取员工信息

        :param db: orm对象
        :param staff_id: 员工ID
        :return: 员工信息
        """
        query_staff_info = (
            await db.execute(
                select(ServiceStaff)
                .where(ServiceStaff.id == staff_id)
            )
        ).scalars().first()

        return query_staff_info
