import io
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
from openpyxl import Workbook
from openpyxl.styles import Alignment, PatternFill, Font
from openpyxl.utils import get_column_letter
from config.constant import CommonConstant
from exceptions.exception import ServiceException
from module_admin.dao.order_dao import OrderDao
from module_admin.dao.order_waiter_dao import OrderWaiterDao
from module_admin.dao.product_sku_dao import ProductSkuDao
from module_admin.dao.service_staff_dao import ServiceStaffDao
from module_admin.dao.service_product_dao import ServiceProductDao
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.vo.order_vo import (
    AddOrderModel,
    DeleteOrderModel,
    EditOrderModel,
    OrderModel,
    OrderPageQueryModel,
)
from module_admin.service.common_service import CommonService
from utils.common_util import CamelCaseUtil
from utils.excel_util import ExcelUtil
from utils.page_util import PageResponseModel


class OrderService:
    """
    订单管理模块服务层
    """

    @classmethod
    async def get_order_list_services(
        cls, query_db: AsyncSession, query_object: OrderPageQueryModel, is_page: bool = False
    ):
        """
        获取订单列表信息service

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 订单列表信息对象
        """
        # 添加调试日志
        print(f"查询参数: order_status={query_object.order_status}, type={type(query_object.order_status)}")

        order_list_result = await OrderDao.get_order_list(query_db, query_object, is_page)

        # 处理订单来源显示
        if hasattr(order_list_result, 'rows'):
            # 分页结果
            processed_rows = []
            for order in order_list_result.rows:
                # 检查是否是SQLAlchemy对象还是字典
                if hasattr(order, '__dict__'):
                    order_dict = CamelCaseUtil.transform_result(order.__dict__)
                else:
                    order_dict = CamelCaseUtil.transform_result(order)

                # 处理订单来源显示
                if order_dict.get('source') == 'client':
                    order_dict['source'] = '门店端'
                elif order_dict.get('source') == 'proxy':
                    order_dict['source'] = '代客下单'
                processed_rows.append(order_dict)
            order_list_result.rows = processed_rows
        else:
            # 非分页结果
            processed_orders = []
            for order in order_list_result:
                # 检查是否是SQLAlchemy对象还是字典
                if hasattr(order, '__dict__'):
                    order_dict = CamelCaseUtil.transform_result(order.__dict__)
                else:
                    order_dict = CamelCaseUtil.transform_result(order)

                # 处理订单来源显示
                if order_dict.get('source') == 'client':
                    order_dict['source'] = '门店端'
                elif order_dict.get('source') == 'proxy':
                    order_dict['source'] = '代客下单'
                processed_orders.append(order_dict)
            order_list_result = processed_orders

        # 添加返回结果调试
        if hasattr(order_list_result, 'total'):
            print(f"分页查询结果: total={order_list_result.total}, rows_count={len(order_list_result.rows) if order_list_result.rows else 0}")
        else:
            print(f"非分页查询结果: count={len(order_list_result) if order_list_result else 0}")

        # 获取统计信息
        statistics = await OrderDao.get_order_statistics(query_db, query_object)

        # 将统计信息添加到返回结果中
        if hasattr(order_list_result, 'total'):
            # 分页结果，添加统计信息
            order_list_result.statistics = statistics
            return order_list_result
        else:
            # 非分页结果，直接返回列表（用于导出等功能）
            return order_list_result

    @classmethod
    async def get_store_names_services(cls, query_db: AsyncSession):
        """
        获取门店名称列表service

        :param query_db: orm对象
        :return: 门店名称列表
        """
        store_names = await OrderDao.get_store_names_from_orders(query_db)
        return store_names

    @classmethod
    async def get_order_statistics_services(cls, query_db: AsyncSession, query_object: OrderPageQueryModel):
        """
        获取订单统计信息service

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :return: 统计信息
        """
        statistics = await OrderDao.get_order_statistics(query_db, query_object)
        return statistics

    @classmethod
    async def check_order_number_unique_services(cls, query_db: AsyncSession, page_object: OrderModel):
        """
        校验订单编号是否唯一service

        :param query_db: orm对象
        :param page_object: 订单对象
        :return: 校验结果
        """
        order_id = -1 if page_object.id is None else page_object.id
        order = await OrderDao.get_order_by_order_number(query_db, page_object.order_number)
        if order and order.id != order_id:
            return CommonConstant.NOT_UNIQUE
        return CommonConstant.UNIQUE

    @classmethod
    async def add_order_services(cls, query_db: AsyncSession, page_object: AddOrderModel):
        """
        新增订单信息service

        :param query_db: orm对象
        :param page_object: 新增订单对象
        :return: 新增订单校验结果
        """
        add_order = OrderModel(**page_object.model_dump(by_alias=True))
        if not await cls.check_order_number_unique_services(query_db, add_order):
            raise ServiceException(message=f'新增订单{page_object.order_number}失败，订单编号已存在')
        else:
            try:
                await OrderDao.add_order_dao(query_db, add_order)
                await query_db.commit()
                return CrudResponseModel(is_success=True, message='新增成功')
            except Exception as e:
                await query_db.rollback()
                raise e

    @classmethod
    async def edit_order_services(cls, query_db: AsyncSession, page_object: EditOrderModel):
        """
        编辑订单信息service

        :param query_db: orm对象
        :param page_object: 编辑订单对象
        :return: 编辑订单校验结果
        """
        edit_order = page_object.model_dump(exclude_unset=True)
        order_info = await cls.order_detail_services(query_db, page_object.id)
        if order_info and order_info.get('id'):
            if not await cls.check_order_number_unique_services(query_db, page_object):
                raise ServiceException(message=f'修改订单{page_object.order_number}失败，订单编号已存在')
            else:
                try:
                    await OrderDao.edit_order_dao(query_db, edit_order)
                    await query_db.commit()
                    return CrudResponseModel(is_success=True, message='更新成功')
                except Exception as e:
                    await query_db.rollback()
                    raise e
        else:
            raise ServiceException(message='订单不存在')

    @classmethod
    async def update_order_remark_services(cls, query_db: AsyncSession, page_object):
        """
        更新订单备注信息service

        :param query_db: orm对象
        :param page_object: 更新订单备注对象
        :return: 更新订单备注校验结果
        """
        update_data = page_object.model_dump(exclude_unset=True)
        order_info = await cls.order_detail_services(query_db, page_object.id)
        if order_info and order_info.get('id'):
            try:
                await OrderDao.edit_order_dao(query_db, update_data)
                await query_db.commit()
                return CrudResponseModel(is_success=True, message='备注更新成功')
            except Exception as e:
                await query_db.rollback()
                raise e
        else:
            raise ServiceException(message='订单不存在')

    @classmethod
    async def cancel_order_services(cls, query_db: AsyncSession, page_object: DeleteOrderModel):
        """
        取消订单信息service

        :param query_db: orm对象
        :param page_object: 取消订单对象
        :return: 取消订单校验结果
        """
        if page_object.ids:
            order_id_list = page_object.ids.split(',')

            # 检查订单状态是否允许取消
            for order_id in order_id_list:
                order_info = await cls.order_detail_services(query_db, int(order_id))
                if not order_info:
                    raise ServiceException(message=f'订单ID {order_id} 不存在')

                # 检查订单状态，已取消、已完成、已评价的订单不能取消
                non_cancelable_statuses = ['已取消', '已完成', '已评价']
                order_status_name = order_info.get('orderStatusName', '')
                if order_status_name in non_cancelable_statuses:
                    order_number = order_info.get('orderNumber', order_id)
                    raise ServiceException(message=f'订单 {order_number} 当前状态为"{order_status_name}"，无法取消')

            try:
                # 更新订单状态为已取消
                await OrderDao.cancel_order_dao(query_db, order_id_list)
                await query_db.commit()
                return CrudResponseModel(is_success=True, message='订单取消成功')
            except Exception as e:
                await query_db.rollback()
                raise e
        else:
            raise ServiceException(message='传入订单id为空')

    @classmethod
    async def order_detail_services(cls, query_db: AsyncSession, order_id: int):
        """
        获取订单详细信息service

        :param query_db: orm对象
        :param order_id: 订单id
        :return: 订单id对应的信息
        """
        order = await OrderDao.get_order_detail_by_id(query_db, order_id)
        if order:
            # 查询产品SKU获取服务时长
            service_duration = None
            if order.product_id:
                product_sku = await ProductSkuDao.get_product_sku_by_product_id(query_db, order.product_id)
                if product_sku and product_sku.duration:
                    service_duration = product_sku.duration  # 分钟数

            # 处理订单来源显示
            source_display = order.source
            if order.source == 'client':
                source_display = '门店端'
            elif order.source == 'proxy':
                source_display = '代客下单'

            # 将SQLAlchemy对象转换为字典
            order_dict = {
                'id': order.id,
                'orderNumber': order.order_number,
                'serveNumber': order.serve_number,
                'userId': order.user_id,
                'mobile': getattr(order, 'mobile', None),  # 添加用户手机号
                'storeId': order.store_id,
                'storeName': order.store_name,
                'storeUuid': order.store_uuid,
                'productId': order.product_id,
                'productName': order.product_name,
                'productType': order.product_type,
                'productTypeName': order.product_type_name,
                'productUnit': order.product_unit,
                'serviceAddress': order.service_address,
                'addressId': order.address_id,
                'channelNumber': order.channel_number,
                'servicePhone': order.service_phone,
                'orderStatus': order.order_status,
                'orderStatusName': order.order_status_name,
                'serviceType': order.service_type,
                'serviceTypeName': order.service_type_name,
                'remark': order.remark,
                'afterSaleRemark': order.after_sale_remark,
                'serviceRemark': order.service_remark,
                'serviceDate': order.service_date,
                'serviceDuration': service_duration,  # 新增服务时长（分钟）
                'recommendLevel': order.recommend_level,
                'founderType': order.founder_type,
                'createTime': order.create_time,
                'updateTime': order.update_time,
                'buyNum': order.buy_num,
                'payActual': order.pay_actual,
                'source': source_display,  # 使用处理后的订单来源显示
                'totalPayActual': order.total_pay_actual,
                'serviceHour': order.service_hour,
                'servicePersonal': order.service_personal,
                'payType': order.pay_type,
                'saleUserCommission': order.sale_user_commission,
                'saleUserUuid': order.sale_user_uuid,
                'usedTime': order.used_time,
                'productSkuId': order.product_sku_id,
                'couponId': order.coupon_id,
                'endaddressId': order.endaddress_id
            }

            # 查询派单信息
            order_waiters = await OrderWaiterDao.get_order_waiter_by_order_number(query_db, order.order_number)
            waiter_list = []

            for waiter in order_waiters:
                waiter_dict = {
                    'id': waiter.id,
                    'orderNumber': waiter.order_number,
                    'serviceId': waiter.service_id,
                    'serviceName': waiter.service_name,
                    'servicePersonalCommission': waiter.service_personal_commission,
                    'servicePersonal': waiter.service_personal,
                    'createTime': waiter.create_time,
                    'serviceStartTime': waiter.service_start_time,
                    'serviceEndTime': waiter.service_end_time,
                }

                # 查询图片信息
                env_before_images = []
                service_before_images = []
                service_after_images = []
                signature_images = []

                if waiter.env_before_images:
                    env_before_images = await CommonService.get_file_list_by_main_id(query_db, int(waiter.env_before_images))

                if waiter.service_before_images:
                    service_before_images = await CommonService.get_file_list_by_main_id(query_db, int(waiter.service_before_images))

                if waiter.service_after_images:
                    service_after_images = await CommonService.get_file_list_by_main_id(query_db, int(waiter.service_after_images))

                if waiter.signature_images:
                    signature_images = await CommonService.get_file_list_by_main_id(query_db, int(waiter.signature_images))

                waiter_dict.update({
                    'envBeforeImages': env_before_images,
                    'serviceBeforeImages': service_before_images,
                    'serviceAfterImages': service_after_images,
                    'signatureImages': signature_images
                })

                waiter_list.append(waiter_dict)

            order_dict['orderWaiters'] = waiter_list
            return order_dict
        else:
            return {}

    @classmethod
    async def export_order_list_services(cls, order_list):
        """
        导出订单信息service

        :param order_list: 订单信息列表
        :return: 订单信息对应excel的二进制数据
        """
        # 确保 order_list 是一个列表
        if isinstance(order_list, dict) and 'rows' in order_list:
            # 如果是包含统计信息的字典结构，提取 rows
            actual_order_list = order_list['rows']
        elif hasattr(order_list, 'rows'):
            # 如果是分页结果对象，提取 rows
            actual_order_list = order_list.rows
        else:
            # 直接是列表
            actual_order_list = order_list
        # 创建一个映射字典，将英文键映射到中文键
        mapping_dict = {
            'id': '订单ID',
            'orderNumber': '订单编号',
            'mobile': '用户手机号',
            'storeId': '门店ID',
            'storeName': '门店名称',
            'productName': '产品名称',
            'orderStatus': '订单状态',
            'orderStatusName': '订单状态名称',
            'serviceType': '服务类型',
            'payActual': '实际支付金额',
            'totalPayActual': '总支付金额',
            'source': '订单来源',
            'serviceDate': '服务时间',
            'createTime': '创建时间',
            'updateTime': '更新时间',
        }

        # 处理数据，只包含映射字典中的字段
        processed_data = []
        for item in actual_order_list:
            # 确保 item 是字典类型
            if hasattr(item, '__dict__'):
                # 如果是对象，转换为字典
                from utils.common_util import CamelCaseUtil
                item_dict = CamelCaseUtil.transform_result(item.__dict__)
            elif isinstance(item, dict):
                # 如果已经是字典，直接使用
                item_dict = item
            else:
                # 跳过无效的数据项
                continue

            processed_item = {}
            for key, chinese_name in mapping_dict.items():
                value = item_dict.get(key, '')
                # 格式化特殊字段
                if key in ['createTime', 'updateTime', 'serviceDate'] and value:
                    # 如果是datetime对象，格式化为字符串
                    if hasattr(value, 'strftime'):
                        value = value.strftime('%Y-%m-%d %H:%M:%S')
                    elif isinstance(value, str) and 'T' in value:
                        # 处理ISO格式的时间字符串
                        try:
                            from datetime import datetime
                            dt = datetime.fromisoformat(value.replace('Z', '+00:00'))
                            value = dt.strftime('%Y-%m-%d %H:%M:%S')
                        except:
                            pass
                elif key == 'source':
                    # 处理订单来源显示
                    if value == 'client':
                        value = '门店端'
                    elif value == 'proxy':
                        value = '代客下单'
                processed_item[key] = value
            processed_data.append(processed_item)

        # 使用自定义方法创建Excel，确保列宽适合内容
        export_result = cls._create_excel_with_auto_width(processed_data, mapping_dict)
        return export_result

    @classmethod
    def _create_excel_with_auto_width(cls, data_list, mapping_dict):
        """
        创建Excel文件，自动调整列宽以适合内容

        :param data_list: 数据列表
        :param mapping_dict: 字段映射字典
        :return: Excel文件的二进制数据
        """
        # 创建工作簿
        wb = Workbook()
        ws = wb.active
        ws.title = "订单列表"

        # 设置表头样式
        header_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
        header_font = Font(color='FFFFFF', bold=True)
        header_alignment = Alignment(horizontal='center', vertical='center')

        # 写入表头
        headers = list(mapping_dict.values())
        for col_num, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col_num, value=header)
            cell.fill = header_fill
            cell.font = header_font
            cell.alignment = header_alignment

        # 写入数据
        for row_num, item in enumerate(data_list, 2):
            for col_num, field_key in enumerate(mapping_dict.keys(), 1):
                value = item.get(field_key, '')
                cell = ws.cell(row=row_num, column=col_num, value=value)
                # 设置数据单元格对齐方式
                cell.alignment = Alignment(horizontal='left', vertical='center')

        # 自动调整列宽
        for col_num, field_key in enumerate(mapping_dict.keys(), 1):
            column_letter = get_column_letter(col_num)
            header_length = len(mapping_dict[field_key])

            # 计算该列的最大内容长度
            max_length = header_length
            for item in data_list:
                value = str(item.get(field_key, ''))
                # 中文字符按2个字符计算宽度
                content_length = sum(2 if ord(char) > 127 else 1 for char in value)
                max_length = max(max_length, content_length)

            # 设置列宽，最小12，最大50
            column_width = min(max(max_length + 2, 12), 50)
            ws.column_dimensions[column_letter].width = column_width

        # 设置行高
        for row in ws.iter_rows():
            ws.row_dimensions[row[0].row].height = 20

        # 保存为二进制数据
        binary_data = io.BytesIO()
        wb.save(binary_data)
        binary_data.seek(0)

        return binary_data.getvalue()

    @classmethod
    async def get_available_dispatch_staff_services(cls, query_db: AsyncSession, order_id: int):
        """
        获取可选派单人员列表service

        :param query_db: orm对象
        :param order_id: 订单ID
        :return: 可选派单人员列表
        """
        # 获取订单信息
        order = await OrderDao.get_order_detail_by_id(query_db, order_id)
        if not order:
            raise ServiceException(message='订单不存在')

        # 1. 通过订单的store_uuid获取该门店下所有员工
        store_staff_list = await ServiceStaffDao.get_staff_by_store_uuid(query_db, order.store_uuid)
        if not store_staff_list:
            return []

        # 2. 获取员工ID列表
        staff_ids = [staff.id for staff in store_staff_list]

        # 3. 通过产品ID和员工ID列表查询支持该产品的员工ID
        if order.product_id:
            supported_staff_ids = await ServiceProductDao.get_staff_by_product_and_staff_ids(
                query_db, order.product_id, staff_ids
            )
        else:
            supported_staff_ids = staff_ids

        # 4. 筛选出支持该产品的员工信息
        available_staff = []
        for staff in store_staff_list:
            if staff.id in supported_staff_ids:
                available_staff.append({
                    'id': staff.id,
                    'user_name': staff.user_name,
                    'mobile': staff.mobile
                })

        return available_staff

    @classmethod
    async def update_order_dispatch_staff_services(cls, query_db: AsyncSession, order_number: str, staff_id: int, service_date: str = None, service_hour: str = None):
        """
        更新订单派单人员和服务时间service

        :param query_db: orm对象
        :param order_number: 订单编号
        :param staff_id: 新的员工ID
        :param service_date: 新的服务时间（可选）
        :param service_hour: 新的服务小时（可选）
        :return: 更新结果
        """
        # 获取员工信息
        staff_info = await ServiceStaffDao.get_staff_by_id(query_db, staff_id)
        if not staff_info:
            raise ServiceException(message='员工不存在')

        try:
            # 更新order_waiter表中的派单信息
            await OrderWaiterDao.update_order_waiter_staff(
                query_db, order_number, str(staff_id), staff_info.user_name
            )

            # 如果提供了服务时间，同时更新订单表中的服务时间
            if service_date and service_hour:
                await OrderDao.update_order_service_time(query_db, order_number, service_date, service_hour)

            await query_db.commit()
            return CrudResponseModel(is_success=True, message='派单信息更新成功')
        except Exception as e:
            await query_db.rollback()
            raise e
