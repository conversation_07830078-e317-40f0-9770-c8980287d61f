from datetime import datetime, timed<PERSON><PERSON>
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession
from exceptions.exception import ServiceException
from module_admin.dao.company_dao import CompanyDao
from module_admin.dao.platform_order_dao import PlatformOrderDao
from module_admin.dao.company_transaction_dao import CompanyTransactionDao
from module_admin.entity.do.company_do import Company
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.vo.company_vo import CompanyModel, CompanyPageQueryModel, DeleteCompanyModel, CompanyRechargeModel, CompanyRechargeResponseModel, BatchRenewCompanyVersionsModel, BatchRenewResponseModel, UndoBatchRenewModel
from utils.common_util import CommonUtil, CamelCaseUtil
from utils.pwd_util import PwdUtil


class CompanyService:
    """
    公司管理模块服务层
    """

    @classmethod
    async def create_company_with_store_service(cls, query_db: AsyncSession, experience_table, current_user=None):
        """
        创建公司并关联门店和软件版本

        :param query_db: orm对象
        :param experience_table: 商户对象
        :param current_user: 当前登录用户
        :return: 创建结果
        """
        # 获取公司名称
        company_name = experience_table.company_name

        # 1. 插入公司表数据
        company_uuid = CommonUtil.get_uuid_without_hyphen()
        company_data = {
            'id': company_uuid,
            'name': experience_table.license_name or company_name,
            # 'city_id': experience_table.selected_address.split('-')[0] if experience_table.selected_address and '-' in experience_table.selected_address else '110000',
            # 'city': experience_table.selected_address.split('-')[1] if experience_table.selected_address and '-' in experience_table.selected_address else '北京市',
            'address': experience_table.detail_address,
            'address_desc': experience_table.legal_address,
            'status': '1',
            'is_delete': '0'
        }
        company = await CompanyDao.add_company_dao(query_db, company_data)

        # 2. 插入门店表数据
        store_uuid = CommonUtil.get_compact_id(length=10)
        store_data = {
            'store_uuid': store_uuid,
            'company_id': company_uuid,
            'name': company_name,
            'phone': experience_table.phone_number,
            'mobile': experience_table.phone_number,
            'address': experience_table.detail_address,
            # 'business_hours': '09:00-18:00',
            'manager': experience_table.legal_person_name,
            'status': 1,
            'store_status': 1,
            'remark': '由商户表自动创建',
            'introduce': experience_table.detail_address or company_name,
            'email': '<EMAIL>',
            'is_new': 1,
            'level': 'A',
            'flag': 0,
            'is_show_wxapp': 1,
            'is_delete': 0,
            'created_by': current_user.user.user_name if current_user else 'admin',
            'updated_by': current_user.user.user_name if current_user else 'admin'
        }
        store = await CompanyDao.add_store_dao(query_db, store_data)

        # 3. 插入store_info表数据
        store_info_data = {
            'store_id': store.id,
            # 'type': '综合型',
            # 'nature': '私营企业',
            # 'unit_nature': '有限责任公司',
            'business_license': experience_table.license_code,
            # 'store_specifications': '中型',
            # 'desc_phone_type': '固定电话',
            # 'website_phone_type': '固定电话',
            'is_invoice': 1,
            'invoice_title': experience_table.license_name,
            'invoice_code': experience_table.license_code,
            'invoice_phone': experience_table.phone_number,
            'invoice_address': experience_table.detail_address,
            # 'invoice_bank': '中国银行',
            # 'invoice_account': '****************',
            'is_bd_pos': 0,
            'location_synchronize_status': 1,
            'health_degree': 95.5,
            'is_clean_keeping': 1,
            'is_housekeeping_staff': 1,
            'is_part_time_job': 1,
            'is_free': 0,
            'is_discount': 0,
            'user_num_limit': 100,
            'pay_num_limit': 100
        }
        await CompanyDao.add_store_info_dao(query_db, store_info_data)

        # 4. 获取软件版本信息
        from sqlalchemy import select
        from module_admin.entity.do.software_version_do import SoftwareVersion

        # 从experience_table.selected_products获取版本ID
        selected_products = experience_table.selected_products or ""
        version_ids = [int(id_str) for id_str in selected_products.split(',') if id_str.strip().isdigit()]

        # 如果没有选择产品，默认使用ID为1的产品
        if not version_ids:
            version_ids = [1]

        # 直接在循环中处理所有版本

        # 为所有选择的版本创建关联
        for version_id in version_ids:
            version = (await query_db.execute(
                select(SoftwareVersion).where(SoftwareVersion.id == version_id)
            )).scalars().first()

            if version:
                # 创建公司与该版本的关联
                relation_data = {
                    'company_uuid': company_uuid,
                    'version_uuid': version.uuid,
                    'purchase_time': datetime.now(),
                    'expire_time': datetime.now() + timedelta(days=7), 
                    'status': 1,
                    'price': version.price,
                    'order_no': f'ORDER-{datetime.now().strftime("%Y%m%d")}-{CommonUtil.get_short_uuid()}',
                    'create_by': current_user.user.user_name if current_user else 'admin',
                    'update_by': current_user.user.user_name if current_user else 'admin',
                    'remark': f'关联版本ID: {version_id}'
                }
                await CompanyDao.add_company_version_relation_dao(query_db, relation_data)

        # 所有版本关联已在上面的循环中创建完成

        # 将ORM对象转换为字典，避免延迟加载问题
        company_dict = {
            'id': company.id,
            'name': company.name
        }

        store_dict = {
            'id': store.id,
            'store_uuid': store.store_uuid,
            'name': store.name
        }

        # 返回创建的公司和门店信息，以便后续使用
        return CrudResponseModel(is_success=True, message='创建公司、门店和版本关联成功', result={'company': company_dict, 'store': store_dict})

    @classmethod
    async def create_internal_user_service(cls, query_db: AsyncSession, company_uuid: str, store_uuid: str,
                                          store_id: int, company_name: str, store_name: str,
                                          experience_table, current_user=None):
        """
        创建内部用户账号

        :param query_db: orm对象
        :param company_uuid: 公司UUID
        :param store_uuid: 门店UUID
        :param store_id: 门店ID
        :param company_name: 公司名称
        :param store_name: 门店名称
        :param experience_table: 商户对象
        :param current_user: 当前登录用户
        :return: 创建结果
        """
        # 生成UUID
        user_uuid = CommonUtil.get_uuid_without_hyphen()

        # 生成随机密码（8位小写字母+数字）
        import random
        import string
        chars = string.ascii_lowercase + string.digits
        default_password = ''.join(random.choice(chars) for _ in range(8))
        encrypted_password = PwdUtil.get_password_hash(default_password)

        # 使用experience_table中的手机号码
        mobile = experience_table.phone_number if experience_table and experience_table.phone_number else '13800000000'

        # 准备内部用户数据
        user_data = {
            'uuid': user_uuid,
            'mobile': mobile,
            'password': encrypted_password,
            'name': store_name,
            'company_id': company_uuid,
            'company_name': company_name,
            'store_id': str(store_id),
            'store_uuid': store_uuid,
            'store_name': store_name,
            'role_id': '1',  # 默认角色ID为1（管理员）
            'role_name': '超级管理员',
            'status': '1'  # 状态为正常
        }

        # 插入内部用户表
        internal_user = await CompanyDao.add_internal_user_dao(query_db, user_data)

        # 准备内部用户权限数据
        permission_data = {
            'user_id': str(internal_user.id),  # 使用internal_user的id字段
            'action_ids': '',  # 默认权限ID
            'action_menu': ''  # 默认菜单权限
        }

        # 插入内部用户权限表
        await CompanyDao.add_internal_user_permission_dao(query_db, permission_data)

        return CrudResponseModel(is_success=True, message=f'创建内部用户账号成功，用户名：{user_data["name"]}，默认密码：{default_password}', result={'username': user_data["name"], 'password': default_password, 'mobile': mobile})

    @classmethod
    async def create_employee_store_service(cls, query_db: AsyncSession, experience_table, current_user=None):
        """
        创建员工门店（绑定到统一公司）

        :param query_db: orm对象
        :param experience_table: 商户对象
        :param current_user: 当前登录用户
        :return: 创建结果
        """
        # 获取公司名称
        company_name = experience_table.company_name

        # 统一绑定的公司UUID
        unified_company_uuid = '39a819e5dee74c929b96da255cc0c384'

        # 1. 插入门店表数据
        store_uuid = CommonUtil.get_compact_id(length=10)
        store_data = {
            'store_uuid': store_uuid,
            'company_id': unified_company_uuid,
            'name': f"{experience_table.company_name}的门店",
            'phone': experience_table.phone_number,
            'mobile': experience_table.phone_number,
            'address': experience_table.detail_address,
            'manager': experience_table.legal_person_name,
            'status': 1,
            'store_status': 1,
            'remark': '小羽佳试用员工，内部自动创建',
            'introduce': experience_table.detail_address or company_name,
            'email': '<EMAIL>',
            'is_new': 1,
            'level': 'A',
            'flag': 0,
            'is_show_wxapp': 1,
            'is_delete': 0,
            'created_by': current_user.user.user_name if current_user else 'admin',
            'updated_by': current_user.user.user_name if current_user else 'admin'
        }
        store = await CompanyDao.add_store_dao(query_db, store_data)

        # 2. 插入store_info表数据
        store_info_data = {
            'store_id': store.id,
            'business_license': experience_table.license_code,
            'is_invoice': 1,
            'invoice_title': experience_table.license_name,
            'invoice_code': experience_table.license_code,
            'invoice_phone': experience_table.phone_number,
            'invoice_address': experience_table.detail_address,
            'is_bd_pos': 0,
            'location_synchronize_status': 1,
            'health_degree': 95.5,
            'is_clean_keeping': 1,
            'is_housekeeping_staff': 1,
            'is_part_time_job': 1,
            'is_free': 0,
            'is_discount': 0,
            'user_num_limit': 100,
            'pay_num_limit': 100
        }
        await CompanyDao.add_store_info_dao(query_db, store_info_data)

        # 将ORM对象转换为字典，避免延迟加载问题
        company_dict = {
            'id': unified_company_uuid,
            'name': '小羽佳服务有限公司'
        }

        store_dict = {
            'id': store.id,
            'store_uuid': store.store_uuid,
            'name': store.name
        }

        # 返回创建的公司和门店信息，以便后续使用
        return CrudResponseModel(is_success=True, message='创建员工门店成功', result={'company': company_dict, 'store': store_dict})

    @classmethod
    async def get_company_list_services(cls, query_db: AsyncSession, query_object: CompanyPageQueryModel, is_page: bool = False):
        """
        获取公司列表信息service

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 公司列表信息对象
        """
        company_list_result = await CompanyDao.get_company_list(query_db, query_object, is_page)
        return company_list_result

    @classmethod
    async def company_detail_services(cls, query_db: AsyncSession, company_id: str):
        """
        获取公司详细信息service

        :param query_db: orm对象
        :param company_id: 公司id
        :return: 公司信息对象
        """
        company = await CompanyDao.get_company_detail_by_id(query_db, company_id)
        if company:
            return company
        else:
            raise ServiceException(message='公司不存在')

    @classmethod
    async def add_company_services(cls, query_db: AsyncSession, add_company: CompanyModel):
        """
        新增公司信息service

        :param query_db: orm对象
        :param add_company: 新增公司对象
        :return: 新增公司校验结果
        """
        # 检查公司名称是否已存在
        existing_company = await CompanyDao.get_company_detail_by_name(query_db, add_company.name)
        if existing_company:
            raise ServiceException(message='公司名称已存在')

        # 生成公司ID
        if not add_company.id:
            add_company.id = CommonUtil.get_uuid_without_hyphen()

        # 设置创建时间
        add_company.create_time = datetime.now()
        add_company.update_time = datetime.now()

        # 转换为字典
        company_data = add_company.model_dump(exclude_unset=True)

        try:
            await CompanyDao.add_company_dao(query_db, company_data)
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='新增成功')
        except Exception as e:
            await query_db.rollback()
            raise ServiceException(message=f'新增失败: {str(e)}')

    @classmethod
    async def edit_company_services(cls, query_db: AsyncSession, edit_company: CompanyModel):
        """
        编辑公司信息service

        :param query_db: orm对象
        :param edit_company: 编辑公司对象
        :return: 编辑公司校验结果
        """
        # 检查公司是否存在 - 使用get_company_by_id获取实体对象用于编辑
        existing_company = await CompanyDao.get_company_by_id(query_db, edit_company.id)
        if not existing_company:
            raise ServiceException(message='公司不存在')

        # 检查公司名称是否与其他公司重复
        name_check_company = await CompanyDao.get_company_detail_by_name(query_db, edit_company.name)
        if name_check_company and name_check_company.id != edit_company.id:
            raise ServiceException(message='公司名称已存在')

        # 更新字段
        for key, value in edit_company.model_dump(exclude_unset=True, exclude={'id'}).items():
            if hasattr(existing_company, key) and value is not None:
                setattr(existing_company, key, value)

        # 设置更新时间
        existing_company.update_time = datetime.now()

        try:
            await CompanyDao.edit_company_dao(query_db, existing_company)
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='更新成功')
        except Exception as e:
            await query_db.rollback()
            raise ServiceException(message=f'更新失败: {str(e)}')

    @classmethod
    async def delete_company_services(cls, query_db: AsyncSession, delete_company: DeleteCompanyModel):
        """
        删除公司信息service

        :param query_db: orm对象
        :param delete_company: 删除公司对象
        :return: 删除公司校验结果
        """
        if delete_company.ids:
            company_id_list = delete_company.ids.split(',')
            try:
                await CompanyDao.delete_company_dao(query_db, delete_company)
                await query_db.commit()
                return CrudResponseModel(is_success=True, message='删除成功')
            except Exception as e:
                await query_db.rollback()
                raise ServiceException(message=f'删除失败: {str(e)}')
        else:
            raise ServiceException(message='传入公司id为空')

    @classmethod
    async def get_company_stores_detail_services(cls, query_db: AsyncSession, company_id: str):
        """
        获取公司门店详情信息service

        :param query_db: orm对象
        :param company_id: 公司id
        :return: 公司门店详情信息
        """
        # 检查公司是否存在
        company = await CompanyDao.get_company_detail_by_id(query_db, company_id)
        if not company:
            raise ServiceException(message='公司不存在')

        # 获取公司下的所有门店及其详细信息
        stores_detail = await CompanyDao.get_company_stores_with_info(query_db, company_id)

        return {
            'company': company,
            'stores': stores_detail
        }

    @classmethod
    async def company_recharge_service(cls, query_db: AsyncSession, recharge_data: CompanyRechargeModel, current_user=None):
        """
        公司充值服务

        :param query_db: orm对象
        :param recharge_data: 充值数据
        :param current_user: 当前登录用户
        :return: 充值结果
        """
        try:
            # 1. 检查公司是否存在
            company = await CompanyDao.get_company_by_id(query_db, recharge_data.company_id)
            if not company:
                raise ServiceException(message='公司不存在')

            # 2. 获取当前余额
            current_balance = await CompanyDao.get_company_balance(query_db, recharge_data.company_id)

            # 3. 计算新余额
            new_balance = current_balance + recharge_data.amount

            # 4. 生成订单号
            order_no = f"RCH{datetime.now().strftime('%Y%m%d%H%M%S')}{CommonUtil.get_short_uuid(6)}"

            # 5. 创建充值订单
            order_data = {
                'order_no': order_no,
                'company_uuid': recharge_data.company_id,
                'company_name': company.name,
                'business_type': 'RECHARGE',
                'business_type_name': '余额充值',
                'product_type': 'BALANCE',
                'product_name': '余额充值',
                'quantity': 1,
                'unit_price': recharge_data.amount,
                'total_amount': recharge_data.amount,
                'discount_amount': 0.00,
                'actual_amount': recharge_data.amount,
                'pay_type': 'ADMIN_RECHARGE',
                'pay_type_name': '管理员充值',
                'payment_status': 2,  # 已支付
                'payment_time': datetime.now(),
                'order_status': 3,  # 已完成
                'order_status_name': '已完成',
                'operator_id': current_user.user.user_id if current_user else 'admin',
                'operator_name': current_user.user.user_name if current_user else 'admin',
                'operator_type': 'ADMIN',
                'remark': recharge_data.remark or '管理员充值',
                'created_by': current_user.user.user_name if current_user else 'admin',
                'updated_by': current_user.user.user_name if current_user else 'admin'
            }

            order = await PlatformOrderDao.add_platform_order_dao(query_db, order_data)

            # 6. 更新公司余额
            await CompanyDao.update_company_balance(query_db, recharge_data.company_id, new_balance)

            # 7. 记录资金流水
            transaction_no = f"TXN{datetime.now().strftime('%Y%m%d%H%M%S')}{CommonUtil.get_short_uuid(6)}"

            # 业务类型映射
            business_type_map = {
                'RECHARGE': '余额充值'
            }

            transaction_data = {
                'company_uuid': recharge_data.company_id,
                'transaction_no': transaction_no,
                'business_type': recharge_data.business_type or 'RECHARGE',
                'business_type_name': business_type_map.get(recharge_data.business_type, '余额充值'),
                'transaction_type': 1,  # 1:充值
                'amount': recharge_data.amount,
                'balance_before': current_balance,
                'balance_after': new_balance,
                'related_order_no': order_no,
                'pay_type': recharge_data.pay_type or 'ADMIN_RECHARGE',
                'external_transaction_id': recharge_data.external_transaction_id,
                'operator_id': current_user.user.user_id if current_user else 'admin',
                'operator_name': current_user.user.user_name if current_user else 'admin',
                'description': recharge_data.description or f'管理员充值，订单号：{order_no}',
                'remark': recharge_data.remark or '管理员充值',
                'transaction_status': 'SUCCESS',  # 管理员充值直接成功
                'created_by': current_user.user.user_name if current_user else 'admin'
            }

            await CompanyTransactionDao.add_company_transaction_dao(query_db, transaction_data)

            # 8. 在提交事务前获取order.id，避免MissingGreenlet错误
            order_id = order.id

            # 9. 提交事务
            await query_db.commit()

            # 10. 构造返回数据
            response_data = {
                'order_id': str(order_id),
                'order_no': order_no,
                'company_id': recharge_data.company_id,
                'amount': recharge_data.amount,
                'balance_before': current_balance,
                'balance_after': new_balance,
                'create_time': datetime.now()
            }

            # 11. 返回充值结果
            return CrudResponseModel(
                is_success=True,
                message='充值成功',
                result=CompanyRechargeResponseModel(**CamelCaseUtil.transform_result(response_data))
            )

        except ServiceException:
            await query_db.rollback()
            raise
        except Exception as e:
            await query_db.rollback()
            raise ServiceException(message=f'充值失败: {str(e)}')

    @classmethod
    async def create_pay_types_service(cls, query_db: AsyncSession, company_uuid: str, current_user=None):
        """
        为公司创建默认支付类型

        :param query_db: orm对象
        :param company_uuid: 公司UUID
        :param current_user: 当前登录用户
        :return: 创建结果
        """
        try:
            # 定义五种默认支付类型
            pay_types_config = [
                {
                    'company_uuid': company_uuid,
                    'pay_type_code': 'BALANCE',
                    'pay_type_name': '余额支付',
                    'icon_url': None,
                    'is_enabled': 1,
                    'is_preset': 1,
                    'channel_config': '{"description":"使用账户余额支付"}',
                    'sort_order': 1,
                    'remark': None,
                    'created_by': current_user.user.user_name if current_user else 'system',
                    'updated_by': None
                },
                {
                    'company_uuid': company_uuid,
                    'pay_type_code': 'WECHAT',
                    'pay_type_name': '微信支付',
                    'icon_url': None,
                    'is_enabled': 1,
                    'is_preset': 1,
                    'channel_config': '{"app_id":"","mch_id":"","api_key":"","cert_path":""}',
                    'sort_order': 2,
                    'remark': None,
                    'created_by': current_user.user.user_name if current_user else 'system',
                    'updated_by': None
                },
                {
                    'company_uuid': company_uuid,
                    'pay_type_code': 'ALIPAY',
                    'pay_type_name': '支付宝支付',
                    'icon_url': None,
                    'is_enabled': 1,
                    'is_preset': 1,
                    'channel_config': '{"app_id":"","private_key":"","public_key":"","gateway":""}',
                    'sort_order': 3,
                    'remark': None,
                    'created_by': current_user.user.user_name if current_user else 'system',
                    'updated_by': None
                },
                {
                    'company_uuid': company_uuid,
                    'pay_type_code': 'BANK',
                    'pay_type_name': '银联支付',
                    'icon_url': None,
                    'is_enabled': 0,
                    'is_preset': 1,
                    'channel_config': '{"bank_code":"","merchant_id":"","api_key":""}',
                    'sort_order': 4,
                    'remark': None,
                    'created_by': current_user.user.user_name if current_user else 'system',
                    'updated_by': None
                },
                {
                    'company_uuid': company_uuid,
                    'pay_type_code': 'YEEPAY',
                    'pay_type_name': '易宝支付',
                    'icon_url': None,
                    'is_enabled': 0,
                    'is_preset': 1,
                    'channel_config': '{"merchant_id":"","private_key":"","public_key":"","gateway":""}',
                    'sort_order': 5,
                    'remark': None,
                    'created_by': current_user.user.user_name if current_user else 'system',
                    'updated_by': None
                }
            ]

            # 批量创建支付类型
            pay_types = await CompanyDao.batch_add_pay_types_dao(query_db, pay_types_config)

            return CrudResponseModel(is_success=True, message=f'成功创建{len(pay_types)}种支付类型', result={'pay_types_count': len(pay_types)})

        except Exception as e:
            raise ServiceException(message=f'创建支付类型失败: {str(e)}')

    @classmethod
    async def create_initial_products_service(cls, query_db: AsyncSession, company_uuid: str, current_user=None):
        """
        为公司创建初始产品（复制模板产品）

        :param query_db: orm对象
        :param company_uuid: 公司UUID
        :param current_user: 当前登录用户
        :return: 创建结果
        """
        try:
            # 1. 获取模板产品数据（id 48-68）
            template_products = await CompanyDao.get_template_products_dao(query_db, 48, 68)
            if not template_products:
                raise ServiceException(message='未找到模板产品数据')

            # 2. 获取模板产品的ID列表
            template_product_ids = [product.id for product in template_products]

            # 3. 获取对应的SKU数据
            template_skus = await CompanyDao.get_template_product_skus_dao(query_db, template_product_ids)

            # 4. 准备新产品数据
            new_products_data = []
            for product in template_products:
                # 将ORM对象转换为字典，排除id字段
                product_dict = {
                    'product_name': product.product_name,
                    'company_uuid': company_uuid,  # 使用新公司的UUID
                    'img_id': product.img_id,
                    'serve_type_name': product.serve_type_name,
                    'service_skill_id': product.service_skill_id,
                    'service_skill_name': product.service_skill_name,
                    'service_skill_main_id': product.service_skill_main_id,
                    'service_skill_main_name': product.service_skill_main_name,
                    'online_store_num': product.online_store_num,
                    'is_all_support_store': product.is_all_support_store,
                    'sum_num': product.sum_num,
                    'is_delete': product.is_delete,
                    'is_open_service_phone': product.is_open_service_phone,
                    'type': product.type,
                    'type_name': product.type_name,
                    'display_edit': product.display_edit,
                    'display_delete': product.display_delete,
                    'display_detail': product.display_detail,
                    'display_edit_product_detail': product.display_edit_product_detail,
                    'is_gaode_line': product.is_gaode_line,
                    'op_user_name': current_user.user.user_name if current_user else 'system',
                    'op_time': datetime.now(),
                    'product_status': product.product_status,
                    'details': product.details,
                    'min_number': product.min_number,
                    'max_number': product.max_number,
                    'video_id': product.video_id,
                    'uuid': CommonUtil.get_uuid_without_hyphen(),  # 生成新的UUID
                    'is_vip_use': product.is_vip_use,
                    'is_bj': product.is_bj,
                    'buy_notes': product.buy_notes,
                    'buy_agreement': product.buy_agreement,
                    'rating_weight': product.rating_weight,
                    'proximity_weight': product.proximity_weight,
                    'wait_time_weight': product.wait_time_weight,
                    'ishot': product.ishot
                }
                new_products_data.append(product_dict)

            # 5. 批量创建新产品
            new_products = await CompanyDao.batch_add_products_dao(query_db, new_products_data)

            # 6. 创建原产品ID到新产品ID的映射
            product_id_mapping = {}
            for i, template_product in enumerate(template_products):
                product_id_mapping[template_product.id] = new_products[i].id

            # 7. 准备新SKU数据
            new_skus_data = []
            for sku in template_skus:
                # 获取对应的新产品ID
                new_product_id = product_id_mapping.get(sku.productid)
                if new_product_id:
                    sku_dict = {
                        'productid': new_product_id,  # 使用新产品的ID
                        'name': sku.name,
                        'now_price': sku.now_price,
                        'vip_price': sku.vip_price,
                        'duration': sku.duration,
                        'type_price_unit': sku.type_price_unit,
                        'define_commission': sku.define_commission,
                        'commission_type': sku.commission_type
                    }
                    new_skus_data.append(sku_dict)

            # 8. 批量创建新SKU
            new_skus = await CompanyDao.batch_add_product_skus_dao(query_db, new_skus_data)

            return CrudResponseModel(
                is_success=True,
                message=f'成功创建{len(new_products)}个产品和{len(new_skus)}个SKU',
                result={
                    'products_count': len(new_products),
                    'skus_count': len(new_skus),
                    'product_id_mapping': product_id_mapping
                }
            )

        except Exception as e:
            raise ServiceException(message=f'创建初始产品失败: {str(e)}')

    @classmethod
    async def get_available_versions_services(cls, query_db: AsyncSession):
        """
        获取可用的软件版本列表service

        :param query_db: orm对象
        :return: 可用版本列表
        """
        try:
            from utils.log_util import logger
            logger.info("开始获取可用版本列表")

            versions_result = await CompanyDao.get_available_versions_dao(query_db)

            logger.info(f"成功获取 {len(versions_result)} 个可用版本")
            return CamelCaseUtil.transform_result(versions_result)

        except Exception as e:
            from utils.log_util import logger
            logger.error(f"获取可用版本列表失败: {str(e)}")
            raise ServiceException(message=f'获取可用版本列表失败: {str(e)}')

    @classmethod
    async def get_company_versions_services(cls, query_db: AsyncSession, company_id: str):
        """
        获取公司关联的版本信息service（简化版）
        查询逻辑：company.id → company_version_relation.company_uuid → software_version.uuid → software_version.name

        :param query_db: orm对象
        :param company_id: 公司ID
        :return: 版本名称列表
        """
        # 直接获取版本名称列表
        version_names = await CompanyDao.get_company_versions_dao(query_db, company_id)

        # 返回版本名称列表，用于前端标签渲染
        return version_names

    @classmethod
    async def add_company_version_services(cls, query_db: AsyncSession, company_id: str, version_data: dict, current_user=None):
        """
        为公司新增版本关联service（优化版，无备注）

        :param query_db: orm对象
        :param company_id: 公司ID
        :param version_data: 版本数据
        :param current_user: 当前登录用户
        :return: 新增结果
        """
        # 检查公司是否存在
        company = await CompanyDao.get_company_by_id(query_db, company_id)
        if not company:
            raise ServiceException(message='公司不存在')

        # 检查版本是否存在
        version_uuid = version_data.get('version_uuid')
        if not version_uuid:
            raise ServiceException(message='版本UUID不能为空')

        version = await CompanyDao.get_software_version_by_uuid(query_db, version_uuid)
        if not version:
            raise ServiceException(message='版本不存在')

        # 检查是否已经关联该版本
        existing_relation = await CompanyDao.check_company_version_relation_dao(query_db, company_id, version_uuid)
        if existing_relation:
            raise ServiceException(message='该版本已经关联到此公司')

        # 准备关联数据（移除备注字段）
        relation_data = {
            'company_uuid': company_id,
            'version_uuid': version_uuid,
            'purchase_time': datetime.now(),
            'expire_time': datetime.strptime(version_data.get('expire_time'), '%Y-%m-%d %H:%M:%S') if version_data.get('expire_time') else datetime.now() + timedelta(days=365),
            'status': 1,
            'price': version.price,
            'order_no': f'ORDER-{datetime.now().strftime("%Y%m%d")}-{CommonUtil.get_short_uuid()}',
            'create_by': current_user.user.user_name if current_user else 'admin',
            'update_by': current_user.user.user_name if current_user else 'admin'
            # 移除 remark 字段
        }

        try:
            await CompanyDao.add_company_version_relation_dao(query_db, relation_data)
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='版本关联成功')
        except Exception as e:
            await query_db.rollback()
            raise ServiceException(message=f'版本关联失败: {str(e)}')

    @classmethod
    async def get_company_versions_detail_services(cls, query_db: AsyncSession, company_id: str):
        """
        获取公司版本详细信息service（包含到期时间）

        :param query_db: orm对象
        :param company_id: 公司ID
        :return: 版本详细信息列表
        """
        try:
            # 检查公司是否存在
            company = await CompanyDao.get_company_by_id(query_db, company_id)
            if not company:
                raise ServiceException(message='公司不存在')

            # 获取版本详细信息
            versions_detail = await CompanyDao.get_company_versions_detail_dao(query_db, company_id)

            # 转换数据格式
            result = []
            for version in versions_detail:
                result.append({
                    'uuid': version.uuid,
                    'name': version.name,
                    'description': version.description,
                    'price': float(version.price) if version.price else 0,
                    'expireTime': version.expire_time.strftime('%Y-%m-%d %H:%M:%S') if version.expire_time else None,
                    'purchaseTime': version.purchase_time.strftime('%Y-%m-%d %H:%M:%S') if version.purchase_time else None,
                    'status': version.status
                })

            return result

        except ServiceException as e:
            raise e
        except Exception as e:
            from utils.log_util import logger
            logger.error(f"获取公司版本详细信息失败: {str(e)}")
            raise ServiceException(message=f'获取公司版本详细信息失败: {str(e)}')

    @classmethod
    async def renew_company_versions_services(cls, query_db: AsyncSession, company_id: str, renew_data: list, current_user=None):
        """
        批量更新公司版本到期时间service

        :param query_db: orm对象
        :param company_id: 公司ID
        :param renew_data: 续费数据列表 [{'version_uuid': 'xxx', 'expire_time': 'yyyy-mm-dd hh:mm:ss'}]
        :param current_user: 当前登录用户
        :return: 更新结果
        """
        try:
            # 检查公司是否存在
            company = await CompanyDao.get_company_by_id(query_db, company_id)
            if not company:
                raise ServiceException(message='公司不存在')

            update_by = current_user.user.user_name if current_user else 'admin'
            updated_count = 0

            # 批量更新版本到期时间
            for item in renew_data:
                version_uuid = item.get('version_uuid')
                expire_time_str = item.get('expire_time')

                if not version_uuid or not expire_time_str:
                    continue

                # 解析时间字符串
                try:
                    expire_time = datetime.strptime(expire_time_str, '%Y-%m-%d %H:%M:%S')
                except ValueError:
                    raise ServiceException(message=f'时间格式错误: {expire_time_str}，请使用 YYYY-MM-DD HH:MM:SS 格式')

                # 更新到期时间
                result = await CompanyDao.update_company_version_expire_time_dao(
                    query_db, company_id, version_uuid, expire_time, update_by
                )

                if result > 0:
                    updated_count += 1

            return CrudResponseModel(
                is_success=True,
                message=f'成功更新 {updated_count} 个版本的到期时间'
            )

        except ServiceException as e:
            raise e
        except Exception as e:
            from utils.log_util import logger
            logger.error(f"批量更新版本到期时间失败: {str(e)}")
            raise ServiceException(message=f'批量更新版本到期时间失败: {str(e)}')

    @classmethod
    async def batch_renew_company_versions_services(cls, query_db: AsyncSession, batch_renew_data: BatchRenewCompanyVersionsModel, current_user=None):
        """
        批量续费多个公司的版本service

        :param query_db: orm对象
        :param batch_renew_data: 批量续费数据
        :param current_user: 当前登录用户
        :return: 批量续费结果
        """
        try:
            import json
            from datetime import datetime, timedelta

            # 生成操作ID
            operation_id = CommonUtil.get_uuid()
            update_by = current_user.user.user_name if current_user else 'admin'

            # 验证公司是否存在
            valid_companies = []
            for company_id in batch_renew_data.company_ids:
                company = await CompanyDao.get_company_by_id(query_db, company_id)
                if company:
                    valid_companies.append(company_id)

            if not valid_companies:
                raise ServiceException(message='所选公司均不存在或已删除')

            # 获取所有公司的版本信息
            company_versions = await CompanyDao.get_companies_versions_for_batch_renew_dao(query_db, valid_companies)

            if not company_versions:
                raise ServiceException(message='所选公司均未关联任何版本')

            # 准备更新数据和回滚数据
            update_data = []
            rollback_data = []

            for version_info in company_versions:
                company_uuid = version_info.company_uuid
                version_uuid = version_info.version_uuid
                current_expire_time = version_info.expire_time

                # 计算新的到期时间
                if batch_renew_data.custom_expire_time:
                    # 自定义时间模式：使用指定的到期时间
                    try:
                        new_expire_time = datetime.strptime(batch_renew_data.custom_expire_time, '%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        raise ServiceException(message=f'自定义时间格式错误: {batch_renew_data.custom_expire_time}，请使用 YYYY-MM-DD HH:MM:SS 格式')
                elif batch_renew_data.operation_type == 'EXTEND':
                    # 延长模式：在当前到期时间基础上延长
                    new_expire_time = current_expire_time + timedelta(days=batch_renew_data.extend_months * 30)
                else:
                    # 重置模式：从当前时间开始计算
                    new_expire_time = datetime.now() + timedelta(days=batch_renew_data.extend_months * 30)

                update_data.append({
                    'company_uuid': company_uuid,
                    'version_uuid': version_uuid,
                    'new_expire_time': new_expire_time
                })

                # 记录回滚数据
                rollback_data.append({
                    'company_uuid': company_uuid,
                    'version_uuid': version_uuid,
                    'original_expire_time': current_expire_time.isoformat() if current_expire_time else None
                })

            # 记录操作日志
            log_data = {
                'operation_id': operation_id,
                'operation_type': 'BATCH_RENEW_VERSIONS',
                'operation_data': json.dumps({
                    'company_ids': batch_renew_data.company_ids,
                    'extend_months': batch_renew_data.extend_months,
                    'operation_type': batch_renew_data.operation_type,
                    'custom_expire_time': batch_renew_data.custom_expire_time,
                    'remark': batch_renew_data.remark
                }),
                'rollback_data': json.dumps(rollback_data),
                'total_count': len(update_data),
                'success_count': 0,
                'failed_count': 0,
                'status': 0,  # 初始状态
                'create_by': update_by,
                'update_by': update_by,
                'remark': batch_renew_data.remark
            }

            await CompanyDao.add_batch_operation_log_dao(query_db, log_data)

            # 执行批量更新
            batch_result = await CompanyDao.batch_update_company_versions_expire_time_dao(query_db, update_data, update_by)

            # 更新操作日志状态
            await CompanyDao.update_batch_operation_log_status_dao(
                query_db, operation_id, 1 if batch_result['failed_count'] == 0 else 0, update_by
            )

            # 提交事务
            await query_db.commit()

            # 构建响应
            response_data = BatchRenewResponseModel(
                operationId=operation_id,
                totalCompanies=len(valid_companies),
                successCount=batch_result['success_count'],
                failedCount=batch_result['failed_count'],
                failedDetails=batch_result['failed_details'],
                createTime=datetime.now()
            )

            return CrudResponseModel(
                is_success=True,
                message=f'批量续费完成，成功 {batch_result["success_count"]} 个，失败 {batch_result["failed_count"]} 个',
                result=response_data
            )

        except ServiceException as e:
            await query_db.rollback()
            raise e
        except Exception as e:
            await query_db.rollback()
            from utils.log_util import logger
            logger.error(f"批量续费失败: {str(e)}")
            raise ServiceException(message=f'批量续费失败: {str(e)}')

    @classmethod
    async def undo_batch_renew_company_versions_services(cls, query_db: AsyncSession, undo_data: UndoBatchRenewModel, current_user=None):
        """
        撤销批量续费操作service

        :param query_db: orm对象
        :param undo_data: 撤销数据
        :param current_user: 当前登录用户
        :return: 撤销结果
        """
        try:
            import json
            from datetime import datetime

            update_by = current_user.user.user_name if current_user else 'admin'

            # 获取操作日志
            operation_log = await CompanyDao.get_batch_operation_log_dao(query_db, undo_data.operation_id)
            if not operation_log:
                raise ServiceException(message='操作记录不存在')

            if operation_log.status == 2:
                raise ServiceException(message='该操作已经被撤销')

            if operation_log.status != 1:
                raise ServiceException(message='只能撤销成功的操作')

            # 解析回滚数据
            try:
                rollback_data = json.loads(operation_log.rollback_data)
            except:
                raise ServiceException(message='回滚数据格式错误，无法撤销')

            # 准备回滚更新数据
            rollback_update_data = []
            for item in rollback_data:
                if item.get('original_expire_time'):
                    rollback_update_data.append({
                        'company_uuid': item['company_uuid'],
                        'version_uuid': item['version_uuid'],
                        'new_expire_time': datetime.fromisoformat(item['original_expire_time'])
                    })

            if not rollback_update_data:
                raise ServiceException(message='没有可回滚的数据')

            # 执行回滚更新
            rollback_result = await CompanyDao.batch_update_company_versions_expire_time_dao(
                query_db, rollback_update_data, update_by
            )

            # 更新操作日志状态为已撤销
            await CompanyDao.update_batch_operation_log_status_dao(
                query_db, undo_data.operation_id, 2, update_by
            )

            # 提交事务
            await query_db.commit()

            return CrudResponseModel(
                is_success=True,
                message=f'撤销成功，已回滚 {rollback_result["success_count"]} 个版本的到期时间'
            )

        except ServiceException as e:
            await query_db.rollback()
            raise e
        except Exception as e:
            await query_db.rollback()
            from utils.log_util import logger
            logger.error(f"撤销批量续费失败: {str(e)}")
            raise ServiceException(message=f'撤销批量续费失败: {str(e)}')