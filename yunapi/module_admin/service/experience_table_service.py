from sqlalchemy.ext.asyncio import AsyncSession
from exceptions.exception import ServiceException
from module_admin.dao.experience_table_dao import ExperienceTableDao
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.vo.experience_table_vo import DeleteExperienceTableModel, ExperienceTableModel, ExperienceTablePageQueryModel, ExperienceTableQueryModel
from utils.common_util import CamelCaseUtil


class ExperienceTableService:
    """
    商户管理模块服务层
    """

    @classmethod
    async def get_experience_table_list_services(
        cls, query_db: AsyncSession, query_object: ExperienceTablePageQueryModel, is_page: bool = False
    ):
        """
        获取商户列表信息service

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 商户列表信息对象
        """
        # 判断status是否为3，如果是3则不进行status筛选
        filter_status = True
        if query_object.status == 3:
            filter_status = False

        experience_table_list_result = await ExperienceTableDao.get_experience_table_list(
            query_db, query_object, is_page, filter_status=filter_status
        )

        return experience_table_list_result

    @classmethod
    async def add_experience_table_services(cls, query_db: AsyncSession, page_object: ExperienceTableModel):
        """
        新增商户信息service

        :param query_db: orm对象
        :param page_object: 新增商户对象
        :return: 新增商户校验结果
        """
        try:
            await ExperienceTableDao.add_experience_table_dao(query_db, page_object)
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='新增成功')
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    async def edit_experience_table_services(cls, query_db: AsyncSession, page_object: ExperienceTableModel):
        """
        编辑商户信息service

        :param query_db: orm对象
        :param page_object: 编辑商户对象
        :return: 编辑商户校验结果
        """
        if page_object.id:
            try:
                experience_table_dict = page_object.model_dump(exclude_none=True)
                await ExperienceTableDao.edit_experience_table_dao(query_db, experience_table_dict)
                await query_db.commit()
                return CrudResponseModel(is_success=True, message='修改成功')
            except Exception as e:
                await query_db.rollback()
                raise e
        else:
            raise ServiceException(message='传入商户ID为空')

    @classmethod
    async def delete_experience_table_services(cls, query_db: AsyncSession, page_object: DeleteExperienceTableModel):
        """
        删除商户信息service

        :param query_db: orm对象
        :param page_object: 删除商户对象
        :return: 删除商户校验结果
        """
        if page_object.ids:
            try:
                id_list = page_object.ids.split(',')
                for id_str in id_list:
                    experience_table = ExperienceTableModel(id=int(id_str))
                    await ExperienceTableDao.delete_experience_table_dao(query_db, experience_table)
                await query_db.commit()
                return CrudResponseModel(is_success=True, message='删除成功')
            except Exception as e:
                await query_db.rollback()
                raise e
        else:
            raise ServiceException(message='传入商户ID为空')

    @classmethod
    async def experience_table_detail_services(cls, query_db: AsyncSession, id: int):
        """
        获取商户详细信息service

        :param query_db: orm对象
        :param id: 商户ID
        :return: 商户ID对应的信息
        """
        experience_table = await ExperienceTableDao.get_experience_table_detail_by_id(query_db, id=id)
        if experience_table:
            result = ExperienceTableModel(**CamelCaseUtil.transform_result(experience_table))
        else:
            result = ExperienceTableModel(**dict())

        return result

    @classmethod
    async def update_experience_table_status_services(cls, query_db: AsyncSession, id: int, status: int):
        """
        更新商户状态service

        :param query_db: orm对象
        :param id: 商户ID
        :param status: 状态值
        :return: 更新状态结果
        """
        try:
            await ExperienceTableDao.update_experience_table_status_dao(query_db, id, status)
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='状态更新成功')
        except Exception as e:
            await query_db.rollback()
            raise e
