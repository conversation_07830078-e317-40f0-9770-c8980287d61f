from datetime import datetime
from typing import List, Optional, Dict, Any, Union
from sqlalchemy import select, and_, or_, func
from sqlalchemy.ext.asyncio import AsyncSession
from module_admin.entity.vo.internal_menu_vo import (InternalMenuModel, InternalMenuQueryModel,
                                                  DeleteInternalMenuModel, InternalMenuTreeSelectModel,
                                                  InternalRoleMenuTreeSelectModel)
from module_admin.entity.do.sys_internal_menu import SysInternalMenu
from module_admin.entity.do.sys_internal_role_menu import SysInternalRoleMenu
from module_admin.entity.do.sys_internal_role import SysInternalRole
from module_admin.entity.vo.role_vo import RoleMenuQueryModel
from module_admin.entity.vo.user_vo import CurrentUserModel
from utils.common_util import CamelCaseUtil, SqlalchemyUtil
from utils.page_util import PageResponseModel
from utils.response_util import ResponseUtil


class InternalMenuService:
    """内部菜单管理服务"""

    @staticmethod
    async def get_menu_list_services(
        query_db: AsyncSession,
        query_params: InternalMenuQueryModel,
        current_user: CurrentUserModel
    ) -> List[InternalMenuModel]:
        """
        获取内部菜单列表
        :param query_db: 数据库会话
        :param query_params: 查询参数
        :param current_user: 当前用户
        :return: 内部菜单列表
        """
        # 构建查询条件
        where_conditions = []
        if query_params.menuName:
            where_conditions.append(SysInternalMenu.menu_name.like(f"%{query_params.menuName}%"))
        if query_params.visible:
            where_conditions.append(SysInternalMenu.visible == query_params.visible)
        if query_params.status:
            where_conditions.append(SysInternalMenu.status == query_params.status)
        if query_params.clientType is not None:
            where_conditions.append(SysInternalMenu.client_type == query_params.clientType)

        # 执行查询
        select_sql = select(SysInternalMenu).where(and_(*where_conditions)).order_by(
            SysInternalMenu.parent_id, SysInternalMenu.order_num
        )
        result = await query_db.execute(select_sql)
        menu_list = result.scalars().all()

        # 构建菜单树形结构
        menu_tree = InternalMenuService._build_menu_list(menu_list)
        
        return menu_tree

    @staticmethod
    def _build_menu_list(menu_list: List[SysInternalMenu]) -> List[Dict[str, Any]]:
        """
        构建菜单树形结构
        :param menu_list: 菜单列表
        :return: 菜单树形结构
        """
        # 转换为字典并构建树形结构
        menu_dict_list = []
        for menu in menu_list:
            menu_dict = SqlalchemyUtil.base_to_dict(menu, 'snake_to_camel')
            menu_dict_list.append(menu_dict)
        
        # 构建菜单树
        return InternalMenuService._build_tree(menu_dict_list)
    
    @staticmethod
    def _build_tree(menu_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        构建树形结构
        :param menu_list: 菜单列表
        :return: 树形结构
        """
        # 创建根节点map
        root_menu = []
        menu_map = {menu['menuId']: menu for menu in menu_list}
        
        # 遍历所有菜单
        for menu in menu_list:
            parent_id = menu.get('parentId', 0)
            # 如果有父节点，添加到父节点的children中
            if parent_id != 0 and parent_id in menu_map:
                parent = menu_map[parent_id]
                if 'children' not in parent:
                    parent['children'] = []
                parent['children'].append(menu)
            # 否则，添加到根节点中
            elif parent_id == 0:
                root_menu.append(menu)
        
        return root_menu
    
    @staticmethod
    async def menu_detail_services(query_db: AsyncSession, menu_id: int) -> InternalMenuModel:
        """
        获取内部菜单详细信息
        :param query_db: 数据库会话
        :param menu_id: 菜单ID
        :return: 内部菜单信息
        """
        # 查询菜单信息
        result = await query_db.execute(
            select(SysInternalMenu).where(
                SysInternalMenu.menu_id == menu_id
            )
        )
        menu = result.scalar_one_or_none()
        if not menu:
            return None

        # 构建返回数据
        menu_dict = SqlalchemyUtil.base_to_dict(menu, 'snake_to_camel')
        return InternalMenuModel(**menu_dict)

    @staticmethod
    async def add_menu_services(query_db: AsyncSession, add_menu: InternalMenuModel) -> ResponseUtil:
        """
        新增内部菜单
        :param query_db: 数据库会话
        :param add_menu: 新增菜单信息
        :return: 响应结果
        """
        # 检查菜单是否已存在
        select_sql = select(SysInternalMenu).where(
            and_(
                SysInternalMenu.menu_name == add_menu.menuName,
                SysInternalMenu.parent_id == add_menu.parentId,
                SysInternalMenu.client_type == add_menu.clientType
            )
        )
        result = await query_db.execute(select_sql)
        if result.scalar_one_or_none():
            return ResponseUtil.error(msg=f"新增菜单'{add_menu.menuName}'失败，菜单名称已存在")
        
        # 新增菜单
        new_menu = SysInternalMenu(
            parent_id=add_menu.parentId,
            menu_name=add_menu.menuName,
            menu_type=add_menu.menuType,
            client_type=add_menu.clientType,
            icon=add_menu.icon,
            order_num=add_menu.orderNum,
            path=add_menu.path,
            component=add_menu.component,
            is_new=add_menu.isNew,
            is_cache=add_menu.isCache,
            visible=add_menu.visible,
            status=add_menu.status,
            perms=add_menu.perms,
            create_time=add_menu.createTime,
            update_time=add_menu.updateTime,
            remark=add_menu.remark
        )
        query_db.add(new_menu)
        await query_db.commit()
        
        return ResponseUtil.success(msg="新增成功")

    @staticmethod
    async def edit_menu_services(query_db: AsyncSession, edit_menu: InternalMenuModel) -> ResponseUtil:
        """
        修改内部菜单
        :param query_db: 数据库会话
        :param edit_menu: 修改菜单信息
        :return: 响应结果
        """
        # 检查菜单是否存在
        result = await query_db.execute(
            select(SysInternalMenu).where(
                SysInternalMenu.menu_id == edit_menu.menuId
            )
        )
        menu = result.scalar_one_or_none()
        if not menu:
            return ResponseUtil.error(msg=f"修改菜单失败，菜单不存在")
        
        # 检查菜单名称是否重复
        select_sql = select(SysInternalMenu).where(
            and_(
                SysInternalMenu.menu_name == edit_menu.menuName,
                SysInternalMenu.parent_id == edit_menu.parentId,
                SysInternalMenu.client_type == edit_menu.clientType,
                SysInternalMenu.menu_id != edit_menu.menuId
            )
        )
        result = await query_db.execute(select_sql)
        if result.scalar_one_or_none():
            return ResponseUtil.error(msg=f"修改菜单'{edit_menu.menuName}'失败，菜单名称已存在")
        
        # 不能将自己设为父菜单
        if edit_menu.menuId == edit_menu.parentId:
            return ResponseUtil.error(msg="修改菜单失败，不能选择自己作为父菜单")
        
        # 修改菜单
        menu.parent_id = edit_menu.parentId
        menu.menu_name = edit_menu.menuName
        menu.menu_type = edit_menu.menuType
        menu.client_type = edit_menu.clientType
        menu.icon = edit_menu.icon
        menu.order_num = edit_menu.orderNum
        menu.path = edit_menu.path
        menu.component = edit_menu.component
        menu.is_new = edit_menu.isNew
        menu.is_cache = edit_menu.isCache
        menu.visible = edit_menu.visible
        menu.status = edit_menu.status
        menu.perms = edit_menu.perms
        menu.update_time = edit_menu.updateTime
        menu.remark = edit_menu.remark
        
        await query_db.commit()
        
        return ResponseUtil.success(msg="修改成功")

    @staticmethod
    async def delete_menu_services(query_db: AsyncSession, delete_menu: DeleteInternalMenuModel) -> ResponseUtil:
        """
        删除内部菜单
        :param query_db: 数据库会话
        :param delete_menu: 删除菜单信息
        :return: 响应结果
        """
        menu_ids = delete_menu.menuIds.split(",")
        for menu_id in menu_ids:
            menu_id = int(menu_id)
            
            # 检查是否有子菜单
            result = await query_db.execute(
                select(SysInternalMenu).where(
                    SysInternalMenu.parent_id == menu_id
                )
            )
            if result.scalar_one_or_none():
                return ResponseUtil.error(msg="存在子菜单，不允许删除")
            
            # 检查菜单是否已分配给角色
            result = await query_db.execute(
                select(SysInternalRoleMenu).where(
                    SysInternalRoleMenu.menu_id == menu_id
                )
            )
            if result.scalar_one_or_none():
                return ResponseUtil.error(msg="菜单已分配给角色，不允许删除")
            
            # 删除菜单
            await query_db.execute(
                SysInternalMenu.__table__.delete().where(
                    SysInternalMenu.menu_id == menu_id
                )
            )
        
        await query_db.commit()
        
        return ResponseUtil.success(msg="删除成功")
    
    @staticmethod
    async def get_menu_tree_services(
        query_db: AsyncSession, 
        client_type: Optional[int], 
        current_user: CurrentUserModel
    ) -> List[InternalMenuTreeSelectModel]:
        """
        获取内部菜单树
        :param query_db: 数据库会话
        :param client_type: 客户端类型
        :param current_user: 当前用户
        :return: 内部菜单树
        """
        # 构建查询条件
        where_conditions = []
        if client_type is not None:
            where_conditions.append(SysInternalMenu.client_type == client_type)
            
        # 执行查询
        select_sql = select(SysInternalMenu).where(and_(*where_conditions)).order_by(
            SysInternalMenu.parent_id, SysInternalMenu.order_num
        )
        result = await query_db.execute(select_sql)
        menu_list = result.scalars().all()
        
        # 构建树选择数据
        menu_tree = []
        for menu in menu_list:
            menu_dict = {
                "id": menu.menu_id,
                "label": menu.menu_name,
                "parentId": menu.parent_id
            }
            menu_tree.append(menu_dict)
        
        # 构建树结构
        return InternalMenuService._build_menu_tree(menu_tree)
    
    @staticmethod
    def _build_menu_tree(menu_list: List[Dict[str, Any]]) -> List[InternalMenuTreeSelectModel]:
        """
        构建菜单树结构
        :param menu_list: 菜单列表
        :return: 菜单树结构
        """
        # 创建根节点map
        root_menu = []
        menu_map = {menu['id']: menu for menu in menu_list}
        
        # 遍历所有菜单
        for menu in menu_list:
            parent_id = menu.get('parentId', 0)
            # 如果有父节点，添加到父节点的children中
            if parent_id != 0 and parent_id in menu_map:
                parent = menu_map[parent_id]
                if 'children' not in parent:
                    parent['children'] = []
                parent['children'].append(menu)
            # 否则，添加到根节点中
            elif parent_id == 0:
                root_menu.append(menu)
        
        # 转换为InternalMenuTreeSelectModel类型
        return [InternalMenuTreeSelectModel(**menu) for menu in root_menu]
    
    @staticmethod
    async def get_role_menu_tree_services(
        query_db: AsyncSession, 
        role_id: int, 
        client_type: Optional[int],
        current_user: CurrentUserModel
    ) -> InternalRoleMenuTreeSelectModel:
        """
        获取角色的菜单树
        :param query_db: 数据库会话
        :param role_id: 角色ID
        :param client_type: 客户端类型
        :param current_user: 当前用户
        :return: 角色菜单树
        """
        # 获取菜单树
        menu_tree = await InternalMenuService.get_menu_tree_services(query_db, client_type, current_user)
        
        # 获取角色已分配的菜单
        result = await query_db.execute(
            select(SysInternalRoleMenu.menu_id).where(
                SysInternalRoleMenu.role_id == role_id
            )
        )
        checked_keys = result.scalars().all()
        
        return InternalRoleMenuTreeSelectModel(
            menus=menu_tree,
            checkedKeys=checked_keys
        ) 