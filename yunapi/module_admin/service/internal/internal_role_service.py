from datetime import datetime
from typing import List, Optional
from sqlalchemy import select, and_, or_, func
from sqlalchemy.ext.asyncio import AsyncSession
from module_admin.entity.vo.internal_role_vo import InternalRoleModel, InternalRolePageQueryModel, DeleteInternalRoleModel
from module_admin.entity.do.sys_internal_role import SysInternalRole
from module_admin.entity.do.sys_internal_role_menu import SysInternalRoleMenu
from module_admin.entity.do.sys_internal_menu import SysInternalMenu
from utils.common_util import CamelCaseUtil, SqlalchemyUtil
from utils.page_util import PageResponseModel
from utils.response_util import ResponseUtil


class InternalRoleService:
    """内部角色管理服务"""

    @staticmethod
    async def get_role_list_services(
        query_db: AsyncSession,
        query_params: InternalRolePageQueryModel,
        is_page: bool = True
    ) -> PageResponseModel:
        """
        获取内部角色列表
        :param query_db: 数据库会话
        :param query_params: 查询参数
        :param is_page: 是否分页
        :return: 内部角色列表
        """
        # 构建查询条件
        where_conditions = []
        if query_params.roleName:
            where_conditions.append(SysInternalRole.role_name.like(f"%{query_params.roleName}%"))
        if query_params.roleKey:
            where_conditions.append(SysInternalRole.role_key.like(f"%{query_params.roleKey}%"))
        if query_params.status:
            where_conditions.append(SysInternalRole.status == query_params.status)
        if query_params.beginTime and query_params.endTime:
            where_conditions.append(
                and_(
                    SysInternalRole.create_time >= datetime.strptime(query_params.beginTime, "%Y-%m-%d"),
                    SysInternalRole.create_time <= datetime.strptime(query_params.endTime, "%Y-%m-%d")
                )
            )

        # 构建查询语句
        select_sql = select(SysInternalRole).where(and_(*where_conditions))
        if is_page:
            # 获取总记录数
            count_sql = select(func.count()).select_from(SysInternalRole).where(and_(*where_conditions))
            result = await query_db.execute(count_sql)
            total = result.scalar() or 0
            # 分页查询
            select_sql = select_sql.offset((query_params.pageNum - 1) * query_params.pageSize).limit(query_params.pageSize)

        # 执行查询
        result = await query_db.execute(select_sql)
        role_list = result.scalars().all()

        # 构建返回数据
        rows = []
        for role in role_list:
            role_dict = SqlalchemyUtil.base_to_dict(role, 'snake_to_camel')
            rows.append(role_dict)

        return PageResponseModel(
            total=total if is_page else len(rows),
            rows=rows
        )

    @staticmethod
    async def role_detail_services(query_db: AsyncSession, role_id: int) -> InternalRoleModel:
        """
        获取内部角色详细信息
        :param query_db: 数据库会话
        :param role_id: 角色ID
        :return: 内部角色信息
        """
        # 查询角色信息
        result = await query_db.execute(
            select(SysInternalRole).where(
                SysInternalRole.role_id == role_id
            )
        )
        role = result.scalar_one_or_none()
        if not role:
            return None

        # 查询角色菜单关联
        result = await query_db.execute(
            select(SysInternalRoleMenu.menu_id).where(
                SysInternalRoleMenu.role_id == role_id
            )
        )
        menu_ids = result.scalars().all()

        # 构建返回数据
        role_dict = SqlalchemyUtil.base_to_dict(role, 'snake_to_camel')
        role_dict["menuIds"] = menu_ids
        return InternalRoleModel(**role_dict)

    @staticmethod
    async def add_role_services(query_db: AsyncSession, add_role: InternalRoleModel) -> ResponseUtil:
        """
        新增内部角色
        :param query_db: 数据库会话
        :param add_role: 新增角色信息
        :return: 响应结果
        """
        # 检查角色名称是否唯一
        result = await query_db.execute(
            select(SysInternalRole).where(
                SysInternalRole.role_name == add_role.roleName
            )
        )
        if result.scalar_one_or_none():
            return ResponseUtil.error(msg="新增角色'" + add_role.roleName + "'失败，角色名称已存在")

        # 新增角色
        new_role = SysInternalRole(
            parent_id=add_role.parentId,
            role_name=add_role.roleName,
            role_key=add_role.roleKey,
            order_num=add_role.orderNum,
            status=add_role.status,
            create_time=add_role.createTime,
            update_time=add_role.updateTime,
            remark=add_role.remark
        )
        query_db.add(new_role)
        await query_db.commit()

        return ResponseUtil.success(msg="新增成功")

    @staticmethod
    async def edit_role_services(query_db: AsyncSession, edit_role: InternalRoleModel) -> ResponseUtil:
        """
        修改内部角色
        :param query_db: 数据库会话
        :param edit_role: 修改角色信息
        :return: 响应结果
        """
        # 检查角色是否存在
        result = await query_db.execute(
            select(SysInternalRole).where(
                SysInternalRole.role_id == edit_role.roleId
            )
        )
        role = result.scalar_one_or_none()
        if not role:
            return ResponseUtil.error(msg="修改角色'" + edit_role.roleName + "'失败，角色不存在")

        # 检查角色名称是否唯一
        result = await query_db.execute(
            select(SysInternalRole).where(
                and_(
                    SysInternalRole.role_name == edit_role.roleName,
                    SysInternalRole.role_id != edit_role.roleId
                )
            )
        )
        if result.scalar_one_or_none():
            return ResponseUtil.error(msg="修改角色'" + edit_role.roleName + "'失败，角色名称已存在")

        # 修改角色
        role.parent_id = edit_role.parentId
        role.role_name = edit_role.roleName
        role.role_key = edit_role.roleKey
        role.order_num = edit_role.orderNum
        role.status = edit_role.status
        role.update_time = edit_role.updateTime
        role.remark = edit_role.remark

        # 修改角色菜单关联关系
        # 先删除原有关联
        await query_db.execute(
            SysInternalRoleMenu.__table__.delete().where(
                SysInternalRoleMenu.role_id == edit_role.roleId
            )
        )
        
        # 添加新的关联关系
        if edit_role.menuIds:
            for menu_id in edit_role.menuIds:
                role_menu = SysInternalRoleMenu(
                    role_id=edit_role.roleId,
                    menu_id=menu_id
                )
                query_db.add(role_menu)
        
        await query_db.commit()

        return ResponseUtil.success(msg="修改成功")

    @staticmethod
    async def delete_role_services(query_db: AsyncSession, delete_role: DeleteInternalRoleModel) -> ResponseUtil:
        """
        删除内部角色
        :param query_db: 数据库会话
        :param delete_role: 删除角色信息
        :return: 响应结果
        """
        role_ids = delete_role.roleIds.split(",")
        for role_id in role_ids:
            # 检查角色是否存在
            result = await query_db.execute(
                select(SysInternalRole).where(
                    SysInternalRole.role_id == role_id
                )
            )
            role = result.scalar_one_or_none()
            if not role:
                return ResponseUtil.error(msg="删除角色失败，角色不存在")

            # 检查是否存在子角色
            result = await query_db.execute(
                select(SysInternalRole).where(
                    SysInternalRole.parent_id == role_id
                )
            )
            if result.scalar_one_or_none():
                return ResponseUtil.error(msg="存在下级角色,不允许删除")

            # 先删除角色菜单关联关系
            await query_db.execute(
                SysInternalRoleMenu.__table__.delete().where(
                    SysInternalRoleMenu.role_id == role_id
                )
            )

            # 实际删除角色记录
            await query_db.execute(
                SysInternalRole.__table__.delete().where(
                    SysInternalRole.role_id == role_id
                )
            )

        await query_db.commit()

        return ResponseUtil.success(msg="删除成功")

    @staticmethod
    async def check_role_allowed_services(role: InternalRoleModel) -> None:
        """
        校验角色是否允许操作
        :param role: 角色信息
        :return: None
        """
        if role.roleId == 1:
            raise ValueError("不允许操作超级管理员角色")
            
    @staticmethod
    async def update_role_status_service(query_db: AsyncSession, role_model: InternalRoleModel) -> ResponseUtil:
        """
        更新角色状态
        :param query_db: 数据库会话
        :param role_model: 角色信息（含roleId和status）
        :return: 响应结果
        """
        # 检查角色是否存在
        result = await query_db.execute(
            select(SysInternalRole).where(
                SysInternalRole.role_id == role_model.roleId
            )
        )
        role = result.scalar_one_or_none()
        if not role:
            return ResponseUtil.error(msg=f"角色不存在")
        
        # 检查是否允许操作该角色
        try:
            await InternalRoleService.check_role_allowed_services(role_model)
        except ValueError as e:
            return ResponseUtil.error(msg=str(e))
        
        # 更新角色状态
        role.status = role_model.status
        role.update_time = role_model.updateTime
        
        await query_db.commit()
        
        return ResponseUtil.success(msg="状态修改成功") 