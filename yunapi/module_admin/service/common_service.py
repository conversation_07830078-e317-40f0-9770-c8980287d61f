import os
from datetime import datetime
from typing import List, Dict, Any
from fastapi import BackgroundTasks, Request, UploadFile
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession
from config.env import UploadConfig, OssConfig
from exceptions.exception import ServiceException
from module_admin.entity.vo.common_vo import CrudResponseModel, UploadResponseModel
from utils.upload_util import UploadUtil
from utils.oss_util import OssUtil
from utils.log_util import logger


class CommonService:
    """
    通用模块服务层
    """

    @classmethod
    async def upload_service(cls, request: Request, file: UploadFile):
        """
        通用上传service - 使用阿里云OSS存储

        :param request: Request对象
        :param file: 上传文件对象
        :return: 上传结果
        """
        # 检查文件类型
        if not UploadUtil.check_file_extension(file):
            raise ServiceException(message='文件类型不合法')

        try:
            # 上传文件到阿里云OSS
            object_key, file_url = await OssUtil.upload_file_to_oss(file)

            # 从对象键中提取文件名
            filename = object_key.split('/')[-1]

            return CrudResponseModel(
                is_success=True,
                result=UploadResponseModel(
                    fileName=object_key,  # OSS对象键
                    newFileName=filename,  # 新文件名
                    originalFilename=file.filename,  # 原始文件名
                    url=file_url,  # 完整的OSS访问URL
                ),
                message='上传成功',
            )

        except ServiceException as e:
            # 重新抛出服务异常
            raise e
        except Exception as e:
            logger.error(f"文件上传失败: {str(e)}")
            raise ServiceException(message='文件上传失败，请重试')

    @classmethod
    async def download_services(cls, background_tasks: BackgroundTasks, file_name, delete: bool):
        """
        下载下载目录文件service

        :param background_tasks: 后台任务对象
        :param file_name: 下载的文件名称
        :param delete: 是否在下载完成后删除文件
        :return: 上传结果
        """
        filepath = os.path.join(UploadConfig.DOWNLOAD_PATH, file_name)
        if '..' in file_name:
            raise ServiceException(message='文件名称不合法')
        elif not UploadUtil.check_file_exists(filepath):
            raise ServiceException(message='文件不存在')
        else:
            if delete:
                background_tasks.add_task(UploadUtil.delete_file, filepath)
            return CrudResponseModel(is_success=True, result=UploadUtil.generate_file(filepath), message='下载成功')

    @classmethod
    async def download_resource_services(cls, resource: str):
        """
        下载上传目录文件service

        :param resource: 下载的文件名称
        :return: 上传结果
        """
        filepath = os.path.join(resource.replace(UploadConfig.UPLOAD_PREFIX, UploadConfig.UPLOAD_PATH))
        filename = resource.rsplit('/', 1)[-1]
        if (
            '..' in filename
            or not UploadUtil.check_file_timestamp(filename)
            or not UploadUtil.check_file_machine(filename)
            or not UploadUtil.check_file_random_code(filename)
        ):
            raise ServiceException(message='文件名称不合法')
        elif not UploadUtil.check_file_exists(filepath):
            raise ServiceException(message='文件不存在')
        else:
            return CrudResponseModel(is_success=True, result=UploadUtil.generate_file(filepath), message='下载成功')

    @classmethod
    async def get_file_list_by_main_id(cls, db: AsyncSession, main_id: int) -> List[Dict[str, Any]]:
        """
        根据文件主表ID获取文件列表，返回格式使用驼峰命名法

        此方法适用于业务代码中直接调用，用于获取文件列表

        :param db: 数据库会话
        :param main_id: 文件主表ID
        :return: 文件列表，每个文件包含id, mainId, fileName, fileType, fileSize, url, createTime等信息
        """
        try:
            # 查询文件项
            sql = """
                SELECT
                    id,
                    main_id,
                    file_name,
                    file_type,
                    file_size,
                    file_url,
                    created_at
                FROM file_item
                WHERE main_id = :main_id
                ORDER BY created_at ASC
            """

            result = await db.execute(text(sql), {"main_id": main_id})

            file_items = []
            for row in result.fetchall():
                file_items.append({
                    "id": row[0],
                    "mainId": row[1],
                    "fileName": row[2],
                    "fileType": row[3],
                    "fileSize": row[4],
                    "url": row[5],
                    "createdAt": row[6].strftime("%Y-%m-%d %H:%M:%S") if row[6] else None
                })

            return file_items

        except Exception as e:
            logger.error(f"获取文件列表失败: {str(e)}")
            return []
