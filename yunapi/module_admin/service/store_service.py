from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from exceptions.exception import ServiceException
from module_admin.dao.store_dao import StoreDao
from module_admin.entity.do.store_do import Store
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.vo.store_vo import StoreModel, StorePageQueryModel, DeleteStoreModel
from utils.common_util import CommonUtil


class StoreService:
    """
    门店管理模块服务层
    """

    @classmethod
    async def get_store_list_services(cls, query_db: AsyncSession, query_object: StorePageQueryModel, is_page: bool = False):
        """
        获取门店列表信息service

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 门店列表信息对象
        """
        store_list_result = await StoreDao.get_store_list(query_db, query_object, is_page)
        return store_list_result

    @classmethod
    async def store_detail_services(cls, query_db: AsyncSession, store_id: int):
        """
        获取门店详细信息service

        :param query_db: orm对象
        :param store_id: 门店id
        :return: 门店信息对象
        """
        store = await StoreDao.get_store_detail_by_id(query_db, store_id)
        if store:
            return store
        else:
            raise ServiceException(message='门店不存在')

    @classmethod
    async def add_store_services(cls, query_db: AsyncSession, add_store: StoreModel):
        """
        新增门店信息service

        :param query_db: orm对象
        :param add_store: 新增门店对象
        :return: 新增门店校验结果
        """
        # 检查门店名称是否已存在
        existing_store = await StoreDao.get_store_detail_by_name(query_db, add_store.name)
        if existing_store:
            raise ServiceException(message='门店名称已存在')
        
        # 生成门店UUID
        if not add_store.store_uuid:
            add_store.store_uuid = CommonUtil.get_uuid_without_hyphen()
        
        # 设置创建时间
        add_store.create_time = datetime.now()
        add_store.update_time = datetime.now()
        
        # 转换为字典
        store_data = add_store.model_dump(exclude_unset=True)
        
        try:
            await StoreDao.add_store_dao(query_db, store_data)
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='新增成功')
        except Exception as e:
            await query_db.rollback()
            raise ServiceException(message=f'新增失败: {str(e)}')

    @classmethod
    async def edit_store_services(cls, query_db: AsyncSession, edit_store: StoreModel):
        """
        编辑门店信息service

        :param query_db: orm对象
        :param edit_store: 编辑门店对象
        :return: 编辑门店校验结果
        """
        # 检查门店是否存在
        existing_store = await StoreDao.get_store_detail_by_id(query_db, edit_store.id)
        if not existing_store:
            raise ServiceException(message='门店不存在')
        
        # 检查门店名称是否与其他门店重复
        name_check_store = await StoreDao.get_store_detail_by_name(query_db, edit_store.name)
        if name_check_store and name_check_store.id != edit_store.id:
            raise ServiceException(message='门店名称已存在')
        
        # 更新字段
        for key, value in edit_store.model_dump(exclude_unset=True, exclude={'id'}).items():
            if hasattr(existing_store, key) and value is not None:
                setattr(existing_store, key, value)
        
        # 设置更新时间
        existing_store.update_time = datetime.now()
        
        try:
            await StoreDao.edit_store_dao(query_db, existing_store)
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='更新成功')
        except Exception as e:
            await query_db.rollback()
            raise ServiceException(message=f'更新失败: {str(e)}')

    @classmethod
    async def delete_store_services(cls, query_db: AsyncSession, delete_store: DeleteStoreModel):
        """
        删除门店信息service

        :param query_db: orm对象
        :param delete_store: 删除门店对象
        :return: 删除门店校验结果
        """
        if delete_store.ids:
            store_id_list = delete_store.ids.split(',')
            try:
                await StoreDao.delete_store_dao(query_db, delete_store)
                await query_db.commit()
                return CrudResponseModel(is_success=True, message='删除成功')
            except Exception as e:
                await query_db.rollback()
                raise ServiceException(message=f'删除失败: {str(e)}')
        else:
            raise ServiceException(message='传入门店id为空')
