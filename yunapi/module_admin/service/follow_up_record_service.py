from sqlalchemy.ext.asyncio import AsyncSession
from exceptions.exception import ServiceException
from module_admin.dao.follow_up_record_dao import FollowUpRecordDao
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.vo.follow_up_record_vo import FollowUpRecordModel, FollowUpRecordPageQueryModel
from utils.common_util import CamelCaseUtil


class FollowUpRecordService:
    """
    跟进记录模块服务层
    """

    @classmethod
    async def get_follow_up_records_by_merchant_id_services(
        cls, query_db: AsyncSession, merchant_id: int, query_object: FollowUpRecordPageQueryModel = None, is_page: bool = False
    ):
        """
        获取商户跟进记录列表service

        :param query_db: orm对象
        :param merchant_id: 商户ID
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 跟进记录列表信息对象
        """
        follow_up_record_list_result = await FollowUpRecordDao.get_follow_up_records_by_merchant_id(
            query_db, merchant_id, query_object, is_page
        )

        return follow_up_record_list_result

    @classmethod
    async def add_follow_up_record_services(cls, query_db: AsyncSession, page_object: FollowUpRecordModel):
        """
        新增跟进记录service

        :param query_db: orm对象
        :param page_object: 新增跟进记录对象
        :return: 新增跟进记录校验结果
        """
        try:
            await FollowUpRecordDao.add_follow_up_record_dao(query_db, page_object)
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='新增成功')
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    async def edit_follow_up_record_services(cls, query_db: AsyncSession, page_object: FollowUpRecordModel):
        """
        编辑跟进记录service

        :param query_db: orm对象
        :param page_object: 编辑跟进记录对象
        :return: 编辑跟进记录校验结果
        """
        if page_object.id:
            try:
                follow_up_record_dict = page_object.model_dump(exclude_none=True)
                await FollowUpRecordDao.edit_follow_up_record_dao(query_db, follow_up_record_dict)
                await query_db.commit()
                return CrudResponseModel(is_success=True, message='修改成功')
            except Exception as e:
                await query_db.rollback()
                raise e
        else:
            raise ServiceException(message='传入跟进记录ID为空')

    @classmethod
    async def follow_up_record_detail_services(cls, query_db: AsyncSession, id: int):
        """
        获取跟进记录详细信息service

        :param query_db: orm对象
        :param id: 跟进记录ID
        :return: 跟进记录ID对应的信息
        """
        follow_up_record = await FollowUpRecordDao.get_follow_up_record_detail_by_id(query_db, id=id)
        if follow_up_record:
            result = FollowUpRecordModel(**CamelCaseUtil.transform_result(follow_up_record))
        else:
            result = FollowUpRecordModel(**dict())

        return result
