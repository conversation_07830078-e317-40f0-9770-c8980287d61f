import uuid
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from module_admin.dao.ccuser_dao import CcuserDao
from module_admin.entity.vo.ccuser_vo import CcuserModel, CcuserPageQueryModel, DeleteCcuserModel
from module_admin.entity.vo.common_vo import CrudResponseModel
from exceptions.exception import ServiceException


class CcuserService:
    """
    客户管理模块服务层
    """

    @classmethod
    async def get_ccuser_list_services(cls, query_db: AsyncSession, query_object: CcuserPageQueryModel, is_page: bool = False):
        """
        获取客户列表信息service

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 客户列表信息对象
        """
        ccuser_list_result = await CcuserDao.get_ccuser_list(query_db, query_object, is_page)

        return ccuser_list_result

    @classmethod
    async def get_ccuser_detail_services(cls, query_db: AsyncSession, ccuser_id: int):
        """
        获取客户详细信息service

        :param query_db: orm对象
        :param ccuser_id: 客户id
        :return: 客户详细信息对象
        """
        ccuser = await CcuserDao.get_ccuser_detail_by_id(query_db, ccuser_id)
        if ccuser:
            result = CcuserModel(**ccuser.__dict__)
            return result
        return None

    @classmethod
    async def add_ccuser_services(cls, query_db: AsyncSession, add_ccuser: CcuserModel):
        """
        新增客户信息service

        :param query_db: orm对象
        :param add_ccuser: 新增客户对象
        :return: 新增客户校验结果
        """
        # 检查手机号是否已存在
        if add_ccuser.mobile:
            existing_ccuser = await CcuserDao.get_ccuser_detail_by_mobile(query_db, add_ccuser.mobile)
            if existing_ccuser:
                raise ServiceException(message='手机号已存在')

        # 生成客户UUID
        if not add_ccuser.uuid:
            add_ccuser.uuid = str(uuid.uuid4())

        # 设置创建时间
        current_time = datetime.now()
        add_ccuser.create_time = current_time
        add_ccuser.created_at = current_time
        add_ccuser.create_time_stamp = int(current_time.timestamp())

        try:
            await CcuserDao.add_ccuser_dao(query_db, add_ccuser.model_dump())
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='新增成功')
        except Exception as e:
            await query_db.rollback()
            raise ServiceException(message=f'新增失败: {str(e)}')

    @classmethod
    async def edit_ccuser_services(cls, query_db: AsyncSession, edit_ccuser: CcuserModel):
        """
        编辑客户信息service

        :param query_db: orm对象
        :param edit_ccuser: 编辑客户对象
        :return: 编辑客户校验结果
        """
        edit_ccuser.update_time = datetime.now()
        edit_ccuser.updated_at = datetime.now()

        query_ccuser = await CcuserDao.get_ccuser_detail_by_id(query_db, edit_ccuser.id)
        if query_ccuser:
            # 检查手机号是否被其他客户使用
            if edit_ccuser.mobile and edit_ccuser.mobile != query_ccuser.mobile:
                existing_ccuser = await CcuserDao.get_ccuser_detail_by_mobile(query_db, edit_ccuser.mobile)
                if existing_ccuser and existing_ccuser.id != edit_ccuser.id:
                    raise ServiceException(message='手机号已被其他客户使用')

            # 更新客户信息
            update_data = edit_ccuser.model_dump(exclude_none=True, by_alias=False)
            print(f"更新数据: {update_data}")  # 调试日志
            for key, value in update_data.items():
                if hasattr(query_ccuser, key):
                    old_value = getattr(query_ccuser, key)
                    setattr(query_ccuser, key, value)
                    print(f"更新字段 {key}: {old_value} -> {value}")  # 调试日志

            try:
                await CcuserDao.edit_ccuser_dao(query_db, query_ccuser)
                await query_db.commit()
                return CrudResponseModel(is_success=True, message='更新成功')
            except Exception as e:
                await query_db.rollback()
                raise ServiceException(message=f'更新失败: {str(e)}')
        else:
            raise ServiceException(message='客户不存在')

    @classmethod
    async def delete_ccuser_services(cls, query_db: AsyncSession, delete_ccuser: DeleteCcuserModel):
        """
        删除客户信息service

        :param query_db: orm对象
        :param delete_ccuser: 删除客户对象
        :return: 删除客户校验结果
        """
        if delete_ccuser.ids:
            ccuser_id_list = delete_ccuser.ids.split(',')
            try:
                await CcuserDao.delete_ccuser_dao(query_db, delete_ccuser)
                await query_db.commit()
                return CrudResponseModel(is_success=True, message='删除成功')
            except Exception as e:
                await query_db.rollback()
                raise ServiceException(message=f'删除失败: {str(e)}')
        else:
            raise ServiceException(message='传入客户id为空')
