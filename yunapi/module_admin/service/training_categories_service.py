from sqlalchemy.ext.asyncio import AsyncSession
from module_admin.dao.training_categories_dao import TrainingCategoriesDao
from module_admin.entity.vo.training_categories_vo import (
    TrainingCategoriesModel, 
    TrainingCategoriesPageQueryModel, 
    DeleteTrainingCategoriesModel,
    TrainingCategoriesTreeModel
)
from module_admin.entity.vo.common_vo import CrudResponseModel
from exceptions.exception import ServiceException
from utils.common_util import CommonUtil


class TrainingCategoriesService:
    """
    培训分类模块服务层
    """

    @classmethod
    async def get_training_categories_list_services(cls, query_db: AsyncSession, query_object: TrainingCategoriesPageQueryModel, is_page: bool = False):
        """
        获取培训分类列表信息service

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 培训分类列表信息对象
        """
        training_categories_list_result = await TrainingCategoriesDao.get_training_categories_list(query_db, query_object, is_page)
        return training_categories_list_result

    @classmethod
    async def training_categories_detail_services(cls, query_db: AsyncSession, category_id: str):
        """
        获取培训分类详细信息service

        :param query_db: orm对象
        :param category_id: 分类id
        :return: 培训分类信息对象
        """
        category = await TrainingCategoriesDao.get_training_categories_detail_by_id(query_db, category_id)
        if category:
            return category
        else:
            raise ServiceException(message='培训分类不存在')

    @classmethod
    async def add_training_categories_services(cls, query_db: AsyncSession, add_category: TrainingCategoriesModel):
        """
        新增培训分类信息service

        :param query_db: orm对象
        :param add_category: 新增分类对象
        :return: 新增分类校验结果
        """
        # 检查分类名称是否已存在
        existing_category = await TrainingCategoriesDao.get_training_categories_detail_by_name(query_db, add_category.name)
        if existing_category:
            raise ServiceException(message='分类名称已存在')
        
        # 生成分类ID和UUID
        if not add_category.id:
            add_category.id = await TrainingCategoriesDao.get_next_category_id(query_db)
        if not add_category.uuid:
            add_category.uuid = await TrainingCategoriesDao.get_next_category_uuid(query_db)
        
        try:
            await TrainingCategoriesDao.add_training_categories_dao(query_db, add_category.model_dump(exclude_unset=True))
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='新增成功')
        except Exception as e:
            await query_db.rollback()
            raise ServiceException(message=f'新增失败: {str(e)}')

    @classmethod
    async def edit_training_categories_services(cls, query_db: AsyncSession, edit_category: TrainingCategoriesModel):
        """
        编辑培训分类信息service

        :param query_db: orm对象
        :param edit_category: 编辑分类对象
        :return: 编辑分类校验结果
        """
        # 检查分类是否存在
        existing_category = await TrainingCategoriesDao.get_training_categories_detail_by_id(query_db, edit_category.id)
        if not existing_category:
            raise ServiceException(message='培训分类不存在')
        
        # 检查分类名称是否与其他分类重复
        name_check_category = await TrainingCategoriesDao.get_training_categories_detail_by_name(
            query_db, edit_category.name, edit_category.id
        )
        if name_check_category:
            raise ServiceException(message='分类名称已存在')
        
        # 更新字段
        for key, value in edit_category.model_dump(exclude_unset=True, exclude={'id'}).items():
            if hasattr(existing_category, key) and value is not None:
                setattr(existing_category, key, value)
        
        try:
            await TrainingCategoriesDao.edit_training_categories_dao(query_db, existing_category)
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='编辑成功')
        except Exception as e:
            await query_db.rollback()
            raise ServiceException(message=f'编辑失败: {str(e)}')

    @classmethod
    async def delete_training_categories_services(cls, query_db: AsyncSession, delete_category: DeleteTrainingCategoriesModel):
        """
        删除培训分类信息service

        :param query_db: orm对象
        :param delete_category: 删除分类对象
        :return: 删除分类校验结果
        """
        if delete_category.ids:
            category_id_list = delete_category.ids.split(',')
            
            # 检查是否有子分类
            for category_id in category_id_list:
                children = await TrainingCategoriesDao.get_children_categories(query_db, category_id)
                if children:
                    raise ServiceException(message=f'分类ID {category_id} 下存在子分类，无法删除')

                # 检查是否有课程使用该分类
                courses_count = await TrainingCategoriesDao.get_courses_count_by_category(query_db, category_id)
                if courses_count > 0:
                    raise ServiceException(message=f'分类ID {category_id} 下存在 {courses_count} 个课程，无法删除。请先删除或移动分类下的课程。')
            
            try:
                await TrainingCategoriesDao.delete_training_categories_dao(query_db, delete_category)
                await query_db.commit()
                return CrudResponseModel(is_success=True, message='删除成功')
            except Exception as e:
                await query_db.rollback()
                raise ServiceException(message=f'删除失败: {str(e)}')
        else:
            raise ServiceException(message='传入分类id为空')

    @classmethod
    async def get_training_categories_tree_services(cls, query_db: AsyncSession):
        """
        获取培训分类树形结构service

        :param query_db: orm对象
        :return: 培训分类树形结构
        """
        categories = await TrainingCategoriesDao.get_all_categories_for_tree(query_db)
        
        # 构建树形结构
        category_dict = {}
        tree_list = []
        
        # 先创建所有节点
        for category in categories:
            category_dict[category.id] = TrainingCategoriesTreeModel(
                id=category.id,
                uuid=category.uuid,
                name=category.name,
                description=category.description,
                icon=category.icon,
                parent_id=category.parent_id,
                sort_order=category.sort_order,
                status=category.status,
                create_time=category.create_time,
                update_time=category.update_time,
                children=[]
            )
        
        # 构建父子关系
        for category in categories:
            if category.parent_id and category.parent_id in category_dict:
                category_dict[category.parent_id].children.append(category_dict[category.id])
            else:
                tree_list.append(category_dict[category.id])
        
        return tree_list
