import json
from datetime import datetime
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession
from exceptions.exception import ServiceException
from module_admin.dao.company_withdrawal_dao import CompanyWithdrawalDao
from module_admin.dao.company_dao import CompanyDao
from module_admin.dao.company_transaction_dao import CompanyTransactionDao
from module_admin.entity.vo.company_withdrawal_vo import (
    CompanyWithdrawalPageQueryModel,
    CompanyWithdrawalCreateModel,
    CompanyWithdrawalReviewModel,
    CompanyWithdrawalProcessModel,
    CompanyWithdrawalResponseModel
)
from utils.common_util import CommonUtil, CamelCaseUtil


class CompanyWithdrawalService:
    """
    公司提现申请服务层
    """

    @classmethod
    async def get_company_withdrawal_list_services(
        cls,
        query_db: AsyncSession,
        query_object: CompanyWithdrawalPageQueryModel,
        is_page: bool = False
    ):
        """
        获取公司提现申请列表信息service

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 公司提现申请列表信息对象
        """
        withdrawal_list_result = await CompanyWithdrawalDao.get_company_withdrawal_list(
            query_db, query_object, is_page
        )

        # 获取所有涉及的公司UUID，用于批量获取公司名称
        company_uuids = set()

        if is_page:
            # 分页结果处理 - PageUtil已经转换为字典格式
            if withdrawal_list_result.rows:
                for item_dict in withdrawal_list_result.rows:
                    company_uuids.add(item_dict.get('companyUuid'))
        else:
            # 非分页结果处理 - 需要手动转换格式
            if withdrawal_list_result:
                for item in withdrawal_list_result:
                    company_uuids.add(item.company_uuid)

        # 批量获取公司名称映射
        company_name_mapping = {}
        for company_uuid in company_uuids:
            if company_uuid:
                company = await CompanyDao.get_company_by_id(query_db, company_uuid)
                if company:
                    company_name_mapping[company_uuid] = company.name

        if is_page:
            # 分页结果处理 - PageUtil已经转换为字典格式
            if withdrawal_list_result.rows:
                # 添加状态名称和公司名称
                for item_dict in withdrawal_list_result.rows:
                    item_dict['statusName'] = cls._get_status_name(item_dict.get('status'))
                    item_dict['withdrawalTypeName'] = cls._get_withdrawal_type_name(item_dict.get('withdrawalType'))
                    item_dict['companyName'] = company_name_mapping.get(item_dict.get('companyUuid'), '未知公司')
        else:
            # 非分页结果处理 - 需要手动转换格式
            if withdrawal_list_result:
                converted_data = []
                for item in withdrawal_list_result:
                    item_dict = CamelCaseUtil.transform_result([item])[0]
                    # 添加状态名称和公司名称
                    item_dict['statusName'] = cls._get_status_name(item_dict.get('status'))
                    item_dict['withdrawalTypeName'] = cls._get_withdrawal_type_name(item_dict.get('withdrawalType'))
                    item_dict['companyName'] = company_name_mapping.get(item_dict.get('companyUuid'), '未知公司')
                    converted_data.append(item_dict)
                withdrawal_list_result = converted_data

        return withdrawal_list_result

    @classmethod
    async def get_company_withdrawal_detail_services(cls, query_db: AsyncSession, withdrawal_id: int):
        """
        获取公司提现申请详情信息service

        :param query_db: orm对象
        :param withdrawal_id: 提现申请ID
        :return: 公司提现申请详情信息对象
        """
        withdrawal_detail = await CompanyWithdrawalDao.get_company_withdrawal_detail_by_id(query_db, withdrawal_id)

        if withdrawal_detail:
            # 转换数据格式
            detail_dict = CamelCaseUtil.transform_result([withdrawal_detail])[0]
            # 添加状态名称和提现类型名称
            detail_dict['statusName'] = cls._get_status_name(detail_dict.get('status'))
            detail_dict['withdrawalTypeName'] = cls._get_withdrawal_type_name(detail_dict.get('withdrawalType'))

            # 添加公司名称
            company_uuid = detail_dict.get('companyUuid')
            if company_uuid:
                company = await CompanyDao.get_company_by_id(query_db, company_uuid)
                detail_dict['companyName'] = company.name if company else '未知公司'

            return detail_dict

        return None

    @classmethod
    async def create_withdrawal_application_services(
        cls, 
        query_db: AsyncSession, 
        withdrawal_data: CompanyWithdrawalCreateModel, 
        current_user
    ):
        """
        创建提现申请service

        :param query_db: orm对象
        :param withdrawal_data: 提现申请数据
        :param current_user: 当前用户
        :return: 创建结果
        """
        try:
            # 1. 检查公司是否存在
            company = await CompanyDao.get_company_by_id(query_db, withdrawal_data.company_uuid)
            if not company:
                raise ServiceException(message='公司不存在')

            # 2. 检查公司余额是否足够
            current_balance = await CompanyDao.get_company_balance(query_db, withdrawal_data.company_uuid)
            if current_balance < withdrawal_data.apply_amount:
                raise ServiceException(message=f'余额不足，当前余额：{current_balance}，申请金额：{withdrawal_data.apply_amount}')

            # 3. 检查是否有待处理的提现申请
            pending_withdrawals = await CompanyWithdrawalDao.get_company_pending_withdrawals(
                query_db, withdrawal_data.company_uuid
            )
            if pending_withdrawals:
                raise ServiceException(message='存在待处理的提现申请，请等待处理完成后再申请')

            # 4. 计算手续费和实际到账金额
            fee_rate = Decimal('0.1') if withdrawal_data.withdrawal_type == 2 else Decimal('0')  # 零工提现10%手续费
            fee_amount = withdrawal_data.apply_amount * fee_rate
            actual_amount = withdrawal_data.apply_amount - fee_amount

            # 5. 生成提现申请单号
            withdrawal_no = f"WD{datetime.now().strftime('%Y%m%d%H%M%S')}{CommonUtil.get_short_uuid(6)}"

            # 6. 准备提现申请数据
            withdrawal_create_data = {
                'company_uuid': withdrawal_data.company_uuid,
                'withdrawal_no': withdrawal_no,
                'withdrawal_type': withdrawal_data.withdrawal_type,
                'withdrawal_type_name': cls._get_withdrawal_type_name(withdrawal_data.withdrawal_type),
                'apply_amount': withdrawal_data.apply_amount,
                'fee_rate': fee_rate,
                'fee_amount': fee_amount,
                'actual_amount': actual_amount,
                'bank_name': withdrawal_data.bank_name,
                'bank_account': withdrawal_data.bank_account,
                'account_holder': withdrawal_data.account_holder,
                'invoice_info': json.dumps(withdrawal_data.invoice_info) if withdrawal_data.invoice_info else None,
                'apply_reason': withdrawal_data.apply_reason,
                'status': 'PENDING',
                'applicant_id': current_user.user.user_id if current_user else 'system',
                'applicant_name': current_user.user.user_name if current_user else 'system',
                'apply_time': datetime.now(),
                'created_by': current_user.user.user_name if current_user else 'system',
                'remark': withdrawal_data.remark
            }

            # 7. 创建提现申请
            withdrawal = await CompanyWithdrawalDao.add_company_withdrawal_dao(query_db, withdrawal_create_data)

            # 8. 提交事务
            await query_db.commit()

            # 9. 返回创建结果
            return {
                'withdrawal_id': withdrawal.id,
                'withdrawal_no': withdrawal_no,
                'apply_amount': withdrawal_data.apply_amount,
                'fee_amount': fee_amount,
                'actual_amount': actual_amount,
                'status': 'PENDING'
            }

        except ServiceException:
            await query_db.rollback()
            raise
        except Exception as e:
            await query_db.rollback()
            raise ServiceException(message=f'创建提现申请失败: {str(e)}')

    @classmethod
    async def review_withdrawal_application_services(
        cls,
        query_db: AsyncSession,
        withdrawal_id: int,
        review_data: CompanyWithdrawalReviewModel,
        current_user
    ):
        """
        审核提现申请service

        :param query_db: orm对象
        :param withdrawal_id: 提现申请ID
        :param review_data: 审核数据
        :param current_user: 当前用户
        :return: 审核结果
        """
        try:
            # 1. 获取提现申请
            withdrawal = await CompanyWithdrawalDao.get_company_withdrawal_detail_by_id(query_db, withdrawal_id)
            if not withdrawal:
                raise ServiceException(message='提现申请不存在')

            # 2. 检查状态是否可以审核
            if withdrawal.status != 'PENDING':
                raise ServiceException(message=f'当前状态({cls._get_status_name(withdrawal.status)})不允许审核')

            # 3. 准备更新数据
            new_status = 'APPROVED' if review_data.action == 'approve' else 'REJECTED'
            update_data = {
                'reviewer_id': current_user.user.user_id if current_user else 'system',
                'reviewer_name': current_user.user.user_name if current_user else 'system',
                'review_time': datetime.now(),
                'review_comment': review_data.review_comment,
                'updated_by': current_user.user.user_name if current_user else 'system'
            }

            # 4. 更新提现申请状态
            success = await CompanyWithdrawalDao.update_withdrawal_status(
                query_db, withdrawal_id, new_status, update_data
            )

            if not success:
                raise ServiceException(message='审核失败')

            # 5. 提交事务
            await query_db.commit()

            return {
                'withdrawal_id': withdrawal_id,
                'status': new_status,
                'status_name': cls._get_status_name(new_status),
                'review_time': update_data['review_time']
            }

        except ServiceException:
            await query_db.rollback()
            raise
        except Exception as e:
            await query_db.rollback()
            raise ServiceException(message=f'审核提现申请失败: {str(e)}')

    @classmethod
    async def process_withdrawal_application_services(
        cls,
        query_db: AsyncSession,
        withdrawal_id: int,
        process_data: CompanyWithdrawalProcessModel,
        current_user
    ):
        """
        处理提现申请service

        :param query_db: orm对象
        :param withdrawal_id: 提现申请ID
        :param process_data: 处理数据
        :param current_user: 当前用户
        :return: 处理结果
        """
        try:
            # 1. 获取提现申请
            withdrawal = await CompanyWithdrawalDao.get_company_withdrawal_detail_by_id(query_db, withdrawal_id)
            if not withdrawal:
                raise ServiceException(message='提现申请不存在')

            # 2. 检查状态是否可以处理
            if withdrawal.status not in ['APPROVED', 'PROCESSING']:
                raise ServiceException(message=f'当前状态({cls._get_status_name(withdrawal.status)})不允许处理')

            # 3. 检查公司余额是否足够
            current_balance = await CompanyDao.get_company_balance(query_db, withdrawal.company_uuid)
            if current_balance < withdrawal.apply_amount:
                raise ServiceException(message=f'余额不足，当前余额：{current_balance}，申请金额：{withdrawal.apply_amount}')

            # 4. 扣减公司余额
            new_balance = current_balance - withdrawal.apply_amount
            await CompanyDao.update_company_balance(query_db, withdrawal.company_uuid, new_balance)

            # 5. 记录资金流水
            transaction_no = f"TXN{datetime.now().strftime('%Y%m%d%H%M%S')}{CommonUtil.get_short_uuid(6)}"
            transaction_data = {
                'company_uuid': withdrawal.company_uuid,
                'transaction_no': transaction_no,
                'business_type': 'WITHDRAWAL',
                'business_type_name': '提现',
                'transaction_type': 2,  # 2:支出
                'amount': withdrawal.apply_amount,
                'balance_before': current_balance,
                'balance_after': new_balance,
                'related_order_no': withdrawal.withdrawal_no,
                'pay_type': 'WITHDRAWAL',
                'operator_id': current_user.user.user_id if current_user else 'system',
                'operator_name': current_user.user.user_name if current_user else 'system',
                'description': f'提现申请，申请单号：{withdrawal.withdrawal_no}',
                'remark': f'提现类型：{withdrawal.withdrawal_type_name}，手续费：{withdrawal.fee_amount}，实际到账：{withdrawal.actual_amount}',
                'transaction_status': 'SUCCESS',
                'created_by': current_user.user.user_name if current_user else 'system'
            }

            await CompanyTransactionDao.add_company_transaction_dao(query_db, transaction_data)

            # 6. 更新提现申请状态为已完成
            update_data = {
                'processor_id': current_user.user.user_id if current_user else 'system',
                'processor_name': current_user.user.user_name if current_user else 'system',
                'process_time': datetime.now(),
                'completion_time': datetime.now(),
                'transaction_id': process_data.transaction_id,
                'updated_by': current_user.user.user_name if current_user else 'system'
            }

            if process_data.remark:
                update_data['remark'] = process_data.remark

            success = await CompanyWithdrawalDao.update_withdrawal_status(
                query_db, withdrawal_id, 'COMPLETED', update_data
            )

            if not success:
                raise ServiceException(message='处理失败')

            # 7. 提交事务
            await query_db.commit()

            return {
                'withdrawal_id': withdrawal_id,
                'status': 'COMPLETED',
                'status_name': '已完成',
                'transaction_no': transaction_no,
                'balance_after': new_balance,
                'completion_time': update_data['completion_time']
            }

        except ServiceException:
            await query_db.rollback()
            raise
        except Exception as e:
            await query_db.rollback()
            raise ServiceException(message=f'处理提现申请失败: {str(e)}')

    @staticmethod
    def _get_status_name(status: str) -> str:
        """获取状态名称"""
        status_map = {
            'PENDING': '待审核',
            'APPROVED': '已通过',
            'REJECTED': '已拒绝',
            'PROCESSING': '处理中',
            'COMPLETED': '已完成',
            'CANCELLED': '已取消'
        }
        return status_map.get(status, status)

    @staticmethod
    def _get_withdrawal_type_name(withdrawal_type: int) -> str:
        """获取提现类型名称"""
        type_map = {
            1: '自行开票',
            2: '零工提现'
        }
        return type_map.get(withdrawal_type, '未知类型')
