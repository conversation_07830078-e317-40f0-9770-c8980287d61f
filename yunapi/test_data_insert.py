"""
测试数据插入脚本
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.ext.asyncio import AsyncSession
from config.get_db import async_engine
from module_admin.entity.do.training_categories_do import TrainingCategories
from module_admin.entity.do.training_courses_do import TrainingCourses
from datetime import datetime
import json


async def insert_test_data():
    """插入测试数据"""
    async with AsyncSession(async_engine) as session:
        try:
            # 插入测试分类
            category1 = TrainingCategories(
                id="cat_001",
                uuid="cat_uuid_001",
                name="基础培训",
                description="门店基础操作培训",
                parent_id=None,
                sort_order=1,
                status=1,
                create_time=datetime.now(),
                update_time=datetime.now()
            )
            
            category2 = TrainingCategories(
                id="cat_002",
                uuid="cat_uuid_002",
                name="高级培训",
                description="门店高级操作培训",
                parent_id=None,
                sort_order=2,
                status=1,
                create_time=datetime.now(),
                update_time=datetime.now()
            )
            
            session.add(category1)
            session.add(category2)
            
            # 插入测试课程
            course1 = TrainingCourses(
                id="course_001",
                uuid="course_uuid_001",
                title="通过门店小程序码下单",
                description="详细介绍如何通过门店小程序码进行下单操作，包括扫码、选择服务、填写信息等完整流程。",
                cover_image="https://jingang.obs.cn-east-3.myhuaweicloud.com/jgstore/static/%E9%A6%96%E9%A1%B52.png",
                category_id="cat_002",
                content_type=1,
                content_data=json.dumps({
                    "url": "https://vi.xiaoyujia.com/30cf4c3941b071f080c30361c0d60102/2ae99f8235ed1485e77730cc90975bea-ld.mp4",
                    "type": "video",
                    "thumbnail": "https://jingang.obs.cn-east-3.myhuaweicloud.com/jgstore/static/%E9%A6%96%E9%A1%B52.png"
                }),
                duration=0,
                views=2568,
                sort_order=1,
                is_featured=1,
                status=1,
                creator_id="admin_001",
                creator_name="系统管理员",
                create_time=datetime.now(),
                update_time=datetime.now()
            )
            
            course2 = TrainingCourses(
                id="course_002",
                uuid="course_uuid_002",
                title="门店管理基础操作",
                description="介绍门店管理的基础操作，包括商品管理、订单处理、客户服务等。",
                cover_image="https://jingang.obs.cn-east-3.myhuaweicloud.com/jgstore/static/example2.png",
                category_id="cat_001",
                content_type=2,
                content_data=json.dumps({
                    "content": "这是一篇关于门店管理的文章内容...",
                    "type": "article"
                }),
                duration=0,
                views=1234,
                sort_order=2,
                is_featured=0,
                status=1,
                creator_id="admin_001",
                creator_name="系统管理员",
                create_time=datetime.now(),
                update_time=datetime.now()
            )
            
            session.add(course1)
            session.add(course2)
            
            await session.commit()
            print("测试数据插入成功！")
            
        except Exception as e:
            await session.rollback()
            print(f"插入测试数据失败: {e}")
        finally:
            await session.close()


if __name__ == "__main__":
    asyncio.run(insert_test_data())
