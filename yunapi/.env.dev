# -------- 应用配置 --------
# 应用运行环境
APP_ENV = 'dev'
# 应用名称
APP_NAME = 'JgAdmin-FastAPI'
# 应用代理路径
APP_ROOT_PATH = '/dev-api'
# 应用主机
APP_HOST = '0.0.0.0'
# 应用端口
APP_PORT = 9099
# 应用版本
APP_VERSION= '1.6.2'
# 应用是否开启热重载
APP_RELOAD = true
# 应用是否开启IP归属区域查询
APP_IP_LOCATION_QUERY = true
# 应用是否允许账号同时登录
APP_SAME_TIME_LOGIN = true

# -------- Jwt配置 --------
# Jwt秘钥
JWT_SECRET_KEY = 'b01c66dc2c58dc6a0aabfe2144256be36226de378bf87f72c0c795dda67f4d55'
# Jwt算法
JWT_ALGORITHM = 'HS256'
# 令牌过期时间
JWT_EXPIRE_MINUTES = 1440
# redis中令牌过期时间
JWT_REDIS_EXPIRE_MINUTES = 30


# -------- 数据库配置 --------
# 数据库类型，可选的有'mysql'、'postgresql'，默认为'mysql'
DB_TYPE = 'mysql'
# 数据库主机
DB_HOST = '127.0.0.1'
# 数据库端口
DB_PORT = 3306
# 数据库用户名
DB_USERNAME = 'root'
# 数据库密码
DB_PASSWORD = '123456'
# 数据库名称
DB_DATABASE = 'jgtest'
# 是否开启sqlalchemy日志
DB_ECHO = true
# 允许溢出连接池大小的最大连接数
DB_MAX_OVERFLOW = 10
# 连接池大小，0表示连接数无限制
DB_POOL_SIZE = 50
# 连接回收时间（单位：秒）
DB_POOL_RECYCLE = 3600
# 连接池中没有线程可用时，最多等待的时间（单位：秒）
DB_POOL_TIMEOUT = 30

# -------- Redis配置 --------
# Redis主机
REDIS_HOST = '127.0.0.1'
# Redis端口
REDIS_PORT = 6379
# Redis用户名
REDIS_USERNAME = ''
# Redis密码
REDIS_PASSWORD = ''
# Redis数据库
REDIS_DATABASE = 2

# -------- 阿里云短信配置 --------
# 阿里云短信AccessKey ID
SMS_ACCESS_KEY_ID = 'LTAIfTID1WDWpmTL'
# 阿里云短信AccessKey Secret
SMS_ACCESS_KEY_SECRET = 'S7SQEdtiAcuZDTK4ATYSTooSfrVs4H'
# 阿里云短信签名名称
SMS_SIGN_NAME = '金刚到家'
# 阿里云短信API地址
SMS_API_URL = 'https://dysmsapi.aliyuncs.com'
# 阿里云短信验证码模板ID
SMS_VERIFICATION_TEMPLATE_CODE = 'SMS_485335604'
# 阿里云短信审核通过模板ID
SMS_AUDIT_SUCCESS_TEMPLATE_CODE = 'SMS_487450406'
# 短信验证码有效期（分钟）
SMS_EXPIRE_MINUTES = 5

# -------- 阿里云OSS配置 --------
OSS_ACCESS_KEY_ID = "LTAIHVOdjxgTfzUK"  # 阿里云OSS访问密钥ID
OSS_ACCESS_KEY_SECRET = "cbLUAqQrpgJuL0SbgijZ8S55ph4MIX"  # 阿里云OSS访问密钥Secret
OSS_BUCKET_NAME = "jingangai"  # 存储桶名称
OSS_ENDPOINT = "http://oss-cn-hangzhou.aliyuncs.com"  # 终端节点
OSS_REGION = "cn-hangzhou"  # 区域
OSS_URL_PREFIX = "https://jingangai.oss-cn-hangzhou.aliyuncs.com"  # URL前缀
OSS_DIRECTORY = "jgstore"  # 存储目录

# -------- 文件上传配置 --------
UPLOAD_MAX_FILE_SIZE = 500  # 文件上传大小限制(MB)