<template>
  <div>
    <el-upload
      :action="uploadUrl"
      :before-upload="handleBeforeUpload"
      :on-success="handleUploadSuccess"
      :on-error="handleUploadError"
      name="file"
      :show-file-list="false"
      :headers="headers"
      style="display: none"
      ref="upload"
      v-if="this.type == 'url'"
    >
    </el-upload>
    <div :id="editorId" :style="styles"></div>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";

export default {
  name: "TinyMCEEditor",
  props: {
    /* 编辑器的内容 */
    value: {
      type: String,
      default: "",
    },
    /* 高度 */
    height: {
      type: Number,
      default: null,
    },
    /* 最小高度 */
    minHeight: {
      type: Number,
      default: null,
    },
    /* 只读 */
    readOnly: {
      type: Boolean,
      default: false,
    },
    /* 上传文件大小限制(MB) */
    fileSize: {
      type: Number,
      default: 500,
    },
    /* 类型（base64格式、url格式） */
    type: {
      type: String,
      default: "url",
    }
  },
  data() {
    return {
      uploadUrl: process.env.VUE_APP_BASE_API + "/common/upload",
      headers: {
        Authorization: "Bearer " + getToken()
      },
      editor: null,
      currentValue: "",
      editorId: 'tinymce-editor-' + Math.random().toString(36).substring(2, 9),
    };
  },
  computed: {
    styles() {
      let style = {};
      if (this.minHeight) {
        style.minHeight = `${this.minHeight}px`;
      }
      if (this.height) {
        style.height = `${this.height}px`;
      }
      return style;
    },
  },
  watch: {
    value: {
      handler(val) {
        if (val !== this.currentValue) {
          this.currentValue = val === null ? "" : val;
          if (this.editor && this.editor.getContent() !== this.currentValue) {
            this.editor.setContent(this.currentValue);
          }
        }
      },
      immediate: true,
    },
  },
  mounted() {
    this.init();
  },
  beforeDestroy() {
    if (this.editor) {
      this.editor.destroy();
      this.editor = null;
    }
  },
  methods: {
    init() {
      // 动态加载TinyMCE
      if (typeof window.tinymce === 'undefined') {
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/tinymce@6/tinymce.min.js';
        script.onload = () => {
          this.initEditor();
        };
        document.head.appendChild(script);
      } else {
        this.initEditor();
      }
    },
    initEditor() {
      const self = this;
      window.tinymce.init({
        selector: `#${this.editorId}`,
        height: this.height || this.minHeight || 300,
        menubar: false,
        readonly: this.readOnly,
        plugins: [
          'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
          'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
          'insertdatetime', 'media', 'table', 'help', 'wordcount'
        ],
        toolbar: 'undo redo | blocks | ' +
          'bold italic forecolor | alignleft aligncenter ' +
          'alignright alignjustify | bullist numlist outdent indent | ' +
          'removeformat | link image | code | help',
        content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }',
        // 支持自定义协议
        allow_unsafe_link_target: true,
        convert_urls: false,
        relative_urls: false,
        remove_script_host: false,
        // 自定义链接验证，支持所有协议包括miniapp://
        link_assume_external_targets: false,
        target_list: false,
        // 允许所有协议的链接
        invalid_elements: '',
        extended_valid_elements: 'a[href|target|title|class|style]',
        // 图片上传配置
        images_upload_handler: this.type === 'url' ? this.handleImageUpload : undefined,
        setup: function(editor) {
          self.editor = editor;
          editor.on('init', function() {
            editor.setContent(self.currentValue);
          });
          editor.on('input change undo redo', function() {
            const content = editor.getContent();
            self.currentValue = content;
            self.$emit("input", content);
            self.$emit("on-change", { 
              html: content, 
              text: editor.getContent({format: 'text'}), 
              editor: editor 
            });
          });
        }
      });
    },
    handleImageUpload(blobInfo, success, failure) {
      const formData = new FormData();
      formData.append('file', blobInfo.blob(), blobInfo.filename());
      
      // 使用axios或fetch上传图片
      this.$http.post(this.uploadUrl, formData, {
        headers: this.headers
      }).then(response => {
        if (response.data.code === 200) {
          success(response.data.url);
        } else {
          failure('上传失败');
        }
      }).catch(() => {
        failure('上传失败');
      });
    },
    handleBeforeUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/gif';
      const isLt2M = file.size / 1024 / 1024 < this.fileSize;
      if (!isJPG) {
        this.$message.error('上传图片只能是 JPG/PNG/GIF 格式!');
      }
      if (!isLt2M) {
        this.$message.error(`上传图片大小不能超过 ${this.fileSize}MB!`);
      }
      return isJPG && isLt2M;
    },
    handleUploadSuccess(res, file) {
      if (res.code == 200) {
        // 插入图片到编辑器
        if (this.editor) {
          this.editor.insertContent(`<img src="${res.url}" alt="${file.name}" />`);
        }
        this.$message.success("图片插入成功");
      } else {
        this.$message.error("图片插入失败");
      }
    },
    handleUploadError() {
      this.$message.error("图片插入失败");
    },
  },
};
</script>

<style scoped>
.tinymce-editor {
  line-height: normal;
}
</style>
