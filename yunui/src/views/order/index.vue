<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="订单编号" prop="orderNumber">
        <el-input
          v-model="queryParams.orderNumber"
          placeholder="请输入订单编号"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="手机号" prop="mobile">
        <el-input
          v-model="queryParams.mobile"
          placeholder="请输入用户手机号"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="门店名称" prop="storeName">
        <el-select
          v-model="queryParams.storeName"
          placeholder="请选择门店名称"
          clearable
          filterable
          style="width: 240px"
          @change="handleQuery"
        >
          <el-option
            v-for="storeName in storeNameOptions"
            :key="storeName"
            :label="storeName"
            :value="storeName"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="服务人员" prop="serviceName">
        <el-input
          v-model="queryParams.serviceName"
          placeholder="请输入服务人员姓名"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="服务地址" prop="serviceAddress">
        <el-input
          v-model="queryParams.serviceAddress"
          placeholder="请输入服务地址关键词"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="订单状态" prop="orderStatus">
        <el-select
          v-model="queryParams.orderStatus"
          placeholder="订单状态"
          clearable
          style="width: 240px"
        >
          <el-option label="待支付" value="待支付" />
          <el-option label="已接单" value="已接单" />
          <el-option label="已派单" value="已派单" />
          <el-option label="派单待确认" value="派单待确认" />
          <el-option label="拒绝接单" value="拒绝接单" />
          <el-option label="执行中" value="执行中" />
          <el-option label="开始服务" value="开始服务" />
          <el-option label="服务结束" value="服务结束" />
          <el-option label="已完成" value="已完成" />
          <el-option label="已评价" value="已评价" />
          <el-option label="已取消" value="已取消" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['order:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['order:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['order:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['order:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>

    <!-- 订单统计信息 -->
    <div class="order-statistics">
      共 {{ orderStatistics.totalCount || 0 }} 条订单，总金额：¥{{ formatAmount(orderStatistics.totalAmount) }}
    </div>

    <el-table v-loading="loading" :data="orderList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="订单编号" align="center" key="orderNumber" prop="orderNumber" v-if="columns[0].visible" :show-overflow-tooltip="true" />

      <el-table-column label="用户手机号" align="center" key="mobile" prop="mobile" v-if="columns[1].visible" />
      <el-table-column label="门店名称" align="center" key="storeName" prop="storeName" v-if="columns[2].visible" :show-overflow-tooltip="true" />
      <el-table-column label="产品名称" align="center" key="productName" prop="productName" v-if="columns[3].visible" :show-overflow-tooltip="true" />
      <el-table-column label="实际支付金额" align="center" key="payActual" prop="payActual" v-if="columns[4].visible" />
      <el-table-column label="订单状态" align="center" key="orderStatus" v-if="columns[5].visible">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.orderStatusName === '待支付'" type="warning" effect="plain">待支付</el-tag>
          <el-tag v-else-if="scope.row.orderStatusName === '已接单'" type="primary" effect="plain">已接单</el-tag>
          <el-tag v-else-if="scope.row.orderStatusName === '派单待确认'" type="warning" effect="dark">派单待确认</el-tag>
          <el-tag v-else-if="scope.row.orderStatusName === '拒绝接单'" type="danger" effect="plain">拒绝接单</el-tag>
          <el-tag v-else-if="scope.row.orderStatusName === '已派单'" type="info" effect="plain">已派单</el-tag>
          <el-tag v-else-if="scope.row.orderStatusName === '执行中'" type="success" effect="plain">执行中</el-tag>
          <el-tag v-else-if="scope.row.orderStatusName === '开始服务'" type="primary" effect="dark">开始服务</el-tag>
          <el-tag v-else-if="scope.row.orderStatusName === '服务结束'" type="info" effect="dark">服务结束</el-tag>
          <el-tag v-else-if="scope.row.orderStatusName === '已完成'" type="info" effect="plain">已完成</el-tag>
          <el-tag v-else-if="scope.row.orderStatusName === '已评价'" type="success" effect="dark">已评价</el-tag>
          <el-tag v-else-if="scope.row.orderStatusName === '已取消'" type="danger" effect="plain">已取消</el-tag>
          <el-tag v-else-if="scope.row.orderStatus == 1" type="warning" effect="dark">待支付</el-tag>
          <el-tag v-else-if="scope.row.orderStatus == 2" type="success" effect="dark">已支付</el-tag>
          <el-tag v-else-if="scope.row.orderStatus == 3" type="info" effect="dark">已完成</el-tag>
          <el-tag v-else-if="scope.row.orderStatus == 4" type="danger" effect="dark">已取消</el-tag>
          <el-tag v-else-if="scope.row.orderStatus == 5" type="primary" effect="dark">进行中</el-tag>
          <el-tag v-else type="info" effect="plain">{{ scope.row.orderStatusName || '未知状态' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="订单来源" align="center" key="source" v-if="columns[6].visible">
        <template slot-scope="scope">
          <span v-if="scope.row.source === 'client'">门店端</span>
          <span v-else-if="scope.row.source === 'proxy'">代客下单</span>
          <span v-else>{{ scope.row.source }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" v-if="columns[7].visible" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="160"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['order:query']"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['order:edit']"
            v-show="false"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-close"
            @click="handleCancel(scope.row)"
            v-hasPermi="['order:cancel']"
            :disabled="!canCancelOrder(scope.row)"
          >取消</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改订单配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="订单编号" prop="orderNumber">
              <el-input v-model="form.orderNumber" placeholder="请输入订单编号" maxlength="64" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="用户ID" prop="userId">
              <el-input v-model="form.userId" placeholder="请输入用户ID" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="门店ID" prop="storeId">
              <el-input v-model="form.storeId" placeholder="请输入门店ID" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="门店名称" prop="storeName">
              <el-input v-model="form.storeName" placeholder="请输入门店名称" maxlength="100" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品名称" prop="productName">
              <el-input v-model="form.productName" placeholder="请输入产品名称" maxlength="100" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="购买数量" prop="buyNum">
              <el-input v-model="form.buyNum" placeholder="请输入购买数量" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="实际支付金额" prop="payActual">
              <el-input v-model="form.payActual" placeholder="请输入实际支付金额" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="总支付金额" prop="totalPayActual">
              <el-input v-model="form.totalPayActual" placeholder="请输入总支付金额" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="订单来源" prop="source">
              <el-input v-model="form.source" placeholder="请输入订单来源" maxlength="50" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="服务时长" prop="serviceHour">
              <el-input v-model="form.serviceHour" placeholder="请输入服务时长（小时）" maxlength="10" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="订单状态" prop="orderStatus">
              <el-select v-model="form.orderStatus" placeholder="请选择订单状态">
                <el-option label="待支付" value="待支付" />
                <el-option label="已接单" value="已接单" />
                <el-option label="派单待确认" value="派单待确认" />
                <el-option label="拒绝接单" value="拒绝接单" />
                <el-option label="已派单" value="已派单" />
                <el-option label="执行中" value="执行中" />
                <el-option label="开始服务" value="开始服务" />
                <el-option label="服务结束" value="服务结束" />
                <el-option label="已完成" value="已完成" />
                <el-option label="已评价" value="已评价" />
                <el-option label="已取消" value="已取消" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="服务地址" prop="serviceAddress">
              <el-input v-model="form.serviceAddress" placeholder="请输入服务地址" maxlength="255" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="客户备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入客户备注"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 订单详情抽屉 -->
    <el-drawer
      title="订单详情"
      :visible.sync="detailDrawer"
      direction="rtl"
      size="60%"
      :before-close="closeDetailDrawer"
    >
      <div style="padding: 20px;">
        <!-- 订单基本信息 -->
        <el-descriptions title="订单基本信息" :column="2" border>
          <el-descriptions-item label="订单编号">{{ orderDetail.orderNumber }}</el-descriptions-item>
          <el-descriptions-item label="用户手机号">{{ orderDetail.mobile }}</el-descriptions-item>
          <el-descriptions-item label="门店名称">{{ orderDetail.storeName }}</el-descriptions-item>
          <el-descriptions-item label="产品名称">{{ orderDetail.productName }}</el-descriptions-item>
          <el-descriptions-item label="产品类型">{{ orderDetail.productTypeName }}</el-descriptions-item>
          <el-descriptions-item label="购买数量">{{ orderDetail.buyNum }}</el-descriptions-item>
          <el-descriptions-item label="实际支付金额">{{ orderDetail.payActual }}</el-descriptions-item>
          <el-descriptions-item label="总支付金额">{{ orderDetail.totalPayActual }}</el-descriptions-item>
          <el-descriptions-item label="订单状态">
            <el-tag v-if="orderDetail.orderStatusName === '待支付'" type="warning" effect="plain">{{ orderDetail.orderStatusName }}</el-tag>
            <el-tag v-else-if="orderDetail.orderStatusName === '已接单'" type="primary" effect="plain">{{ orderDetail.orderStatusName }}</el-tag>
            <el-tag v-else-if="orderDetail.orderStatusName === '派单待确认'" type="warning" effect="dark">{{ orderDetail.orderStatusName }}</el-tag>
            <el-tag v-else-if="orderDetail.orderStatusName === '拒绝接单'" type="danger" effect="plain">{{ orderDetail.orderStatusName }}</el-tag>
            <el-tag v-else-if="orderDetail.orderStatusName === '已派单'" type="info" effect="plain">{{ orderDetail.orderStatusName }}</el-tag>
            <el-tag v-else-if="orderDetail.orderStatusName === '执行中'" type="success" effect="plain">{{ orderDetail.orderStatusName }}</el-tag>
            <el-tag v-else-if="orderDetail.orderStatusName === '开始服务'" type="primary" effect="dark">{{ orderDetail.orderStatusName }}</el-tag>
            <el-tag v-else-if="orderDetail.orderStatusName === '服务结束'" type="info" effect="dark">{{ orderDetail.orderStatusName }}</el-tag>
            <el-tag v-else-if="orderDetail.orderStatusName === '已完成'" type="info" effect="plain">{{ orderDetail.orderStatusName }}</el-tag>
            <el-tag v-else-if="orderDetail.orderStatusName === '已评价'" type="success" effect="dark">{{ orderDetail.orderStatusName }}</el-tag>
            <el-tag v-else-if="orderDetail.orderStatusName === '已取消'" type="danger" effect="plain">{{ orderDetail.orderStatusName }}</el-tag>
            <el-tag v-else>{{ orderDetail.orderStatusName }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="订单来源">{{ orderDetail.source }}</el-descriptions-item>
          <el-descriptions-item label="服务地址" :span="2">{{ orderDetail.serviceAddress }}</el-descriptions-item>
          <el-descriptions-item label="服务时长">{{ orderDetail.serviceDuration ? orderDetail.serviceDuration + '分钟' : '未设置' }}</el-descriptions-item>
          <el-descriptions-item label="服务时间">
            <div style="display: flex; align-items: center;">
              <span style="flex: 1;">{{ parseTime(orderDetail.serviceDate, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
              <el-button type="text" size="small" @click="editServiceTime">编辑</el-button>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间" :span="2">{{ parseTime(orderDetail.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
          <el-descriptions-item label="客户备注" :span="2">
            <div style="display: flex; align-items: center;">
              <span style="flex: 1;">{{ orderDetail.remark || '无' }}</span>
              <el-button type="text" size="small" @click="editRemark('remark', '客户备注', orderDetail.remark)">编辑</el-button>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="售后备注" :span="2">
            <div style="display: flex; align-items: center;">
              <span style="flex: 1;">{{ orderDetail.afterSaleRemark || '无' }}</span>
              <el-button type="text" size="small" @click="editRemark('afterSaleRemark', '售后备注', orderDetail.afterSaleRemark)">编辑</el-button>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="服务提醒" :span="2">
            <div style="display: flex; align-items: center;">
              <span style="flex: 1;">{{ orderDetail.serviceRemark || '无' }}</span>
              <el-button type="text" size="small" @click="editRemark('serviceRemark', '服务提醒', orderDetail.serviceRemark)">编辑</el-button>
            </div>
          </el-descriptions-item>
        </el-descriptions>

        <!-- 派单信息 -->
        <div v-if="orderDetail.orderWaiters && orderDetail.orderWaiters.length > 0" style="margin-top: 30px;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
            <h3 style="margin: 0; color: #303133;">派单信息</h3>
            <el-button
              type="primary"
              size="small"
              icon="el-icon-edit"
              @click="handleModifyDispatch"
              v-hasPermi="['order:edit']"
            >修改派单信息</el-button>
          </div>
          <el-card v-for="(waiter, index) in orderDetail.orderWaiters" :key="index" style="margin-bottom: 20px;" shadow="hover">
            <div slot="header" class="clearfix">
              <span style="font-weight: bold; color: #409EFF;">派单记录 {{ index + 1 }}</span>
            </div>

            <el-descriptions :column="2" border>
              <el-descriptions-item label="员工姓名">{{ waiter.serviceName }}</el-descriptions-item>
              <el-descriptions-item label="员工ID">{{ waiter.serviceId }}</el-descriptions-item>
              <el-descriptions-item label="分成比例">{{ waiter.servicePersonalCommission }}%</el-descriptions-item>
              <el-descriptions-item label="分成金额">{{ waiter.servicePersonal }}</el-descriptions-item>
              <el-descriptions-item label="派单时间" :span="3">{{ parseTime(waiter.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
            </el-descriptions>

            <!-- 图片展示区域 -->
            <div style="margin-top: 20px;">
              <!-- 开始前环境图 -->
              <div v-if="waiter.envBeforeImages && waiter.envBeforeImages.length > 0" style="margin-bottom: 20px;">
                <h4 style="margin-bottom: 15px; color: #303133; font-size: 16px;">开始前环境图</h4>
                <el-row :gutter="15">
                  <el-col :span="12" v-for="image in waiter.envBeforeImages" :key="image.id" style="margin-bottom: 15px;">
                    <el-card :body-style="{ padding: '15px' }" shadow="hover" class="image-card">
                      <img :src="image.url" :alt="image.fileName" class="image-display" @click="previewImage(image.url)">
                      <div class="image-info">
                        {{ image.fileName }}
                      </div>
                    </el-card>
                  </el-col>
                </el-row>
              </div>

              <!-- 服务前图 -->
              <div v-if="waiter.serviceBeforeImages && waiter.serviceBeforeImages.length > 0" style="margin-bottom: 20px;">
                <h4 style="margin-bottom: 15px; color: #303133; font-size: 16px;">服务前图</h4>
                <el-row :gutter="15">
                  <el-col :span="12" v-for="image in waiter.serviceBeforeImages" :key="image.id" style="margin-bottom: 15px;">
                    <el-card :body-style="{ padding: '15px' }" shadow="hover" class="image-card">
                      <img :src="image.url" :alt="image.fileName" class="image-display" @click="previewImage(image.url)">
                      <div class="image-info">
                        {{ image.fileName }}
                      </div>
                    </el-card>
                  </el-col>
                </el-row>
              </div>

              <!-- 服务后图 -->
              <div v-if="waiter.serviceAfterImages && waiter.serviceAfterImages.length > 0" style="margin-bottom: 20px;">
                <h4 style="margin-bottom: 15px; color: #303133; font-size: 16px;">服务后图</h4>
                <el-row :gutter="15">
                  <el-col :span="12" v-for="image in waiter.serviceAfterImages" :key="image.id" style="margin-bottom: 15px;">
                    <el-card :body-style="{ padding: '15px' }" shadow="hover" class="image-card">
                      <img :src="image.url" :alt="image.fileName" class="image-display" @click="previewImage(image.url)">
                      <div class="image-info">
                        {{ image.fileName }}
                      </div>
                    </el-card>
                  </el-col>
                </el-row>
              </div>

              <!-- 电子签图 -->
              <div v-if="waiter.signatureImages && waiter.signatureImages.length > 0" style="margin-bottom: 20px;">
                <h4 style="margin-bottom: 15px; color: #303133; font-size: 16px;">电子签图</h4>
                <el-row :gutter="15">
                  <el-col :span="12" v-for="image in waiter.signatureImages" :key="image.id" style="margin-bottom: 15px;">
                    <el-card :body-style="{ padding: '15px' }" shadow="hover" class="image-card">
                      <img :src="image.url" :alt="image.fileName" class="image-display" @click="previewImage(image.url)">
                      <div class="image-info">
                        {{ image.fileName }}
                      </div>
                    </el-card>
                  </el-col>
                </el-row>
              </div>
            </div>
          </el-card>
        </div>
      </div>
    </el-drawer>

    <!-- 修改派单信息对话框 -->
    <el-dialog title="修改派单信息" :visible.sync="dispatchDialogVisible" width="500px" center>
      <div v-loading="dispatchLoading">
        <el-form :model="dispatchForm" label-width="120px">
          <el-form-item label="当前派单员工">
            <span>{{ currentDispatchStaff }}</span>
          </el-form-item>
          <el-form-item label="选择新员工" required>
            <el-select v-model="dispatchForm.staffId" placeholder="请选择员工" style="width: 100%;">
              <el-option
                v-for="staff in availableStaffList"
                :key="staff.id"
                :label="staff.user_name"
                :value="staff.id"
              >
                <span style="float: left">{{ staff.user_name }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ staff.mobile }}</span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dispatchDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmModifyDispatch" :loading="dispatchSubmitting">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 图片预览对话框 -->
    <el-dialog title="图片预览" :visible.sync="imagePreviewVisible" width="60%" center>
      <div style="text-align: center;">
        <img :src="previewImageUrl" style="max-width: 100%; max-height: 500px;" />
      </div>
    </el-dialog>

    <!-- 编辑备注对话框 -->
    <el-dialog :title="'编辑' + remarkDialog.title" :visible.sync="remarkDialog.open" width="600px" append-to-body>
      <el-form ref="remarkForm" :model="remarkDialog.form" label-width="100px">
        <el-form-item :label="remarkDialog.title">
          <el-input
            v-model="remarkDialog.form.content"
            type="textarea"
            :rows="4"
            :placeholder="'请输入' + remarkDialog.title"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="remarkDialog.open = false">取 消</el-button>
        <el-button type="primary" @click="saveRemark" :loading="remarkDialog.loading">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 编辑服务时间对话框 -->
    <el-dialog title="编辑服务时间" :visible.sync="serviceTimeDialog.open" width="500px" append-to-body>
      <el-form ref="serviceTimeForm" :model="serviceTimeDialog.form" label-width="100px">
        <el-form-item label="服务时间" required>
          <el-date-picker
            v-model="serviceTimeDialog.form.serviceDate"
            type="datetime"
            placeholder="选择服务时间"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 100%;"
            :picker-options="{
              disabledDate(time) {
                return time.getTime() < Date.now() - 8.64e7;
              }
            }"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="serviceTimeDialog.open = false">取 消</el-button>
        <el-button type="primary" @click="saveServiceTime" :loading="serviceTimeDialog.loading">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listOrder, getOrder, delOrder, cancelOrder, addOrder, updateOrder, updateOrderRemark, exportOrder, getAvailableDispatchStaff, updateOrderDispatchStaff, getStoreNames } from "@/api/order/order";

export default {
  name: "Order",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 订单表格数据
      orderList: null,
      // 门店名称选项列表
      storeNameOptions: [],
      // 订单统计信息
      orderStatistics: {
        totalCount: 0,
        totalAmount: 0
      },
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示详情抽屉
      detailDrawer: false,
      // 订单详情数据
      orderDetail: {},
      // 图片预览
      imagePreviewVisible: false,
      previewImageUrl: '',
      // 修改派单信息
      dispatchDialogVisible: false,
      dispatchLoading: false,
      dispatchSubmitting: false,
      currentDispatchStaff: '',
      availableStaffList: [],
      dispatchForm: {
        staffId: null
      },
      // 日期范围
      dateRange: [],
      // 表单参数
      form: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        orderNumber: undefined,
        mobile: undefined,
        storeName: undefined,
        serviceName: undefined,
        serviceAddress: undefined,
        orderStatus: undefined
      },
      // 列信息
      columns: [
        { key: 0, label: `订单编号`, visible: true },
        { key: 1, label: `用户手机号`, visible: true },
        { key: 2, label: `门店名称`, visible: true },
        { key: 3, label: `产品名称`, visible: true },
        { key: 4, label: `实际支付金额`, visible: true },
        { key: 5, label: `订单状态`, visible: true },
        { key: 6, label: `订单来源`, visible: true },
        { key: 7, label: `创建时间`, visible: true }
      ],
      // 表单校验
      rules: {
        orderNumber: [
          { required: true, message: "订单编号不能为空", trigger: "blur" }
        ],
        userId: [
          { required: true, message: "用户ID不能为空", trigger: "blur" }
        ],
        storeId: [
          { required: true, message: "门店ID不能为空", trigger: "blur" }
        ],
        buyNum: [
          { required: true, message: "购买数量不能为空", trigger: "blur" }
        ],
        payActual: [
          { required: true, message: "实际支付金额不能为空", trigger: "blur" }
        ],
        totalPayActual: [
          { required: true, message: "总支付金额不能为空", trigger: "blur" }
        ],
        source: [
          { required: true, message: "订单来源不能为空", trigger: "blur" }
        ],
        serviceHour: [
          { required: true, message: "服务时长不能为空", trigger: "blur" }
        ]
      },
      // 编辑备注对话框
      remarkDialog: {
        open: false,
        loading: false,
        title: '',
        field: '',
        form: {
          content: ''
        }
      },
      // 编辑服务时间对话框
      serviceTimeDialog: {
        open: false,
        loading: false,
        form: {
          serviceDate: ''
        }
      }
    };
  },
  created() {
    this.getList();
    this.getStoreNameOptions();
  },
  methods: {
    /** 查询订单列表 */
    getList() {
      this.loading = true;
      const params = this.addDateRange(this.queryParams, this.dateRange);
      console.log('查询参数:', params);
      listOrder(params).then(response => {
          this.orderList = response.rows;
          this.total = response.total;

          // 更新统计信息
          if (response.statistics) {
            this.orderStatistics = {
              totalCount: response.statistics.total_count || 0,
              totalAmount: response.statistics.total_amount || 0
            };
          }

          this.loading = false;
        }
      );
    },
    /** 获取门店名称选项列表 */
    getStoreNameOptions() {
      getStoreNames().then(response => {
        this.storeNameOptions = response.data || [];
      }).catch(error => {
        console.error('获取门店名称列表失败:', error);
        this.$message.error('获取门店名称列表失败');
      });
    },
    /** 格式化金额显示 */
    formatAmount(amount) {
      if (!amount && amount !== 0) return '0.00';
      return Number(amount).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        orderNumber: undefined,
        userId: undefined,
        storeId: undefined,
        storeName: undefined,
        productName: undefined,
        buyNum: undefined,
        payActual: undefined,
        totalPayActual: undefined,
        source: undefined,
        serviceHour: undefined,
        orderStatus: undefined,
        serviceAddress: undefined,
        remark: undefined
      };
      this.resetForm("form");
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加订单";
    },
    /** 查看按钮操作 */
    handleView(row) {
      const orderId = row.id || this.ids;
      this.getOrderDetail(orderId);
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const orderId = row.id || this.ids
      getOrder(orderId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改订单";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != undefined) {
            updateOrder(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addOrder(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete() {
      const orderIds = this.ids;
      const orderNumbers = this.orderList.filter(item => this.ids.includes(item.id)).map(item => item.orderNumber);
      this.$modal.confirm('是否确认取消订单编号为"' + orderNumbers.join(', ') + '"的订单？取消后订单状态将变为"已取消"。').then(function() {
        return delOrder(orderIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("订单取消成功");
      }).catch(() => {});
    },
    /** 取消按钮操作 */
    handleCancel(row) {
      const orderIds = row.id || this.ids;
      const orderNumber = row.orderNumber || orderIds;
      this.$modal.confirm('是否确认取消订单编号为"' + orderNumber + '"的订单？取消后订单状态将变为"已取消"。').then(function() {
        return cancelOrder(orderIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("订单取消成功");
      }).catch(() => {});
    },
    /** 判断订单是否可以取消 */
    canCancelOrder(row) {
      // 已取消、已完成、已评价的订单不能再次取消
      const nonCancelableStatuses = ['已取消', '已完成', '已评价'];
      return !nonCancelableStatuses.includes(row.orderStatusName);
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('order/export', {
        ...this.queryParams
      }, `order_${new Date().getTime()}.xlsx`)
    },
    /** 获取订单详情 */
    getOrderDetail(orderId) {
      getOrder(orderId).then(response => {
        this.orderDetail = response.data;
        this.detailDrawer = true;
      }).catch(error => {
        console.error('获取订单详情失败:', error);
        this.$message.error('获取订单详情失败，请重试');
      });
    },
    /** 图片预览 */
    previewImage(imageUrl) {
      this.previewImageUrl = imageUrl;
      this.imagePreviewVisible = true;
    },
    /** 关闭详情抽屉 */
    closeDetailDrawer() {
      this.detailDrawer = false;
      this.orderDetail = {};
    },
    /** 修改派单信息 */
    async handleModifyDispatch() {
      if (!this.orderDetail.id) {
        this.$message.error('订单信息不完整');
        return;
      }

      // 获取当前派单员工信息
      if (this.orderDetail.orderWaiters && this.orderDetail.orderWaiters.length > 0) {
        this.currentDispatchStaff = this.orderDetail.orderWaiters[0].serviceName;
      } else {
        this.currentDispatchStaff = '暂无派单';
      }

      // 重置表单
      this.dispatchForm.staffId = null;
      this.availableStaffList = [];
      this.dispatchDialogVisible = true;
      this.dispatchLoading = true;

      try {
        // 获取可选派单人员列表
        const response = await getAvailableDispatchStaff(this.orderDetail.id);
        this.availableStaffList = response.data || [];
        if (this.availableStaffList.length === 0) {
          this.$message.warning('暂无可选派单人员');
        }
      } catch (error) {
        console.error('获取可选派单人员失败:', error);
        this.$message.error('获取可选派单人员失败，请重试');
      } finally {
        this.dispatchLoading = false;
      }
    },
    /** 确认修改派单信息 */
    async confirmModifyDispatch() {
      if (!this.dispatchForm.staffId) {
        this.$message.error('请选择派单员工');
        return;
      }

      this.dispatchSubmitting = true;
      try {
        const data = {
          order_number: this.orderDetail.orderNumber,
          staff_id: this.dispatchForm.staffId
        };

        await updateOrderDispatchStaff(data);
        this.$message.success('派单信息修改成功');
        this.dispatchDialogVisible = false;

        // 刷新订单详情
        this.getOrderDetail(this.orderDetail.id);
      } catch (error) {
        console.error('修改派单信息失败:', error);
        this.$message.error('修改派单信息失败，请重试');
      } finally {
        this.dispatchSubmitting = false;
      }
    },
    /** 编辑备注 */
    editRemark(field, title, currentValue) {
      this.remarkDialog.field = field;
      this.remarkDialog.title = title;
      this.remarkDialog.form.content = currentValue || '';
      this.remarkDialog.open = true;
    },
    /** 保存备注 */
    async saveRemark() {
      this.remarkDialog.loading = true;
      try {
        const updateData = {
          id: this.orderDetail.id,
          [this.remarkDialog.field]: this.remarkDialog.form.content
        };

        await updateOrderRemark(updateData);
        this.$modal.msgSuccess('备注更新成功');
        this.remarkDialog.open = false;

        // 更新本地数据
        this.orderDetail[this.remarkDialog.field] = this.remarkDialog.form.content;
      } catch (error) {
        this.$modal.msgError('备注更新失败');
      } finally {
        this.remarkDialog.loading = false;
      }
    },
    /** 编辑服务时间 */
    editServiceTime() {
      // 确保时间格式正确
      let currentDate = this.orderDetail.serviceDate;
      if (currentDate && typeof currentDate === 'string') {
        // 如果是字符串，转换为Date对象
        currentDate = new Date(currentDate);
      }
      this.serviceTimeDialog.form.serviceDate = currentDate || new Date();
      this.serviceTimeDialog.open = true;
    },
    /** 保存服务时间 */
    async saveServiceTime() {
      if (!this.serviceTimeDialog.form.serviceDate) {
        this.$modal.msgError('请选择服务时间');
        return;
      }

      this.serviceTimeDialog.loading = true;
      try {
        // 确保时间格式正确
        let serviceDate = this.serviceTimeDialog.form.serviceDate;
        if (serviceDate instanceof Date) {
          // 如果是Date对象，转换为字符串
          serviceDate = serviceDate.toISOString().slice(0, 19).replace('T', ' ');
        }

        const updateData = {
          id: this.orderDetail.id,
          serviceDate: serviceDate
        };

        await updateOrderRemark(updateData);
        this.$modal.msgSuccess('服务时间更新成功');
        this.serviceTimeDialog.open = false;

        // 更新本地数据
        this.orderDetail.serviceDate = serviceDate;
      } catch (error) {
        this.$modal.msgError('服务时间更新失败');
      } finally {
        this.serviceTimeDialog.loading = false;
      }
    }
  }
};
</script>

<style scoped>
/* 订单状态标签自定义样式 */
.el-tag.el-tag--warning.el-tag--plain {
  background-color: #fdf6ec;
  border-color: #f5dab1;
  color: #e6a23c;
}

.el-tag.el-tag--success.el-tag--plain {
  background-color: #f0f9ff;
  border-color: #b3d8ff;
  color: #409eff;
}

.el-tag.el-tag--primary.el-tag--plain {
  background-color: #ecf5ff;
  border-color: #b3d8ff;
  color: #409eff;
}

.el-tag.el-tag--danger.el-tag--plain {
  background-color: #fef0f0;
  border-color: #fbc4c4;
  color: #f56c6c;
}

.el-tag.el-tag--info.el-tag--plain {
  background-color: #f4f4f5;
  border-color: #d3d4d6;
  color: #909399;
}

/* 抽屉内容样式 */
.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}

/* 图片展示样式 */
.image-card {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.image-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.image-display {
  width: 100%;
  height: 180px;
  object-fit: cover;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.image-display:hover {
  transform: scale(1.02);
}

.image-info {
  padding: 10px 0 5px 0;
  font-size: 13px;
  color: #606266;
  text-align: center;
  word-break: break-all;
  line-height: 1.4;
}

/* 订单统计信息样式 */
.order-statistics {
  margin-bottom: 10px;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-left: 4px solid #409eff;
  color: #606266;
  font-size: 14px;
}
</style>
