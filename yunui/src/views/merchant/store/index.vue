<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="所属公司" prop="companyId">
        <el-select
          v-model="queryParams.companyId"
          placeholder="请选择公司"
          clearable
          filterable
          @change="handleCompanyChange"
          style="width: 200px;"
        >
          <el-option
            v-for="company in companyList"
            :key="company.id"
            :label="company.name"
            :value="company.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="门店名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入门店名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="负责人" prop="manager">
        <el-input
          v-model="queryParams.manager"
          placeholder="请输入负责人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="营业" value="1" />
          <el-option label="关闭" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['merchant:store:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['merchant:store:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['merchant:store:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="storeList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="门店编号" align="center" prop="id" width="100" />
      <el-table-column label="门店名称" align="center" prop="name" :show-overflow-tooltip="true" />
      <el-table-column label="所属公司" align="center" width="150" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ getCompanyName(scope.row.companyId) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="联系电话" align="center" prop="phone" />
      <el-table-column label="地址" align="center" prop="address" :show-overflow-tooltip="true" />
      <el-table-column label="负责人" align="center" prop="manager" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === 1" type="success">营业</el-tag>
          <el-tag v-else type="danger">关闭</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-user"
            @click="handleViewCustomers(scope.row)"
            v-hasPermi="['merchant:ccuser:list']"
          >查看客户</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['merchant:store:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['merchant:store:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改门店对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="门店名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入门店名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属公司" prop="companyId">
              <el-input
                v-model="companyDisplayName"
                placeholder="所属公司"
                readonly
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="负责人" prop="manager">
              <el-input v-model="form.manager" placeholder="请输入负责人" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="门店等级" prop="level">
              <el-input v-model="form.level" placeholder="请输入门店等级" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="form.phone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="门店电话" prop="mobile">
              <el-input v-model="form.mobile" placeholder="请输入门店电话" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="地址" prop="address">
          <el-input v-model="form.address" type="textarea" placeholder="请输入地址" />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="营业时间" prop="businessHours">
              <el-input v-model="form.businessHours" placeholder="请输入营业时间" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="form.email" placeholder="请输入邮箱" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio :label="1">营业</el-radio>
                <el-radio :label="0">关闭</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="门店简介" prop="introduce">
          <el-input v-model="form.introduce" type="textarea" placeholder="请输入门店简介" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listStore, getStore, delStore, addStore, updateStore, listAllCompanies } from "@/api/merchant/store";

export default {
  name: "Store",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 门店表格数据
      storeList: [],
      // 公司列表数据
      companyList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 当前筛选的公司名称
      currentCompanyName: null,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        manager: null,
        status: null,
        companyId: null,
      },
      // 表单参数
      form: {},
      // 公司显示名称
      companyDisplayName: '',
      // 表单校验
      rules: {
        name: [
          { required: true, message: "门店名称不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getCompanyList();
    this.initializeFromRoute();
  },
  watch: {
    // 监听路由变化，当路由查询参数改变时重新初始化
    '$route.query': {
      handler(newQuery, oldQuery) {
        // 只有当companyId发生变化时才重新初始化
        if (newQuery.companyId !== oldQuery.companyId) {
          this.initializeFromRoute();
        }
      },
      immediate: false
    }
  },
  methods: {
    /** 从路由初始化页面状态 */
    initializeFromRoute() {
      // 重置查询参数
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        name: null,
        manager: null,
        status: null,
        companyId: null,
      };

      // 重置公司筛选状态
      this.currentCompanyName = null;

      // 检查是否有从公司管理页面传递过来的查询参数
      if (this.$route.query.companyId) {
        this.queryParams.companyId = this.$route.query.companyId;
        this.currentCompanyName = this.$route.query.companyName || `公司ID: ${this.$route.query.companyId}`;
      }

      // 重新加载数据
      this.getList();
    },
    /** 获取公司列表 */
    getCompanyList() {
      listAllCompanies().then(response => {
        this.companyList = response.rows || [];
      }).catch(error => {
        console.error('获取公司列表失败:', error);
      });
    },
    /** 处理公司选择变化 */
    handleCompanyChange(companyId) {
      if (companyId) {
        // 找到选中的公司信息
        const selectedCompany = this.companyList.find(company => company.id === companyId);
        this.currentCompanyName = selectedCompany ? selectedCompany.name : `公司ID: ${companyId}`;

        // 更新路由查询参数（不跳转，只更新URL）
        this.$router.replace({
          path: '/merchant/store',
          query: {
            companyId: companyId,
            companyName: this.currentCompanyName
          }
        });
      } else {
        // 清除公司筛选
        this.currentCompanyName = null;
        this.$router.replace({ path: '/merchant/store' });
      }

      // 重新查询数据
      this.handleQuery();
    },
    /** 根据公司ID获取公司名称 */
    getCompanyName(companyId) {
      if (!companyId) {
        return '-';
      }
      const company = this.companyList.find(item => item.id === companyId);
      return company ? company.name : `公司ID: ${companyId}`;
    },
    /** 查询门店列表 */
    getList() {
      this.loading = true;
      listStore(this.queryParams).then(response => {
        this.storeList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(error => {
        this.loading = false;
        this.$modal.msgError('查询门店列表失败');
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        storeUuid: null,
        companyId: this.queryParams.companyId, // 保持当前筛选的公司ID
        name: null,
        phone: null,
        mobile: null,
        address: null,
        businessHours: null,
        manager: null,
        status: 1,
        storeStatus: 1,
        remark: null,
        introduce: null,
        email: null,
        level: null
      };
      this.companyDisplayName = this.getCompanyName(this.queryParams.companyId);
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      // 保存当前的公司筛选
      const currentCompanyId = this.queryParams.companyId;
      const currentCompanyName = this.currentCompanyName;

      // 重置表单
      this.resetForm("queryForm");

      // 恢复公司筛选
      this.queryParams.companyId = currentCompanyId;
      this.currentCompanyName = currentCompanyName;

      this.handleQuery();
    },
    /** 清除公司筛选 */
    clearCompanyFilter() {
      // 清除路由查询参数并重新初始化
      this.$router.replace({ path: '/merchant/store' }).then(() => {
        this.initializeFromRoute();
      });
    },
    /** 查看客户按钮操作 */
    handleViewCustomers(row) {
      // 跳转到客户管理页面，并传递门店UUID作为查询参数
      this.$router.push({
        path: '/merchant/ccuser',
        query: {
          storeUuid: row.storeUuid,
          storeName: row.name
        }
      }).catch(err => {
        // 如果跳转失败，提示用户需要先配置菜单
        this.$modal.msgError('客户管理页面尚未配置，请先在菜单管理中配置客户管理页面后再使用此功能');
      });
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加门店";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids[0];
      getStore(id).then(response => {
        this.form = response.data;
        this.companyDisplayName = this.getCompanyName(this.form.companyId);
        this.open = true;
        this.title = "修改门店";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateStore(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addStore(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除门店ID为"' + ids + '"的数据项？').then(function() {
        return delStore(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    }
  }
};
</script>
