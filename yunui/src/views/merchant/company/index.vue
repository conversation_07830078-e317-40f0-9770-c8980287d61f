<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="公司名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入公司名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="城市" prop="city">
        <el-input
          v-model="queryParams.city"
          placeholder="请输入城市"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="正常" value="1" />
          <el-option label="冻结" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['merchant:company:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['merchant:company:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['merchant:company:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-time"
          size="mini"
          :disabled="multiple"
          @click="handleBatchRenew"
          v-hasPermi="['merchant:company:edit']"
        >批量续费</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-refresh-left"
          size="mini"
          :disabled="!lastOperationId"
          @click="handleUndoBatchRenew"
          v-hasPermi="['merchant:company:edit']"
        >撤销续费</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="companyList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="公司名称" align="center" prop="name" :show-overflow-tooltip="true" />
      <el-table-column label="城市" align="center" prop="city" />
      <el-table-column label="地址" align="center" prop="address" :show-overflow-tooltip="true" />
      <el-table-column label="版本类型" align="center" prop="versionNames" width="300">
        <template slot-scope="scope">
          <div class="version-tags">
            <template v-if="scope.row.versionNames && scope.row.versionNames.length > 0">
              <el-tag
                v-for="(versionInfo, index) in scope.row.versionNames"
                :key="index"
                size="mini"
                effect="plain"
                :type="getVersionTagType(versionInfo)"
                class="version-tag"
              >
                {{ formatVersionDisplay(versionInfo) }}
              </el-tag>
            </template>
            <span v-else>-</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="余额" align="center" prop="balance" width="120">
        <template slot-scope="scope">
          <span style="color: #409EFF; font-weight: bold;">
            ¥{{ scope.row.balance ? parseFloat(scope.row.balance).toFixed(2) : '0.00' }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === '1'" type="success">正常</el-tag>
          <el-tag v-else type="danger">冻结</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="280">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-document"
            @click="handleViewDetail(scope.row)"
            v-hasPermi="['merchant:company:query']"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleViewStores(scope.row)"
            v-hasPermi="['merchant:store:list']"
          >查看门店</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-money"
            @click="handleRecharge(scope.row)"
            v-hasPermi="['merchant:company:recharge']"
          >充值</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-bank-card"
            @click="handleWithdrawal(scope.row)"
            v-hasPermi="['finance:withdrawal:add']"
          >提现</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-setting"
            @click="handleVersionManagement(scope.row)"
            v-hasPermi="['merchant:company:edit']"
          >版本管理</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-time"
            @click="handleRenew(scope.row)"
            v-hasPermi="['merchant:company:edit']"
          >续费</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['merchant:company:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['merchant:company:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改公司对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="公司名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入公司名称" />
        </el-form-item>
        <el-form-item label="城市" prop="city">
          <el-input v-model="form.city" placeholder="请输入城市" />
        </el-form-item>
        <el-form-item label="地址" prop="address">
          <el-input v-model="form.address" type="textarea" placeholder="请输入地址" />
        </el-form-item>
        <el-form-item label="地址描述" prop="addressDesc">
          <el-input v-model="form.addressDesc" type="textarea" placeholder="请输入地址描述" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="1">正常</el-radio>
            <el-radio label="0">冻结</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 充值对话框 -->
    <el-dialog title="公司充值" :visible.sync="rechargeOpen" width="500px" append-to-body>
      <el-form ref="rechargeForm" :model="rechargeForm" :rules="rechargeRules" label-width="100px">
        <el-form-item label="公司名称">
          <el-input v-model="rechargeForm.companyName" disabled />
        </el-form-item>
        <el-form-item label="业务类型" prop="businessType">
          <el-select v-model="rechargeForm.businessType" placeholder="请选择业务类型" style="width: 100%">
            <el-option label="余额充值" value="RECHARGE" />
          </el-select>
        </el-form-item>
        <el-form-item label="充值金额" prop="amount">
          <el-input-number
            v-model="rechargeForm.amount"
            :precision="2"
            :step="100"
            :min="0.01"
            :max="999999.99"
            placeholder="请输入充值金额"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="支付方式" prop="payType">
          <el-select v-model="rechargeForm.payType" placeholder="请选择支付方式" style="width: 100%">
            <el-option label="现金" value="CASH" />
            <el-option label="银行转账" value="BANK_TRANSFER" />
            <el-option label="支付宝" value="ALIPAY" />
            <el-option label="微信支付" value="WECHAT_PAY" />
            <el-option label="其他" value="OTHER" />
          </el-select>
        </el-form-item>
        <el-form-item label="外部交易ID" prop="externalTransactionId">
          <el-input
            v-model="rechargeForm.externalTransactionId"
            placeholder="请输入外部交易ID（可选）"
          />
        </el-form-item>
        <el-form-item label="交易描述" prop="description">
          <el-input
            v-model="rechargeForm.description"
            placeholder="请输入交易描述"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="rechargeForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入充值备注（可选）"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitRecharge" :loading="rechargeLoading">确 定</el-button>
        <el-button @click="cancelRecharge">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 提现申请对话框 -->
    <el-dialog title="提现申请" :visible.sync="withdrawalOpen" width="600px" append-to-body>
      <el-form ref="withdrawalForm" :model="withdrawalForm" :rules="withdrawalRules" label-width="120px">
        <el-form-item label="公司名称">
          <el-input v-model="withdrawalForm.companyName" disabled />
        </el-form-item>
        <el-form-item label="申请人">
          <el-input v-model="withdrawalForm.applicantName" disabled />
        </el-form-item>
        <el-form-item label="当前余额">
          <el-input v-model="withdrawalForm.currentBalance" disabled>
            <template slot="append">元</template>
          </el-input>
        </el-form-item>
        <el-form-item label="提现类型" prop="withdrawalType">
          <el-radio-group v-model="withdrawalForm.withdrawalType" @change="handleWithdrawalTypeChange">
            <el-radio :label="1">自行开票（无手续费）</el-radio>
            <el-radio :label="2">零工提现（扣10%手续费）</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="申请金额" prop="applyAmount">
          <el-input-number
            v-model="withdrawalForm.applyAmount"
            :precision="2"
            :step="100"
            :min="0.01"
            :max="withdrawalForm.maxAmount"
            placeholder="请输入提现金额"
            style="width: 100%"
            @change="calculateFee"
          />
        </el-form-item>
        <el-form-item label="手续费" v-if="withdrawalForm.withdrawalType === 2">
          <el-input v-model="withdrawalForm.feeAmount" disabled>
            <template slot="append">元</template>
          </el-input>
        </el-form-item>
        <el-form-item label="实际到账金额">
          <el-input v-model="withdrawalForm.actualAmount" disabled>
            <template slot="append">元</template>
          </el-input>
        </el-form-item>

        <!-- 自行开票信息 -->
        <template v-if="withdrawalForm.withdrawalType === 1">
          <el-divider content-position="left">开票信息</el-divider>
          <el-form-item label="发票类型" prop="invoiceType">
            <el-select v-model="withdrawalForm.invoiceType" placeholder="请选择发票类型" style="width: 100%">
              <el-option label="增值税专用发票" value="VAT_SPECIAL" />
              <el-option label="增值税普通发票" value="VAT_ORDINARY" />
              <el-option label="增值税电子发票" value="VAT_ELECTRONIC" />
            </el-select>
          </el-form-item>
          <el-form-item label="发票抬头" prop="invoiceTitle">
            <el-input v-model="withdrawalForm.invoiceTitle" placeholder="请输入发票抬头" />
          </el-form-item>
          <el-form-item label="纳税人识别号" prop="taxNumber">
            <el-input v-model="withdrawalForm.taxNumber" placeholder="请输入纳税人识别号" />
          </el-form-item>
          <el-form-item label="开户行及账号" prop="invoiceBankInfo">
            <el-input v-model="withdrawalForm.invoiceBankInfo" placeholder="请输入开户行及账号" />
          </el-form-item>
          <el-form-item label="注册地址及电话" prop="registeredAddress">
            <el-input v-model="withdrawalForm.registeredAddress" placeholder="请输入注册地址及电话" />
          </el-form-item>
          <el-form-item label="发票内容" prop="invoiceContent">
            <el-input v-model="withdrawalForm.invoiceContent" placeholder="请输入发票内容/项目名称" />
          </el-form-item>
          <el-divider></el-divider>
        </template>

        <el-form-item label="开户银行" prop="bankName">
          <el-input v-model="withdrawalForm.bankName" placeholder="请输入开户银行" />
        </el-form-item>
        <el-form-item label="银行账号" prop="bankAccount">
          <el-input v-model="withdrawalForm.bankAccount" placeholder="请输入银行账号" />
        </el-form-item>
        <el-form-item label="开户人姓名" prop="accountHolder">
          <el-input v-model="withdrawalForm.accountHolder" placeholder="请输入开户人姓名" />
        </el-form-item>
        <el-form-item label="申请原因" prop="applyReason">
          <el-input
            v-model="withdrawalForm.applyReason"
            type="textarea"
            :rows="3"
            placeholder="请输入申请原因"
          />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="withdrawalForm.remark"
            type="textarea"
            :rows="2"
            placeholder="请输入备注（可选）"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelWithdrawal">取 消</el-button>
        <el-button type="primary" :loading="withdrawalLoading" @click="submitWithdrawal">提交申请</el-button>
      </div>
    </el-dialog>

    <!-- 版本管理对话框 -->
    <el-dialog title="版本管理" :visible.sync="versionManagementOpen" width="800px" append-to-body>
      <div v-if="currentCompany">
        <h4 style="margin-bottom: 15px; color: #409EFF;">{{ currentCompany.name }} - 版本管理</h4>

        <!-- 当前版本信息 -->
        <el-card class="box-card" style="margin-bottom: 20px;">
          <div slot="header" class="clearfix">
            <span style="font-weight: bold;">当前版本</span>
          </div>
          <!-- 加载中状态 -->
          <div v-if="companyVersionsLoading" style="text-align: center; padding: 20px;">
            <i class="el-icon-loading"></i>
            <span style="margin-left: 8px; color: #666;">正在加载版本信息...</span>
          </div>
          <!-- 有版本数据 -->
          <div v-else-if="companyVersions.length > 0">
            <div class="version-tags">
              <el-tag
                v-for="(versionName, index) in companyVersions"
                :key="index"
                effect="plain"
                type="primary"
                size="medium"
                class="version-tag"
                style="margin-right: 8px; margin-bottom: 8px;"
              >
                {{ versionName }}
              </el-tag>
            </div>
          </div>
          <!-- 暂无版本信息 -->
          <div v-else style="color: #999; text-align: center; padding: 20px;">
            暂无版本信息
          </div>
        </el-card>

        <!-- 新增版本 -->
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span style="font-weight: bold;">新增版本</span>
          </div>
          <el-form ref="versionForm" :model="versionForm" :rules="versionRules" label-width="100px">
            <el-form-item label="选择版本" prop="selectedVersions">
              <el-select
                v-model="selectedVersionsArray"
                multiple
                collapse-tags
                placeholder="请选择要添加的版本"
                style="width: 100%;"
                @change="handleVersionsChange"
                :disabled="availableVersionsForAdd.length === 0"
              >
                <el-option
                  v-for="version in availableVersionsForAdd"
                  :key="version.uuid"
                  :label="version.name + ' (¥' + version.price + ')'"
                  :value="version.uuid"
                />
              </el-select>
              <!-- 无可新增版本提示 -->
              <div v-if="availableVersionsForAdd.length === 0" style="color: #999; font-size: 12px; margin-top: 5px;">
                该公司已拥有所有可用版本，无需新增
              </div>
            </el-form-item>
            <el-form-item label="过期时间" prop="expireTime">
              <el-date-picker
                v-model="versionForm.expireTime"
                type="datetime"
                placeholder="选择过期时间"
                style="width: 100%;"
                value-format="yyyy-MM-dd HH:mm:ss"
                default-time="23:59:59"
              />
            </el-form-item>
          </el-form>
        </el-card>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="submitVersionForm"
          :loading="versionLoading"
          :disabled="availableVersionsForAdd.length === 0"
        >确 定</el-button>
        <el-button @click="cancelVersionManagement">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 续费对话框 -->
    <el-dialog title="版本续费" :visible.sync="renewOpen" width="800px" append-to-body>
      <div v-if="currentRenewCompany">
        <h4 style="margin-bottom: 15px; color: #409EFF;">{{ currentRenewCompany.name }} - 版本续费</h4>

        <!-- 版本续费列表 -->
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span style="font-weight: bold;">当前版本及到期时间</span>
          </div>

          <!-- 加载中状态 -->
          <div v-if="renewLoading" style="text-align: center; padding: 20px;">
            <i class="el-icon-loading"></i>
            <span style="margin-left: 8px; color: #666;">正在加载版本信息...</span>
          </div>

          <!-- 版本列表 -->
          <div v-else-if="renewVersions.length > 0">
            <el-table :data="renewVersions" border style="width: 100%">
              <el-table-column prop="name" label="版本名称" width="200">
                <template slot-scope="scope">
                  <el-tag type="primary" effect="plain">{{ scope.row.name }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="description" label="版本描述" show-overflow-tooltip />
              <el-table-column prop="price" label="价格" width="100" align="center">
                <template slot-scope="scope">
                  ¥{{ scope.row.price }}
                </template>
              </el-table-column>
              <el-table-column prop="expireTime" label="当前到期时间" width="180" align="center">
                <template slot-scope="scope">
                  <span :class="getExpireTimeClass(scope.row.expireTime)">
                    {{ scope.row.expireTime }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column label="新到期时间" width="200" align="center">
                <template slot-scope="scope">
                  <el-date-picker
                    v-model="scope.row.newExpireTime"
                    type="datetime"
                    placeholder="选择新到期时间"
                    size="mini"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    default-time="23:59:59"
                    style="width: 100%;"
                  />
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 暂无版本信息 -->
          <div v-else style="color: #999; text-align: center; padding: 20px;">
            该公司暂无版本信息
          </div>
        </el-card>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="submitRenew"
          :loading="renewSubmitLoading"
          :disabled="renewVersions.length === 0"
        >确 定</el-button>
        <el-button @click="cancelRenew">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 公司详情对话框 -->
    <el-dialog title="公司详情" :visible.sync="detailOpen" width="80%" append-to-body>
      <div v-if="companyDetail">
        <!-- 公司基本信息 -->
        <el-card class="box-card" style="margin-bottom: 20px;">
          <div slot="header" class="clearfix">
            <span style="font-weight: bold; font-size: 16px;">公司基本信息</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="detail-item">
                <span class="detail-label">公司名称：</span>
                <span class="detail-value">{{ companyDetail.company.name }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <span class="detail-label">城市：</span>
                <span class="detail-value">{{ companyDetail.company.city || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <span class="detail-label">版本类型：</span>
                <div class="detail-value">
                  <div class="version-tags">
                    <template v-if="companyDetail.company.versionNames && companyDetail.company.versionNames.length > 0">
                      <el-tag
                        v-for="(versionInfo, index) in companyDetail.company.versionNames"
                        :key="index"
                        size="mini"
                        effect="plain"
                        :type="getVersionTagType(versionInfo)"
                        class="version-tag"
                      >
                        {{ formatVersionDisplay(versionInfo) }}
                      </el-tag>
                    </template>
                    <span v-else>-</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-top: 15px;">
            <el-col :span="12">
              <div class="detail-item">
                <span class="detail-label">地址：</span>
                <span class="detail-value">{{ companyDetail.company.address || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <span class="detail-label">状态：</span>
                <el-tag :type="companyDetail.company.status === '1' ? 'success' : 'danger'">
                  {{ companyDetail.company.status === '1' ? '正常' : '冻结' }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <span class="detail-label">创建时间：</span>
                <span class="detail-value">{{ parseTime(companyDetail.company.createTime, '{y}-{m}-{d}') }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 门店信息 -->
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span style="font-weight: bold; font-size: 16px;">门店信息（共{{ companyDetail.stores.length }}家）</span>
          </div>

          <div v-if="companyDetail.stores.length === 0" style="text-align: center; color: #999; padding: 40px;">
            暂无门店信息
          </div>

          <div v-else>
            <el-collapse v-model="activeStores" accordion>
              <el-collapse-item
                v-for="(storeData, index) in companyDetail.stores"
                :key="storeData.store.id"
                :title="`${storeData.store.name} (ID: ${storeData.store.id})`"
                :name="index.toString()"
              >
                <template slot="title">
                  <div style="display: flex; align-items: center; width: 100%;">
                    <span style="font-weight: bold; margin-right: 10px;">{{ storeData.store.name }}</span>
                    <el-tag size="mini" :type="storeData.store.status === 1 ? 'success' : 'danger'">
                      {{ storeData.store.status === 1 ? '营业' : '关闭' }}
                    </el-tag>
                    <span style="margin-left: auto; color: #999; font-size: 12px;">
                      ID: {{ storeData.store.id }}
                    </span>
                  </div>
                </template>

                <!-- 门店基本信息 -->
                <el-row :gutter="20" style="margin-bottom: 20px;">
                  <el-col :span="24">
                    <h4 style="margin: 0 0 15px 0; color: #409EFF;">基本信息</h4>
                  </el-col>
                  <el-col :span="8">
                    <div class="detail-item">
                      <span class="detail-label">门店编号：</span>
                      <span class="detail-value">{{ storeData.store.storeUuid || '-' }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="detail-item">
                      <span class="detail-label">负责人：</span>
                      <span class="detail-value">{{ storeData.store.manager || '-' }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="detail-item">
                      <span class="detail-label">联系电话：</span>
                      <span class="detail-value">{{ storeData.store.phone || '-' }}</span>
                    </div>
                  </el-col>
                </el-row>

                <el-row :gutter="20" style="margin-bottom: 20px;">
                  <el-col :span="8">
                    <div class="detail-item">
                      <span class="detail-label">门店电话：</span>
                      <span class="detail-value">{{ storeData.store.mobile || '-' }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="detail-item">
                      <span class="detail-label">邮箱：</span>
                      <span class="detail-value">{{ storeData.store.email || '-' }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="detail-item">
                      <span class="detail-label">门店等级：</span>
                      <span class="detail-value">{{ storeData.store.level || '-' }}</span>
                    </div>
                  </el-col>
                </el-row>

                <el-row :gutter="20" style="margin-bottom: 20px;">
                  <el-col :span="12">
                    <div class="detail-item">
                      <span class="detail-label">地址：</span>
                      <span class="detail-value">{{ storeData.store.address || '-' }}</span>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="detail-item">
                      <span class="detail-label">营业时间：</span>
                      <span class="detail-value">{{ storeData.store.businessHours || '-' }}</span>
                    </div>
                  </el-col>
                </el-row>

                <el-row :gutter="20" style="margin-bottom: 20px;" v-if="storeData.store.introduce">
                  <el-col :span="24">
                    <div class="detail-item">
                      <span class="detail-label">门店简介：</span>
                      <span class="detail-value">{{ storeData.store.introduce }}</span>
                    </div>
                  </el-col>
                </el-row>

                <!-- 门店详细信息 -->
                <div v-if="storeData.store_info">
                  <el-divider></el-divider>
                  <el-row :gutter="20" style="margin-bottom: 20px;">
                    <el-col :span="24">
                      <h4 style="margin: 0 0 15px 0; color: #67C23A;">详细配置</h4>
                    </el-col>
                    <el-col :span="8">
                      <div class="detail-item">
                        <span class="detail-label">店铺类型：</span>
                        <span class="detail-value">{{ storeData.store_info.type || '-' }}</span>
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <div class="detail-item">
                        <span class="detail-label">店铺性质：</span>
                        <span class="detail-value">{{ storeData.store_info.nature || '-' }}</span>
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <div class="detail-item">
                        <span class="detail-label">单位性质：</span>
                        <span class="detail-value">{{ storeData.store_info.unitNature || '-' }}</span>
                      </div>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20" style="margin-bottom: 20px;">
                    <el-col :span="8">
                      <div class="detail-item">
                        <span class="detail-label">营业执照：</span>
                        <span class="detail-value">{{ storeData.store_info.businessLicense || '-' }}</span>
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <div class="detail-item">
                        <span class="detail-label">门店规格：</span>
                        <span class="detail-value">{{ storeData.store_info.storeSpecifications || '-' }}</span>
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <div class="detail-item">
                        <span class="detail-label">健康度：</span>
                        <span class="detail-value">{{ storeData.store_info.healthDegree || '-' }}</span>
                      </div>
                    </el-col>
                  </el-row>

                  <!-- 发票信息 -->
                  <div v-if="storeData.store_info.isInvoice">
                    <h5 style="margin: 15px 0 10px 0; color: #E6A23C;">发票信息</h5>
                    <el-row :gutter="20" style="margin-bottom: 15px;">
                      <el-col :span="8">
                        <div class="detail-item">
                          <span class="detail-label">发票抬头：</span>
                          <span class="detail-value">{{ storeData.store_info.invoiceTitle || '-' }}</span>
                        </div>
                      </el-col>
                      <el-col :span="8">
                        <div class="detail-item">
                          <span class="detail-label">发票代码：</span>
                          <span class="detail-value">{{ storeData.store_info.invoiceCode || '-' }}</span>
                        </div>
                      </el-col>
                      <el-col :span="8">
                        <div class="detail-item">
                          <span class="detail-label">发票电话：</span>
                          <span class="detail-value">{{ storeData.store_info.invoicePhone || '-' }}</span>
                        </div>
                      </el-col>
                    </el-row>
                    <el-row :gutter="20" style="margin-bottom: 15px;">
                      <el-col :span="12">
                        <div class="detail-item">
                          <span class="detail-label">发票地址：</span>
                          <span class="detail-value">{{ storeData.store_info.invoiceAddress || '-' }}</span>
                        </div>
                      </el-col>
                      <el-col :span="6">
                        <div class="detail-item">
                          <span class="detail-label">发票银行：</span>
                          <span class="detail-value">{{ storeData.store_info.invoiceBank || '-' }}</span>
                        </div>
                      </el-col>
                      <el-col :span="6">
                        <div class="detail-item">
                          <span class="detail-label">发票账户：</span>
                          <span class="detail-value">{{ storeData.store_info.invoiceAccount || '-' }}</span>
                        </div>
                      </el-col>
                    </el-row>
                  </div>

                  <!-- 服务配置 -->
                  <h5 style="margin: 15px 0 10px 0; color: #F56C6C;">服务配置</h5>
                  <el-row :gutter="20">
                    <el-col :span="6">
                      <div class="detail-item">
                        <span class="detail-label">保洁服务：</span>
                        <el-tag size="mini" :type="storeData.store_info.isCleanKeeping ? 'success' : 'info'">
                          {{ storeData.store_info.isCleanKeeping ? '支持' : '不支持' }}
                        </el-tag>
                      </div>
                    </el-col>
                    <el-col :span="6">
                      <div class="detail-item">
                        <span class="detail-label">家政员工：</span>
                        <el-tag size="mini" :type="storeData.store_info.isHousekeepingStaff ? 'success' : 'info'">
                          {{ storeData.store_info.isHousekeepingStaff ? '有' : '无' }}
                        </el-tag>
                      </div>
                    </el-col>
                    <el-col :span="6">
                      <div class="detail-item">
                        <span class="detail-label">兼职服务：</span>
                        <el-tag size="mini" :type="storeData.store_info.isPartTimeJob ? 'success' : 'info'">
                          {{ storeData.store_info.isPartTimeJob ? '支持' : '不支持' }}
                        </el-tag>
                      </div>
                    </el-col>
                    <el-col :span="6">
                      <div class="detail-item">
                        <span class="detail-label">免费服务：</span>
                        <el-tag size="mini" :type="storeData.store_info.isFree ? 'success' : 'info'">
                          {{ storeData.store_info.isFree ? '有' : '无' }}
                        </el-tag>
                      </div>
                    </el-col>
                  </el-row>

                  <!-- 折扣信息 -->
                  <div v-if="storeData.store_info.isDiscount" style="margin-top: 15px;">
                    <h5 style="margin: 15px 0 10px 0; color: #909399;">折扣信息</h5>
                    <el-row :gutter="20">
                      <el-col :span="8">
                        <div class="detail-item">
                          <span class="detail-label">折扣比例：</span>
                          <span class="detail-value">{{ storeData.store_info.discountRatio || '-' }}%</span>
                        </div>
                      </el-col>
                      <el-col :span="8">
                        <div class="detail-item">
                          <span class="detail-label">折扣到期：</span>
                          <span class="detail-value">{{ parseTime(storeData.store_info.discountExpiryDate, '{y}-{m}-{d}') || '-' }}</span>
                        </div>
                      </el-col>
                    </el-row>
                  </div>
                </div>
                <div v-else style="color: #999; font-style: italic; margin-top: 10px;">
                  暂无详细配置信息
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </el-card>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="detailOpen = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 批量续费对话框 -->
    <el-dialog title="批量续费" :visible.sync="batchRenewOpen" width="600px" append-to-body>
      <el-form ref="batchRenewForm" :model="batchRenewForm" :rules="batchRenewRules" label-width="100px">
        <el-form-item label="选中公司" prop="selectedCompanies">
          <div class="selected-companies">
            <el-tag
              v-for="company in selectedCompanies"
              :key="company.id"
              size="small"
              type="info"
              style="margin-right: 8px; margin-bottom: 4px;"
            >
              {{ company.name }}
            </el-tag>
            <div v-if="selectedCompanies.length === 0" style="color: #999;">
              请先选择要续费的公司
            </div>
          </div>
        </el-form-item>

        <el-form-item label="续费模式" prop="renewMode">
          <el-radio-group v-model="batchRenewForm.renewMode" @change="handleRenewModeChange">
            <el-radio label="EXTEND_MONTHS">延长指定月数</el-radio>
            <el-radio label="CUSTOM_DATE">设置到期时间</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item
          v-if="batchRenewForm.renewMode === 'EXTEND_MONTHS'"
          label="延长期限"
          prop="extendMonths"
        >
          <el-select v-model="batchRenewForm.extendMonths" placeholder="请选择延长期限" style="width: 200px;">
            <el-option
              v-for="option in extendOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
          <span style="margin-left: 10px; color: #666;">或</span>
          <el-input-number
            v-model="batchRenewForm.extendMonths"
            :min="1"
            :max="120"
            style="width: 120px; margin-left: 10px;"
            placeholder="自定义"
          />
          <span style="margin-left: 5px; color: #666;">个月</span>
        </el-form-item>

        <el-form-item
          v-if="batchRenewForm.renewMode === 'CUSTOM_DATE'"
          label="到期时间"
          prop="customExpireTime"
        >
          <el-date-picker
            v-model="batchRenewForm.customExpireTime"
            type="datetime"
            placeholder="请选择到期时间"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            :picker-options="{
              disabledDate(time) {
                return time.getTime() < Date.now() - 8.64e7; // 不能选择昨天之前的日期
              }
            }"
            style="width: 100%;"
          />
        </el-form-item>

        <el-form-item
          v-if="batchRenewForm.renewMode === 'EXTEND_MONTHS'"
          label="操作类型"
          prop="operationType"
        >
          <el-radio-group v-model="batchRenewForm.operationType">
            <el-radio label="EXTEND">延长（在现有到期时间基础上延长）</el-radio>
            <el-radio label="RESET">重置（从当前时间开始计算）</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="batchRenewForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入操作备注（可选）"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelBatchRenew">取 消</el-button>
        <el-button type="primary" :loading="batchRenewLoading" @click="submitBatchRenew">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 撤销操作对话框 -->
    <el-dialog title="撤销批量续费" :visible.sync="undoOpen" width="500px" append-to-body>
      <div style="text-align: center; padding: 20px;">
        <i class="el-icon-warning" style="font-size: 48px; color: #E6A23C;"></i>
        <p style="margin: 20px 0; font-size: 16px;">
          确定要撤销上次的批量续费操作吗？
        </p>
        <p style="color: #666; font-size: 14px;">
          此操作将恢复所有公司版本的原始到期时间，且不可逆转。
        </p>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="undoOpen = false">取 消</el-button>
        <el-button type="danger" :loading="undoLoading" @click="confirmUndo">确认撤销</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listCompany, getCompany, delCompany, addCompany, updateCompany, getCompanyStoresDetail, rechargeCompany, getAvailableVersions, getCompanyVersions, addCompanyVersion, getCompanyVersionsDetail, renewCompanyVersions, batchRenewCompanyVersions, undoBatchRenewCompanyVersions } from "@/api/merchant/company";
import { createWithdrawal } from "@/api/finance/withdrawal";

export default {
  name: "Company",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 公司表格数据
      companyList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示详情弹出层
      detailOpen: false,
      // 公司详情数据
      companyDetail: null,
      // 展开的门店索引
      activeStores: '',
      // 是否显示充值弹出层
      rechargeOpen: false,
      // 充值加载状态
      rechargeLoading: false,
      // 充值表单数据
      rechargeForm: {
        companyId: null,
        companyName: '',
        businessType: 'RECHARGE',
        amount: null,
        payType: null,
        externalTransactionId: '',
        description: '',
        remark: ''
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        city: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: "公司名称不能为空", trigger: "blur" }
        ],
      },
      // 充值表单校验
      rechargeRules: {
        businessType: [
          { required: true, message: "请选择业务类型", trigger: "change" }
        ],
        amount: [
          { required: true, message: "充值金额不能为空", trigger: "blur" },
          { type: 'number', min: 0.01, max: 999999.99, message: "充值金额必须在0.01-999999.99之间", trigger: "blur" }
        ],
        payType: [
          { required: true, message: "请选择支付方式", trigger: "change" }
        ],
        description: [
          { required: true, message: "请输入交易描述", trigger: "blur" }
        ]
      },
      // 版本管理相关数据
      versionManagementOpen: false,
      versionLoading: false,
      companyVersionsLoading: false,
      currentCompany: null,
      availableVersions: [],
      companyVersions: [],
      selectedVersionsArray: [],
      versionForm: {
        selectedVersions: '',
        expireTime: null
      },
      versionRules: {
        selectedVersions: [
          { required: true, message: "请选择要添加的版本", trigger: "change" }
        ],
        expireTime: [
          { required: true, message: "请选择过期时间", trigger: "change" }
        ]
      },
      // 续费相关数据
      renewOpen: false,
      renewLoading: false,
      renewSubmitLoading: false,
      currentRenewCompany: null,
      renewVersions: [],
      // 提现相关数据
      withdrawalOpen: false,
      withdrawalLoading: false,
      withdrawalForm: {
        companyId: null,
        companyName: '',
        applicantName: '',
        currentBalance: 0,
        withdrawalType: 1,
        applyAmount: null,
        feeAmount: 0,
        actualAmount: 0,
        maxAmount: 0,
        // 开票信息
        invoiceType: '',
        invoiceTitle: '',
        taxNumber: '',
        invoiceBankInfo: '',
        registeredAddress: '',
        invoiceContent: '',
        // 银行信息
        bankName: '',
        bankAccount: '',
        accountHolder: '',
        applyReason: '',
        remark: ''
      },
      withdrawalRules: {
        withdrawalType: [
          { required: true, message: "请选择提现类型", trigger: "change" }
        ],
        applyAmount: [
          { required: true, message: "申请金额不能为空", trigger: "blur" },
          { type: 'number', min: 0.01, message: "申请金额必须大于0", trigger: "blur" }
        ],
        // 开票信息验证（仅自行开票时需要）
        invoiceType: [
          { required: true, message: "请选择发票类型", trigger: "change" }
        ],
        invoiceTitle: [
          { required: true, message: "发票抬头不能为空", trigger: "blur" }
        ],
        taxNumber: [
          { required: true, message: "纳税人识别号不能为空", trigger: "blur" },
          { pattern: /^[A-Z0-9]{15,20}$/, message: "纳税人识别号格式不正确", trigger: "blur" }
        ],
        invoiceBankInfo: [
          { required: true, message: "开户行及账号不能为空", trigger: "blur" }
        ],
        registeredAddress: [
          { required: true, message: "注册地址及电话不能为空", trigger: "blur" }
        ],
        invoiceContent: [
          { required: true, message: "发票内容不能为空", trigger: "blur" }
        ],
        // 银行信息验证
        bankName: [
          { required: true, message: "开户银行不能为空", trigger: "blur" }
        ],
        bankAccount: [
          { required: true, message: "银行账号不能为空", trigger: "blur" }
        ],
        accountHolder: [
          { required: true, message: "开户人姓名不能为空", trigger: "blur" }
        ],
        applyReason: [
          { required: true, message: "申请原因不能为空", trigger: "blur" }
        ]
      },
      // 批量续费相关数据
      batchRenewOpen: false,
      batchRenewLoading: false,
      batchRenewForm: {
        renewMode: 'EXTEND_MONTHS', // EXTEND_MONTHS: 延长月数, CUSTOM_DATE: 自定义时间
        extendMonths: 12,
        customExpireTime: null,
        operationType: 'EXTEND',
        remark: ''
      },
      batchRenewRules: {
        renewMode: [
          { required: true, message: "请选择续费模式", trigger: "change" }
        ],
        extendMonths: [
          { required: true, message: "延长月数不能为空", trigger: "blur" },
          { type: 'number', min: 1, max: 120, message: "延长月数必须在1-120之间", trigger: "blur" }
        ],
        customExpireTime: [
          { required: true, message: "请选择到期时间", trigger: "change" }
        ],
        operationType: [
          { required: true, message: "请选择操作类型", trigger: "change" }
        ]
      },
      selectedCompanies: [],
      // 撤销相关数据
      undoOpen: false,
      undoLoading: false,
      lastOperationId: null,
      // 预设的延长期限选项
      extendOptions: [
        { label: '1个月', value: 1 },
        { label: '3个月', value: 3 },
        { label: '6个月', value: 6 },
        { label: '1年', value: 12 },
        { label: '2年', value: 24 }
      ]
    };
  },
  computed: {
    /** 可新增的版本列表（去重已有版本） */
    availableVersionsForAdd() {
      if (!this.availableVersions || !this.companyVersions) {
        return [];
      }

      // 过滤掉公司已有的版本
      return this.availableVersions.filter(version => {
        return !this.companyVersions.includes(version.name);
      });
    }
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询公司列表 */
    getList() {
      this.loading = true;
      listCompany(this.queryParams).then(response => {
        this.companyList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: null,
        city: null,
        address: null,
        addressDesc: null,
        status: "1"
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加公司";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids[0];
      getCompany(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改公司";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateCompany(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addCompany(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 查看详情按钮操作 */
    handleViewDetail(row) {
      this.companyDetail = null;
      this.activeStores = '';
      this.detailOpen = true;

      // 获取公司门店详情
      getCompanyStoresDetail(row.id).then(response => {
        this.companyDetail = response.data;
      }).catch(error => {
        this.$modal.msgError('获取公司详情失败');
        this.detailOpen = false;
      });
    },
    /** 查看门店按钮操作 */
    handleViewStores(row) {
      // 跳转到门店管理页面，并传递公司ID作为查询参数
      this.$router.push({
        path: '/merchant/store',
        query: {
          companyId: row.id,
          companyName: row.name
        }
      }).catch(err => {
        // 如果跳转失败，提示用户需要先配置菜单
        this.$modal.msgError('门店管理页面尚未配置，请先在菜单管理中配置门店管理页面后再使用此功能');
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除公司编号为"' + ids + '"的数据项？').then(function() {
        return delCompany(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 充值按钮操作 */
    handleRecharge(row) {
      this.resetRechargeForm();
      this.rechargeForm.companyId = row.id;
      this.rechargeForm.companyName = row.name;
      this.rechargeOpen = true;
    },
    /** 重置充值表单 */
    resetRechargeForm() {
      this.rechargeForm = {
        companyId: null,
        companyName: '',
        businessType: 'RECHARGE',
        amount: null,
        payType: null,
        externalTransactionId: '',
        description: '',
        remark: ''
      };
      if (this.$refs.rechargeForm) {
        this.$refs.rechargeForm.resetFields();
      }
    },
    /** 取消充值 */
    cancelRecharge() {
      this.rechargeOpen = false;
      this.resetRechargeForm();
    },
    /** 提交充值 */
    submitRecharge() {
      this.$refs["rechargeForm"].validate(valid => {
        if (valid) {
          this.rechargeLoading = true;
          const rechargeData = {
            businessType: this.rechargeForm.businessType,
            amount: this.rechargeForm.amount,
            payType: this.rechargeForm.payType,
            externalTransactionId: this.rechargeForm.externalTransactionId || null,
            description: this.rechargeForm.description,
            remark: this.rechargeForm.remark
          };

          rechargeCompany(this.rechargeForm.companyId, rechargeData).then(response => {
            this.$modal.msgSuccess("充值成功");
            this.rechargeOpen = false;
            this.resetRechargeForm();
            this.getList(); // 刷新列表
          }).catch(error => {
            console.error('充值失败:', error);
          }).finally(() => {
            this.rechargeLoading = false;
          });
        }
      });
    },
    /** 提现按钮操作 */
    handleWithdrawal(row) {
      this.resetWithdrawalForm();
      this.withdrawalForm.companyId = row.id;
      this.withdrawalForm.companyName = row.name;
      this.withdrawalForm.applicantName = this.$store.state.user.name || '当前用户';
      this.withdrawalForm.currentBalance = row.balance;
      this.withdrawalForm.maxAmount = row.balance;
      this.calculateFee();
      this.withdrawalOpen = true;
    },
    /** 重置提现表单 */
    resetWithdrawalForm() {
      this.withdrawalForm = {
        companyId: null,
        companyName: '',
        applicantName: '',
        currentBalance: 0,
        withdrawalType: 1,
        applyAmount: null,
        feeAmount: 0,
        actualAmount: 0,
        maxAmount: 0,
        // 开票信息
        invoiceType: '',
        invoiceTitle: '',
        taxNumber: '',
        invoiceBankInfo: '',
        registeredAddress: '',
        invoiceContent: '',
        // 银行信息
        bankName: '',
        bankAccount: '',
        accountHolder: '',
        applyReason: '',
        remark: ''
      };
      if (this.$refs.withdrawalForm) {
        this.$refs.withdrawalForm.resetFields();
      }
    },
    /** 取消提现 */
    cancelWithdrawal() {
      this.withdrawalOpen = false;
      this.resetWithdrawalForm();
    },
    /** 提现类型变化处理 */
    handleWithdrawalTypeChange() {
      this.calculateFee();
      // 动态设置验证规则
      this.updateWithdrawalRules();
    },
    /** 更新提现表单验证规则 */
    updateWithdrawalRules() {
      const isInvoiceRequired = this.withdrawalForm.withdrawalType === 1;

      // 开票信息字段的验证规则
      const invoiceFields = ['invoiceType', 'invoiceTitle', 'taxNumber', 'invoiceBankInfo', 'registeredAddress', 'invoiceContent'];

      invoiceFields.forEach(field => {
        if (isInvoiceRequired) {
          // 自行开票时需要验证开票信息
          this.$set(this.withdrawalRules, field, this.withdrawalRules[field]);
        } else {
          // 零工提现时不需要验证开票信息
          this.$delete(this.withdrawalRules, field);
        }
      });

      // 重新验证表单
      if (this.$refs.withdrawalForm) {
        this.$refs.withdrawalForm.clearValidate();
      }
    },
    /** 计算手续费 */
    calculateFee() {
      if (!this.withdrawalForm.applyAmount) {
        this.withdrawalForm.feeAmount = 0;
        this.withdrawalForm.actualAmount = 0;
        return;
      }

      const feeRate = this.withdrawalForm.withdrawalType === 2 ? 0.1 : 0;
      this.withdrawalForm.feeAmount = (this.withdrawalForm.applyAmount * feeRate).toFixed(2);
      this.withdrawalForm.actualAmount = (this.withdrawalForm.applyAmount - this.withdrawalForm.feeAmount).toFixed(2);
    },
    /** 提交提现申请 */
    submitWithdrawal() {
      this.$refs["withdrawalForm"].validate(valid => {
        if (valid) {
          this.withdrawalLoading = true;

          // 准备开票信息（仅自行开票时需要）
          let invoiceInfo = null;
          if (this.withdrawalForm.withdrawalType === 1) {
            invoiceInfo = {
              invoiceType: this.withdrawalForm.invoiceType,
              invoiceTitle: this.withdrawalForm.invoiceTitle,
              taxNumber: this.withdrawalForm.taxNumber,
              invoiceBankInfo: this.withdrawalForm.invoiceBankInfo,
              registeredAddress: this.withdrawalForm.registeredAddress,
              invoiceContent: this.withdrawalForm.invoiceContent
            };
          }

          const withdrawalData = {
            companyUuid: this.withdrawalForm.companyId,
            withdrawalType: this.withdrawalForm.withdrawalType,
            applyAmount: this.withdrawalForm.applyAmount,
            bankName: this.withdrawalForm.bankName,
            bankAccount: this.withdrawalForm.bankAccount,
            accountHolder: this.withdrawalForm.accountHolder,
            invoiceInfo: invoiceInfo,
            applyReason: this.withdrawalForm.applyReason,
            remark: this.withdrawalForm.remark
          };

          createWithdrawal(withdrawalData).then(response => {
            this.$modal.msgSuccess("提现申请提交成功，请等待审核");
            this.withdrawalOpen = false;
            this.resetWithdrawalForm();
            this.getList(); // 刷新列表
          }).catch(error => {
            console.error('提现申请失败:', error);
          }).finally(() => {
            this.withdrawalLoading = false;
          });
        }
      });
    },
    /** 格式化版本显示 */
    formatVersionDisplay(versionInfo) {
      if (!versionInfo || !versionInfo.includes(':')) {
        return versionInfo || '';
      }

      // 找到第一个冒号的位置，分割版本名和时间
      const firstColonIndex = versionInfo.indexOf(':');
      if (firstColonIndex === -1) {
        return versionInfo;
      }

      const name = versionInfo.substring(0, firstColonIndex);
      const expireTime = versionInfo.substring(firstColonIndex + 1);

      if (!expireTime) {
        return name;
      }

      // 格式化时间显示，显示完整的日期时间
      const date = new Date(expireTime);
      if (isNaN(date.getTime())) {
        return `${name}:${expireTime}`;
      }

      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      return `${name}:${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    /** 获取版本标签类型 */
    getVersionTagType(versionInfo) {
      if (!versionInfo || !versionInfo.includes(':')) {
        return 'primary';
      }

      // 找到第一个冒号的位置，获取时间部分
      const firstColonIndex = versionInfo.indexOf(':');
      if (firstColonIndex === -1) {
        return 'primary';
      }

      const expireTime = versionInfo.substring(firstColonIndex + 1);
      if (!expireTime) {
        return 'primary';
      }

      const expireDate = new Date(expireTime);
      if (isNaN(expireDate.getTime())) {
        return 'primary';
      }

      const now = new Date();
      const diffDays = Math.ceil((expireDate - now) / (1000 * 60 * 60 * 24));

      if (diffDays < 0) {
        return 'danger'; // 已过期
      } else if (diffDays <= 7) {
        return 'warning'; // 7天内到期
      } else if (diffDays <= 30) {
        return 'info'; // 30天内到期
      } else {
        return 'success'; // 正常
      }
    },
    /** 基于过期时间获取版本标签类型 */
    getVersionTagTypeByExpireTime(expireTime) {
      if (!expireTime) {
        return 'primary';
      }

      const expireDate = new Date(expireTime);
      if (isNaN(expireDate.getTime())) {
        return 'primary';
      }

      const now = new Date();
      const diffDays = Math.ceil((expireDate - now) / (1000 * 60 * 60 * 24));

      if (diffDays < 0) {
        return 'danger'; // 已过期 - 红色
      } else if (diffDays <= 7) {
        return 'warning'; // 7天内到期 - 橙色
      } else if (diffDays <= 30) {
        return 'info'; // 30天内到期 - 蓝色
      } else {
        return 'success'; // 正常 - 绿色
      }
    },
    /** 版本管理按钮操作 */
    handleVersionManagement(row) {
      this.currentCompany = row;
      this.resetVersionForm();

      // 重置版本数据状态
      this.companyVersions = [];
      this.companyVersionsLoading = true;

      this.versionManagementOpen = true;

      // 获取可用版本列表
      this.getAvailableVersionsList();

      // 获取公司当前版本
      this.getCompanyVersionsList(row.id);
    },
    /** 获取可用版本列表 */
    getAvailableVersionsList() {
      console.log('开始获取可用版本列表');
      getAvailableVersions().then(response => {
        console.log('获取可用版本列表成功:', response);
        this.availableVersions = response.data || [];
      }).catch(error => {
        console.error('获取可用版本列表失败:', error);
        this.availableVersions = [];
        // 显示错误提示，但不阻止用户查看当前版本
        this.$modal.msgError('获取可用版本列表失败，但不影响查看当前版本');
      });
    },
    /** 获取公司版本列表 */
    getCompanyVersionsList(companyId) {
      console.log('开始获取公司版本列表，公司ID:', companyId);
      this.companyVersionsLoading = true;

      getCompanyVersions(companyId).then(response => {
        console.log('获取公司版本列表成功:', response);
        this.companyVersions = response.data || [];
      }).catch(error => {
        console.error('获取公司版本列表失败:', error);
        this.companyVersions = [];
        // 显示详细的错误信息
        if (error.response && error.response.data && error.response.data.msg) {
          this.$modal.msgError(`获取公司版本信息失败: ${error.response.data.msg}`);
        } else {
          this.$modal.msgError('获取公司版本信息失败，请检查公司是否存在');
        }
      }).finally(() => {
        this.companyVersionsLoading = false;
      });
    },
    /** 处理版本选择变化 */
    handleVersionsChange(value) {
      // 将选择的版本UUID数组转为逗号分隔的字符串
      this.versionForm.selectedVersions = value.join(',');
    },
    /** 重置版本表单 */
    resetVersionForm() {
      this.versionForm = {
        selectedVersions: '',
        expireTime: null
      };
      this.selectedVersionsArray = [];
      if (this.$refs.versionForm) {
        this.$refs.versionForm.resetFields();
      }
    },
    /** 取消版本管理 */
    cancelVersionManagement() {
      this.versionManagementOpen = false;
      this.resetVersionForm();
      this.currentCompany = null;
    },
    /** 提交版本表单（优化版，无备注） */
    submitVersionForm() {
      this.$refs["versionForm"].validate(valid => {
        if (valid) {
          this.versionLoading = true;

          // 为每个选择的版本创建关联（移除备注字段）
          const versionPromises = this.selectedVersionsArray.map(versionUuid => {
            const versionData = {
              version_uuid: versionUuid,
              expire_time: this.versionForm.expireTime
            };
            return addCompanyVersion(this.currentCompany.id, versionData);
          });

          Promise.all(versionPromises).then(() => {
            this.$modal.msgSuccess("版本关联成功");
            this.versionManagementOpen = false;
            this.resetVersionForm();
            this.getList(); // 刷新公司列表
            // 重新获取公司版本列表
            this.getCompanyVersionsList(this.currentCompany.id);
          }).catch(error => {
            console.error('版本关联失败:', error);
            this.$modal.msgError('版本关联失败：' + (error.message || '未知错误'));
          }).finally(() => {
            this.versionLoading = false;
          });
        }
      });
    },
    /** 续费按钮操作 */
    handleRenew(row) {
      this.currentRenewCompany = row;
      this.renewVersions = [];
      this.renewLoading = true;
      this.renewOpen = true;

      // 获取公司版本详细信息
      this.getRenewVersionsDetail(row.id);
    },
    /** 获取续费版本详细信息 */
    getRenewVersionsDetail(companyId) {
      this.renewLoading = true;

      getCompanyVersionsDetail(companyId).then(response => {
        console.log('获取版本详细信息成功:', response);
        this.renewVersions = (response.data || []).map(version => ({
          ...version,
          newExpireTime: version.expireTime // 初始化新到期时间为当前到期时间
        }));
      }).catch(error => {
        console.error('获取版本详细信息失败:', error);
        this.renewVersions = [];
        if (error.response && error.response.data && error.response.data.msg) {
          this.$modal.msgError(`获取版本信息失败: ${error.response.data.msg}`);
        } else {
          this.$modal.msgError('获取版本信息失败，请检查公司是否存在');
        }
      }).finally(() => {
        this.renewLoading = false;
      });
    },
    /** 获取到期时间样式类 */
    getExpireTimeClass(expireTime) {
      if (!expireTime) {
        return '';
      }

      const expireDate = new Date(expireTime);
      if (isNaN(expireDate.getTime())) {
        return '';
      }

      const now = new Date();
      const diffDays = Math.ceil((expireDate - now) / (1000 * 60 * 60 * 24));

      if (diffDays < 0) {
        return 'expire-time-expired'; // 已过期 - 红色
      } else if (diffDays <= 7) {
        return 'expire-time-warning'; // 7天内到期 - 橙色
      } else if (diffDays <= 30) {
        return 'expire-time-info'; // 30天内到期 - 蓝色
      } else {
        return 'expire-time-success'; // 正常 - 绿色
      }
    },
    /** 取消续费 */
    cancelRenew() {
      this.renewOpen = false;
      this.currentRenewCompany = null;
      this.renewVersions = [];
    },
    /** 提交续费 */
    submitRenew() {
      // 验证是否有修改
      const renewData = this.renewVersions
        .filter(version => version.newExpireTime && version.newExpireTime !== version.expireTime)
        .map(version => ({
          version_uuid: version.uuid,
          expire_time: version.newExpireTime
        }));

      if (renewData.length === 0) {
        this.$modal.msgWarning('请至少修改一个版本的到期时间');
        return;
      }

      this.renewSubmitLoading = true;

      renewCompanyVersions(this.currentRenewCompany.id, renewData).then(response => {
        this.$modal.msgSuccess("续费成功");
        this.renewOpen = false;
        this.getList(); // 刷新公司列表
      }).catch(error => {
        console.error('续费失败:', error);
        if (error.response && error.response.data && error.response.data.msg) {
          this.$modal.msgError(`续费失败: ${error.response.data.msg}`);
        } else {
          this.$modal.msgError('续费失败，请重试');
        }
      }).finally(() => {
        this.renewSubmitLoading = false;
      });
    },
    /** 批量续费按钮操作 */
    handleBatchRenew() {
      if (this.ids.length === 0) {
        this.$modal.msgWarning('请先选择要续费的公司');
        return;
      }

      // 获取选中的公司信息
      this.selectedCompanies = this.companyList.filter(company => this.ids.includes(company.id));

      // 重置表单
      this.resetBatchRenewForm();
      this.batchRenewOpen = true;
    },
    /** 重置批量续费表单 */
    resetBatchRenewForm() {
      this.batchRenewForm = {
        renewMode: 'EXTEND_MONTHS',
        extendMonths: 12,
        customExpireTime: null,
        operationType: 'EXTEND',
        remark: ''
      };
      if (this.$refs.batchRenewForm) {
        this.$refs.batchRenewForm.resetFields();
      }
    },
    /** 续费模式变化处理 */
    handleRenewModeChange() {
      // 清除验证错误
      if (this.$refs.batchRenewForm) {
        this.$refs.batchRenewForm.clearValidate();
      }

      // 根据续费模式动态设置验证规则
      if (this.batchRenewForm.renewMode === 'EXTEND_MONTHS') {
        // 延长月数模式需要验证extendMonths
        this.$set(this.batchRenewRules, 'extendMonths', [
          { required: true, message: "延长月数不能为空", trigger: "blur" },
          { type: 'number', min: 1, max: 120, message: "延长月数必须在1-120之间", trigger: "blur" }
        ]);
        this.$delete(this.batchRenewRules, 'customExpireTime');
      } else {
        // 自定义时间模式需要验证customExpireTime
        this.$set(this.batchRenewRules, 'customExpireTime', [
          { required: true, message: "请选择到期时间", trigger: "change" }
        ]);
        this.$delete(this.batchRenewRules, 'extendMonths');
      }
    },
    /** 取消批量续费 */
    cancelBatchRenew() {
      this.batchRenewOpen = false;
      this.resetBatchRenewForm();
      this.selectedCompanies = [];
    },
    /** 提交批量续费 */
    submitBatchRenew() {
      this.$refs["batchRenewForm"].validate(valid => {
        if (valid) {
          // 根据续费模式验证必填字段
          if (this.batchRenewForm.renewMode === 'EXTEND_MONTHS' && !this.batchRenewForm.extendMonths) {
            this.$modal.msgError('请输入延长月数');
            return;
          }
          if (this.batchRenewForm.renewMode === 'CUSTOM_DATE' && !this.batchRenewForm.customExpireTime) {
            this.$modal.msgError('请选择到期时间');
            return;
          }

          this.$modal.confirm(`确定要对选中的 ${this.selectedCompanies.length} 个公司进行批量续费吗？`).then(() => {
            this.batchRenewLoading = true;

            let batchRenewData = {
              companyIds: this.ids,
              remark: this.batchRenewForm.remark
            };

            // 根据续费模式设置不同的参数
            if (this.batchRenewForm.renewMode === 'EXTEND_MONTHS') {
              batchRenewData.extendMonths = this.batchRenewForm.extendMonths;
              batchRenewData.operationType = this.batchRenewForm.operationType;
            } else {
              // 自定义时间模式，计算从当前时间到指定时间的月数
              const now = new Date();
              const expireTime = new Date(this.batchRenewForm.customExpireTime);
              const diffMonths = Math.ceil((expireTime - now) / (1000 * 60 * 60 * 24 * 30));

              if (diffMonths <= 0) {
                this.$modal.msgError('到期时间必须大于当前时间');
                this.batchRenewLoading = false;
                return;
              }

              batchRenewData.extendMonths = diffMonths;
              batchRenewData.operationType = 'RESET'; // 自定义时间模式使用重置模式
              batchRenewData.customExpireTime = this.batchRenewForm.customExpireTime;
            }

            batchRenewCompanyVersions(batchRenewData).then(response => {
              this.$modal.msgSuccess(response.msg);
              this.batchRenewOpen = false;
              this.resetBatchRenewForm();
              this.selectedCompanies = [];
              this.getList(); // 刷新列表

              // 保存操作ID用于撤销
              if (response.data && response.data.operationId) {
                this.lastOperationId = response.data.operationId;
                // 不再显示额外的提示信息，避免重复提示
              }
            }).catch(error => {
              console.error('批量续费失败:', error);
              if (error.response && error.response.data && error.response.data.msg) {
                this.$modal.msgError(`批量续费失败: ${error.response.data.msg}`);
              } else {
                this.$modal.msgError('批量续费失败，请重试');
              }
            }).finally(() => {
              this.batchRenewLoading = false;
            });
          }).catch(() => {
            // 用户取消操作
          });
        }
      });
    },
    /** 撤销批量续费操作 */
    handleUndoBatchRenew() {
      if (!this.lastOperationId) {
        this.$modal.msgWarning('没有可撤销的操作');
        return;
      }

      this.undoOpen = true;
    },
    /** 确认撤销操作 */
    confirmUndo() {
      this.undoLoading = true;

      const undoData = {
        operationId: this.lastOperationId
      };

      undoBatchRenewCompanyVersions(undoData).then(response => {
        this.$modal.msgSuccess(response.msg);
        this.undoOpen = false;
        this.lastOperationId = null;
        this.getList(); // 刷新列表
      }).catch(error => {
        console.error('撤销操作失败:', error);
        if (error.response && error.response.data && error.response.data.msg) {
          this.$modal.msgError(`撤销失败: ${error.response.data.msg}`);
        } else {
          this.$modal.msgError('撤销失败，请重试');
        }
      }).finally(() => {
        this.undoLoading = false;
      });
    }
  }
};
</script>

<style scoped>
.detail-item {
  margin-bottom: 10px;
}

.detail-label {
  font-weight: bold;
  color: #606266;
  margin-right: 8px;
}

.detail-value {
  color: #303133;
}

.box-card {
  border-radius: 8px;
}

.box-card .clearfix:before,
.box-card .clearfix:after {
  display: table;
  content: "";
}

.box-card .clearfix:after {
  clear: both;
}

.el-collapse-item__header {
  font-size: 14px;
}

.el-collapse-item__content {
  padding-bottom: 15px;
}

/* 版本标签容器样式 */
.version-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  justify-content: center; /* 水平居中 */
  align-items: center;
}

/* 版本标签样式 */
.version-tag {
  margin-right: 5px;
  margin-bottom: 5px;
  font-size: 12px;
  white-space: nowrap;
  max-width: 220px;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 到期时间样式 */
.expire-time-expired {
  color: #F56C6C;
  font-weight: bold;
}

.expire-time-warning {
  color: #E6A23C;
  font-weight: bold;
}

.expire-time-info {
  color: #409EFF;
}

.expire-time-success {
  color: #67C23A;
}
</style>
