<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">

      <el-form-item label="公司名称" prop="companyName">
        <el-input
          v-model="queryParams.companyName"
          placeholder="请输入公司名称"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="营业执照名称" prop="licenseName">
        <el-input
          v-model="queryParams.licenseName"
          placeholder="请输入营业执照名称"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="营业执照代码" prop="licenseCode">
        <el-input
          v-model="queryParams.licenseCode"
          placeholder="请输入营业执照代码"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="法人姓名" prop="legalPersonName">
        <el-input
          v-model="queryParams.legalPersonName"
          placeholder="请输入法人姓名"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="电话号码" prop="phoneNumber">
        <el-input
          v-model="queryParams.phoneNumber"
          placeholder="请输入电话号码"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="审核状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择审核状态"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="(value, key) in statusMap"
            :key="key"
            :label="value"
            :value="parseInt(key)"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="时间范围">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">

      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['merchant:experienceTable:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['merchant:experienceTable:remove']"
        >删除</el-button>
      </el-col>

      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="storeList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50" align="center" header-align="center" />
      <el-table-column label="商户ID" prop="id" width="60" align="center" header-align="center" />
      <el-table-column label="公司名称" prop="companyName" min-width="150" align="center" header-align="center">
        <template slot-scope="scope">
          <div class="long-text-cell">{{ scope.row.companyName }}</div>
        </template>
      </el-table-column>
      <el-table-column label="法人姓名" prop="legalPersonName" min-width="100" align="center" header-align="center">
        <template slot-scope="scope">
          <div class="long-text-cell">{{ scope.row.legalPersonName }}</div>
        </template>
      </el-table-column>
      <el-table-column label="电话号码" prop="phoneNumber" width="120" align="center" header-align="center" />
      <el-table-column label="营业执照名称" prop="licenseName" min-width="150" align="center" header-align="center">
        <template slot-scope="scope">
          <div class="long-text-cell">{{ scope.row.licenseName }}</div>
        </template>
      </el-table-column>
      <el-table-column label="营业执照代码" prop="licenseCode" min-width="150" align="center" header-align="center">
        <template slot-scope="scope">
          <div class="long-text-cell">{{ scope.row.licenseCode }}</div>
        </template>
      </el-table-column>
          <el-table-column label="推荐人" prop="referrer" width="80" align="center" header-align="center" />
      <el-table-column label="审核状态" align="center" header-align="center" width="100">
        <template slot-scope="scope">
          <el-tag :type="statusTypeMap[scope.row.status]" effect="plain">
            {{ statusMap[scope.row.status] }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="法定地址" prop="legalAddress" min-width="150" align="center" header-align="center">
        <template slot-scope="scope">
          <div class="long-text-cell">{{ scope.row.legalAddress }}</div>
        </template>
      </el-table-column>
      <el-table-column label="详细地址" prop="detailAddress" min-width="150" align="center" header-align="center">
        <template slot-scope="scope">
          <div class="long-text-cell">{{ scope.row.detailAddress }}</div>
        </template>
      </el-table-column>

      <el-table-column label="选择的产品" prop="selectedProducts" min-width="200" align="center" header-align="center">
        <template slot-scope="scope">
          <div class="product-tags">
            <template v-if="scope.row.selectedProducts">
              <el-tag
                v-for="(productName, index) in formatProductTypes(scope.row.selectedProducts).split('、')"
                :key="index"
                size="mini"
                effect="plain"
                type="primary"
                class="product-tag"
              >
                {{ productName }}
              </el-tag>
            </template>
            <span v-else>-</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="总价" prop="totalPrice" width="80" align="center" header-align="center" />
      <el-table-column label="营业执照到期日期" align="center" header-align="center" prop="licenseExpiryDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.licenseExpiryDate) }}</span>
        </template>
      </el-table-column>
           <el-table-column label="跟进记录" width="80" fixed="right" align="center" header-align="center">
        <template slot-scope="scope">
          <div class="operation-buttons">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-document"
              @click="handleFollowUp(scope.row.id)"
              v-hasPermi="['merchant:experienceTable:query']"
            >查看跟进</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" fixed="right" header-align="center" class-name="small-padding fixed-width" width="120">
        <template slot-scope="scope">
          <div class="operation-buttons">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['merchant:experienceTable:edit']"
            >修改</el-button>
          </div>
          <div class="operation-buttons">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['merchant:experienceTable:remove']"
            >删除</el-button>
          </div>
          <div class="operation-buttons">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
              v-hasPermi="['merchant:experienceTable:query']"
            >审核</el-button>
          </div>

        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改商户配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1000px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="150px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="公司名称" prop="companyName">
              <el-input v-model="form.companyName" placeholder="请输入公司名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="法人姓名" prop="legalPersonName">
              <el-input v-model="form.legalPersonName" placeholder="请输入法人姓名" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="电话号码" prop="phoneNumber">
              <el-input v-model="form.phoneNumber" placeholder="请输入电话号码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="营业执照名称" prop="licenseName">
              <el-input v-model="form.licenseName" placeholder="请输入营业执照名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="营业执照代码" prop="licenseCode">
              <el-input v-model="form.licenseCode" placeholder="请输入营业执照代码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="法定地址" prop="legalAddress">
              <el-input v-model="form.legalAddress" placeholder="请输入法定地址" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="选择的地址" prop="selectedAddress">
              <el-input disabled v-model="form.selectedAddress" placeholder="请输入选择的地址" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="详细地址" prop="detailAddress">
              <el-input disabled v-model="form.detailAddress" placeholder="请输入详细地址" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="推荐人" prop="referrer">
              <el-input v-model="form.referrer" placeholder="请输入推荐人" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="选择的产品" prop="selectedProducts">
              <el-select
                v-model="selectedProductsArray"
                multiple
                collapse-tags
                placeholder="请选择产品"
                style="width: 100%;"
                @change="handleProductsChange"
              >
                <el-option
                  v-for="(value, key) in productTypeMap"
                  :key="key"
                  :label="value"
                  :value="key"
                />
              </el-select>
            </el-form-item>
          </el-col>

        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="总价" prop="totalPrice">
              <el-input-number v-model="form.totalPrice" :precision="2" :step="0.1" :min="0" controls-position="right" style="width: 100%;" />
            </el-form-item>
          </el-col>
                    <el-col :span="12">
            <el-form-item label="审核状态" prop="status">
              <el-select disabled v-model="form.status" placeholder="请选择审核状态" style="width: 100%;">
                <el-option
                  v-for="(value, key) in statusMap"
                  :key="key"
                  :label="value"
                  :value="parseInt(key)"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>

          <el-col :span="12">
            <el-form-item label="营业执照到期日期" prop="licenseExpiryDate">
              <el-date-picker
                v-model="form.licenseExpiryDate"
                type="date"
                placeholder="选择营业执照到期日期"
                value-format="yyyy-MM-dd"
                style="width: 100%;"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="营业执照图片" prop="licenseImage">
              <el-upload
                class="avatar-uploader"
                action="#"
                :auto-upload="false"
                :show-file-list="false"
                :on-change="file => beforeAvatarUpload(file.raw)"
              >
                <img v-if="form.licenseImage" :src="form.licenseImage" class="avatar" />
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 查看商户详情对话框 -->
    <el-dialog title="商户详情" :visible.sync="openView" width="800px" append-to-body>
      <el-descriptions :column="2" border>

        <el-descriptions-item label="公司名称">{{ form.companyName }}</el-descriptions-item>
        <el-descriptions-item label="法人姓名">{{ form.legalPersonName }}</el-descriptions-item>
        <el-descriptions-item label="电话号码">{{ form.phoneNumber }}</el-descriptions-item>
        <el-descriptions-item label="营业执照名称">{{ form.licenseName }}</el-descriptions-item>
        <el-descriptions-item label="营业执照代码">{{ form.licenseCode }}</el-descriptions-item>
        <el-descriptions-item label="法定地址">{{ form.legalAddress }}</el-descriptions-item>
        <el-descriptions-item label="选择的地址">{{ form.selectedAddress }}</el-descriptions-item>
        <el-descriptions-item label="详细地址">{{ form.detailAddress }}</el-descriptions-item>
        <el-descriptions-item label="推荐人">{{ form.referrer }}</el-descriptions-item>
        <el-descriptions-item label="审核状态">
          <el-tag :type="statusTypeMap[form.status]" effect="plain">
            {{ statusMap[form.status] }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="总价">{{ form.totalPrice }}</el-descriptions-item>
        <el-descriptions-item label="营业执照到期日期">{{ parseTime(form.licenseExpiryDate) }}</el-descriptions-item>
        <el-descriptions-item label="选择的产品">
          <div class="product-tags">
            <template v-if="form.selectedProducts">
              <el-tag
                v-for="(productName, index) in formatProductTypes(form.selectedProducts).split('、')"
                :key="index"
                size="mini"
                effect="plain"
                type="primary"
                class="product-tag"
              >
                {{ productName }}
              </el-tag>
            </template>
            <span v-else>-</span>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="营业执照图片" :span="2">
          <img v-if="form.licenseImage" :src="form.licenseImage" style="max-width: 100%; max-height: 300px;" />
          <span v-else>暂无图片</span>
        </el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button
          v-if="form.status === 0"
          type="primary"
          @click="handleApprove(form)"
          v-hasPermi="['merchant:experienceTable:edit']"
        >通过审核</el-button>
        <el-button
          v-if="form.status === 0"
          type="primary"
          @click="handleEmployeeReview(form)"
          v-hasPermi="['merchant:experienceTable:edit']"
        >小羽佳员工审核</el-button>
        <el-button @click="openView = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 跟进记录弹窗 -->
    <el-dialog
      title="跟进记录"
      :visible.sync="openFollowUp"
      width="650px"
      append-to-body
      custom-class="follow-up-dialog"
    >
      <div class="follow-up-container">
        <!-- 跟进记录列表 -->
        <div class="follow-up-list">
          <div v-if="followUpRecords.length === 0" class="follow-up-empty">
            <i class="el-icon-chat-dot-square empty-icon"></i>
            <p>暂无跟进记录</p>
          </div>
          <div v-for="record in followUpRecords" :key="record.id" class="follow-up-item">
            <div class="follow-up-header">
              <div class="follow-up-avatar">{{ record.operator.substr(0, 1) }}</div>
              <div class="follow-up-info">
                <div class="follow-up-operator">{{ record.operator }}</div>
                <div class="follow-up-date">
                  <i class="el-icon-time"></i>
                  {{ formatDateTime(record.date) }}
                </div>
              </div>
            </div>
            <div class="follow-up-content">
              <span class="follow-up-text">{{ record.content }}</span>
            </div>
          </div>
        </div>

        <!-- 跟进记录输入区域 -->
        <div class="follow-up-input-area">
          <div class="input-title">
            <i class="el-icon-edit"></i>
            <span>添加跟进记录</span>
          </div>
          <el-input
            type="textarea"
            v-model="followUpContent"
            placeholder="请输入跟进记录内容..."
            :rows="3"
            :maxlength="200"
            show-word-limit
            class="follow-up-textarea"
          ></el-input>

          <div class="follow-up-actions">
            <el-button
              type="primary"
              icon="el-icon-s-promotion"
              @click="publishFollowUp"
              v-hasPermi="['merchant:experienceTable:add']"
            >发布跟进</el-button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listStore, getStore, delStore, addStore, updateStore, followRecords, addFollowUpRecord, examineStore, examineEmployee } from "@/api/merchant/storeRegister";
import { getToken } from "@/utils/auth";

export default {
  name: "StoreRegister",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 商户表格数据
      storeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示查看弹出层
      openView: false,
      // 上传图片地址
      uploadImgUrl: process.env.VUE_APP_BASE_API + "/common/upload",
      // 上传头像的请求头
      headers: {
        Authorization: "Bearer " + getToken()
      },
      // 状态数据字典
      statusMap: {
        0: '待审核',
        1: '通过',
        2: '不通过',
        3: '全部',
        4: '员工审核'
      },
      // 状态样式映射
      statusTypeMap: {
        0: 'warning',
        1: 'success',
        2: 'danger',
        4: 'info'
      },
      // 产品类型映射
      productTypeMap: {
        1: '三嫂版',
        2: '保洁版',
        3: '搬家版',
        4: '维修版',
        5: '洗护版',
        6: '装修版'
      },
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        companyName: undefined,
        licenseName: undefined,
        licenseCode: undefined,
        legalPersonName: undefined,
        phoneNumber: undefined,
        status: 3,
        beginTime: undefined,
        endTime: undefined
      },
      // 表单参数
      form: {},
      // 选择的产品数组
      selectedProductsArray: [],
      // 是否显示跟进记录弹窗
      openFollowUp: false,
      // 当前商户ID
      currentMerchantId: null,
      // 当前商户名称
      currentMerchantName: '',
      // 跟进记录列表
      followUpRecords: [],
      // 跟进内容
      followUpContent: '',
      // 是否只看员工主动记录
      onlyStaffRecords: false,
      // 下次跟进日期
      nextFollowUpDate: '',
      // 跟进状态映射
      followUpStatusMap: {
        pending: { text: '待跟进', type: 'warning' },
        processing: { text: '跟进中', type: 'success' },
        expired: { text: '已失效', type: 'danger' }
      },
      // 表单校验
      rules: {
        companyName: [
          { required: true, message: "公司名称不能为空", trigger: "blur" }
        ],
        legalPersonName: [
          { required: true, message: "法人姓名不能为空", trigger: "blur" }
        ],
        phoneNumber: [
          { required: true, message: "电话号码不能为空", trigger: "blur" },
          { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码", trigger: "blur" }
        ],
        licenseName: [
          { required: true, message: "营业执照名称不能为空", trigger: "blur" }
        ],
        licenseCode: [
          { required: true, message: "营业执照代码不能为空", trigger: "blur" }
        ],
        licenseExpiryDate: [
          { required: true, message: "营业执照到期日期不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询商户列表 */
    getList() {
      this.loading = true;
      listStore(this.queryParams).then(response => {
        this.storeList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        companyName: undefined,
        licenseImage: undefined,
        licenseName: undefined,
        licenseCode: undefined,
        legalAddress: undefined,
        legalPersonName: undefined,
        phoneNumber: undefined,
        verifyCode: undefined,
        selectedAddress: undefined,
        detailAddress: undefined,
        referrer: undefined,
        selectedProducts: undefined,
        totalPrice: undefined,
        isAllSelected: 0,
        periodType: undefined,
        licenseExpiryDate: undefined,
        status: 2 // 默认为待审核
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      // 处理日期范围
      if (this.dateRange && this.dateRange.length === 2) {
        this.queryParams.beginTime = this.dateRange[0];
        this.queryParams.endTime = this.dateRange[1];
      } else {
        this.queryParams.beginTime = undefined;
        this.queryParams.endTime = undefined;
      }
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        companyName: undefined,
        licenseName: undefined,
        licenseCode: undefined,
        legalPersonName: undefined,
        phoneNumber: undefined,
        status: undefined,
        beginTime: undefined,
        endTime: undefined
      };
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加商户";
    },

    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids[0];
      getStore(id).then(response => {
        this.form = response.data;
        // 初始化选择的产品数组
        this.initSelectedProductsArray();
        this.open = true;
        this.title = "修改商户";
      });
    },

    /** 查看按钮操作 */
    handleView(row) {
      this.reset();
      const id = row.id;
      getStore(id).then(response => {
        this.form = response.data;
        this.openView = true;
      });
    },

    /** 初始化选择的产品数组 */
    initSelectedProductsArray() {
      if (this.form.selectedProducts) {
        // 将逗号分隔的字符串转为数组
        this.selectedProductsArray = this.form.selectedProducts.split(',').map(id => id);
      } else {
        this.selectedProductsArray = [];
      }
    },

    /** 处理产品选择变化 */
    handleProductsChange(value) {
      // 将选择的产品ID数组转为逗号分隔的字符串
      this.form.selectedProducts = value.join(',');
    },

    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != undefined) {
            updateStore(this.form).then(() => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addStore(this.form).then(() => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除商户编号为"' + ids + '"的数据项？').then(() => {
        delStore(ids).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        });
      }).catch(() => {});
    },

    // 文件上传中处理
    handleAvatarSuccess(_, file) {
      // 模拟上传成功，使用本地URL
      const reader = new FileReader();
      reader.readAsDataURL(file.raw);
      reader.onload = () => {
        this.form.licenseImage = reader.result;
      };
    },

    // 上传前校验
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isJPG) {
        this.$message.error('上传营业执照图片只能是 JPG/PNG 格式!');
      }
      if (!isLt2M) {
        this.$message.error('上传营业执照图片大小不能超过 2MB!');
      }

      // 模拟上传过程
      if (isJPG && isLt2M) {
        setTimeout(() => {
          this.handleAvatarSuccess({ url: URL.createObjectURL(file) }, { raw: file });
        }, 300);
        return false; // 阻止默认上传
      }
      return false;
    },

    /** 通过审核按钮操作 */
    handleApprove(row) {
      const id = row.id;
      this.$modal.confirm('确认要通过商户"' + row.companyName + '"的审核吗？').then(() => {
        // 调用审核接口
        examineStore(id).then(() => {
          this.$modal.msgSuccess("审核已通过");
          this.getList(); // 刷新列表
          this.openView = false; // 关闭查看弹窗
        });
      }).catch(() => {});
    },

    /** 拒绝申请按钮操作 */
    handleReject(row) {
      const id = row.id;
      this.$modal.confirm('确认要拒绝商户"' + row.companyName + '"的申请吗？').then(() => {
        // 获取商户信息
        getStore(id).then(response => {
          const storeData = response.data;
          // 更新状态为"不通过"
          storeData.status = 2; // 状态值2表示"不通过"
          // 调用更新接口
          updateStore(storeData).then(() => {
            this.$modal.msgSuccess("已拒绝申请");
            this.getList(); // 刷新列表
            this.openView = false; // 关闭查看弹窗
          });
        });
      }).catch(() => {});
    },

    /** 小羽佳员工审核按钮操作 */
    handleEmployeeReview(row) {
      const id = row.id;
      this.$modal.confirm('确认将商户"' + row.companyName + '"转交给小羽佳员工审核吗？').then(() => {
        // 直接调用员工审核接口
        examineEmployee(id).then(() => {
          this.$modal.msgSuccess("员工审核通过，已创建员工账号并发送短信通知");
          this.getList(); // 刷新列表
          this.openView = false; // 关闭查看弹窗
        }).catch(error => {
          this.$modal.msgError("员工审核失败：" + (error.message || "未知错误"));
        });
      }).catch(() => {});
    },

    /** 格式化产品类型 */
    formatProductTypes(productStr) {
      if (!productStr) return '';

      // 将逗号分隔的字符串转为数组
      const productIds = productStr.split(',');

      // 映射为产品名称并过滤掉无效的ID
      return productIds
        .map(id => this.productTypeMap[id])
        .filter(name => name)
        .map(name => `${name}`)
        .join('、');
    },

    /** 查看跟进按钮操作 */
    handleFollowUp(id) {
      // 打开跟进记录弹窗
      this.currentMerchantId=id;
      this.openFollowUp = true;
      // 模拟获取跟进记录数据
      this.getFollowUpRecords(id);
    },

    /** 获取跟进记录数据 */
    getFollowUpRecords(id) {
      // 调用API获取跟进记录数据
      followRecords(id).then(response => {
        if (response.rows && response.rows.length > 0) {
          // 将后端返回的数据格式转换为前端需要的格式
          this.followUpRecords = response.rows.map(item => {
            return {
              id: item.id,
              date: item.followUpTime, // 后端返回的跟进时间
              operator: item.followUpPerson, // 后端返回的跟进人
              status: this.mapFollowUpStatus(item.status), // 映射状态
              content: item.followUpContent // 后端返回的跟进内容
            };
          });
        } else {
          // 如果没有数据，显示空数组
          this.followUpRecords = [];
        }
      }).catch(error => {
        console.error("获取跟进记录失败:", error);
        this.$message.error("获取跟进记录失败");
        this.followUpRecords = [];
      });
    },

    /** 映射跟进状态 */
    mapFollowUpStatus(status) {
      // 根据后端返回的状态值映射到前端状态
      // 这里需要根据实际情况调整映射关系
      const statusMap = {
        0: 'pending',   // 待跟进
        1: 'processing', // 跟进中
        2: 'expired'     // 已失效
      };
      return statusMap[status] || 'pending';
    },

    /** 格式化日期时间 */
    formatDateTime(time) {
      if (!time) return '';
      // 使用全局挂载的parseTime方法格式化时间
      return this.parseTime(time, '{y}-{m}-{d} {h}:{i}');
    },

    /** 发布跟进记录 */
    publishFollowUp() {
      if (!this.followUpContent.trim()) {
        this.$message.warning('请输入跟进内容');
        return;
      }

      // 构建跟进记录数据
      const followUpData = {
        merchantId: this.currentMerchantId,
        followUpContent: this.followUpContent,
        followUpPerson: this.$store.getters.name // 添加跟进人字段，使用当前登录用户名
      };

      // 调用API保存跟进记录
      addFollowUpRecord(followUpData).then(() => {
        this.$message.success('发布跟进成功');
        this.followUpContent = '';
        // 重新获取跟进记录列表
        this.getFollowUpRecords(this.currentMerchantId);
      }).catch(error => {
        console.error("发布跟进记录失败:", error);
        this.$message.error("发布跟进记录失败");
      });
    },

    /** 上传图片 */
    uploadImage() {
      this.$message.info('图片上传功能待实现');
      // TODO: 实现图片上传功能
    }
  }
}
</script>

<style scoped>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 400px;
  height: 320px;
  display: block;
}

/* 长文本单元格样式 */
.long-text-cell {
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.5;
  max-height: 100px;
  overflow: auto;
  padding: 5px 0;
  text-align: center; /* 添加文本居中 */
}

/* 表格行高度调整 */
.el-table__row {
  height: auto !important;
}

/* 表格单元格高度调整 */
.el-table__cell {
  padding: 8px 0;
}

/* 操作按钮容器样式 */
.operation-buttons {
  display: flex;
  justify-content: center;
  margin: 5px 0;
}

/* 产品标签容器样式 */
.product-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  justify-content: center; /* 水平居中 */
}

/* 产品标签样式 */
.product-tag {
  margin-right: 5px;
  margin-bottom: 5px;
}

/* 跟进记录弹窗 */
.follow-up-dialog .el-dialog__header {
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #f9f9f9;
}

.follow-up-dialog .el-dialog__title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.follow-up-dialog .el-dialog__body {
  padding: 0;
}

/* 跟进记录容器 */
.follow-up-container {
  display: flex;
  flex-direction: column;
  height: 550px;
  background-color: #f9fafc;
}

/* 跟进记录列表 */
.follow-up-list {
  flex: 1;
  overflow-y: auto;
  padding: 15px 20px;
  margin-bottom: 0;
  border-bottom: 1px solid #ebeef5;
  background-color: #fff;
}

/* 空记录提示 */
.follow-up-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #909399;
}

.follow-up-empty .empty-icon {
  font-size: 48px;
  margin-bottom: 10px;
  color: #c0c4cc;
}

/* 跟进记录项 */
.follow-up-item {
  margin-bottom: 20px;
  padding: 15px;
  border-radius: 8px;
  background-color: #f9fafc;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.follow-up-item:hover {
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

/* 跟进记录头部 */
.follow-up-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

/* 跟进人头像 */
.follow-up-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #409EFF;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
  margin-right: 10px;
}

/* 跟进信息区域 */
.follow-up-info {
  flex: 1;
}

/* 跟进人名称 */
.follow-up-operator {
  font-size: 15px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

/* 跟进记录日期 */
.follow-up-date {
  font-size: 13px;
  color: #909399;
  display: flex;
  align-items: center;
}

.follow-up-date i {
  margin-right: 4px;
  font-size: 14px;
}

/* 跟进记录内容 */
.follow-up-content {
  padding: 5px 0 0 46px;
}

/* 跟进文本 */
.follow-up-text {
  font-size: 14px;
  line-height: 1.6;
  color: #606266;
  word-break: break-word;
}

/* 跟进记录输入区域 */
.follow-up-input-area {
  padding: 20px;
  background-color: #fff;
}

/* 输入标题 */
.input-title {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  font-size: 15px;
  font-weight: 600;
  color: #303133;
}

.input-title i {
  margin-right: 6px;
  color: #409EFF;
}

/* 文本输入框 */
.follow-up-textarea {
  margin-bottom: 15px;
}

.follow-up-textarea .el-textarea__inner {
  border-radius: 4px;
  border-color: #dcdfe6;
  transition: all 0.3s;
}

.follow-up-textarea .el-textarea__inner:focus {
  border-color: #409EFF;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

/* 跟进操作 */
.follow-up-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}
</style>