<template>
  <div class="app-container">
    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="产品昵称" prop="productName">
        <el-input
          v-model="queryParams.productName"
          placeholder="请输入产品昵称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="公司昵称" prop="companyName">
        <el-input
          v-model="queryParams.companyName"
          placeholder="请输入公司昵称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="产品类型" prop="serviceSkillName">
        <el-input
          v-model="queryParams.serviceSkillName"
          placeholder="请输入产品类型"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="产品状态" prop="productStatus">
        <el-select v-model="queryParams.productStatus" placeholder="请选择产品状态" clearable>
          <el-option label="正常" :value="0" />
          <el-option label="停用" :value="1" />
          <el-option label="待审核" :value="2" />
          <el-option label="审核失败" :value="3" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-refresh"
          size="mini"
          :disabled="multiple"
          @click="handleSync"
          v-hasPermi="['merchant:productSync:sync']"
        >同步选中</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-refresh-right"
          size="mini"
          @click="handleBatchSync"
          v-hasPermi="['merchant:productSync:sync']"
        >批量同步</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['merchant:productSync:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 表格数据 -->
    <el-table v-loading="loading" :data="productSyncList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="产品ID" align="center" prop="id" width="80" />
      <el-table-column label="产品昵称" align="center" prop="productName" :show-overflow-tooltip="true" />
      <el-table-column label="公司昵称" align="center" prop="companyName" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ scope.row.companyName || '未关联' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="产品类型" align="center" prop="serviceSkillName" :show-overflow-tooltip="true" />
      <el-table-column label="产品状态" align="center" prop="productStatus" width="100">
        <template slot-scope="scope">
          <el-tag :type="getStatusTagType(scope.row.productStatus)">
            {{ getStatusText(scope.row.productStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="160">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
            v-hasPermi="['merchant:productSync:query']"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="handleSyncSingle(scope.row)"
            v-hasPermi="['merchant:productSync:sync']"
          >同步</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 产品详情对话框 -->
    <el-dialog title="产品详情" :visible.sync="detailOpen" width="1000px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="产品ID">{{ detailForm.id }}</el-descriptions-item>
        <el-descriptions-item label="产品昵称">{{ detailForm.product_name }}</el-descriptions-item>
        <el-descriptions-item label="公司昵称">{{ detailForm.company_name || '未关联' }}</el-descriptions-item>
        <el-descriptions-item label="产品类型">{{ detailForm.service_skill_name }}</el-descriptions-item>
        <el-descriptions-item label="最低购买数">{{ detailForm.min_number }}</el-descriptions-item>
        <el-descriptions-item label="最高购买数">{{ detailForm.max_number }}</el-descriptions-item>
        <el-descriptions-item label="产品状态">
          <el-tag :type="getStatusTagType(detailForm.product_status)">
            {{ getStatusText(detailForm.product_status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ parseTime(detailForm.create_time, '{y}-{m}-{d} {h}:{i}:{s}') }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 产品图片 -->
      <div v-if="detailForm.productImages && detailForm.productImages.length > 0" style="margin-top: 20px;">
        <h4>产品图片</h4>
        <el-row :gutter="10">
          <el-col :span="6" v-for="image in detailForm.productImages" :key="image.id">
            <el-card :body-style="{ padding: '10px' }">
              <img :src="image.url" :alt="image.fileName" style="width: 100%; height: 120px; object-fit: cover;" @click="previewImage(image.url)">
              <div style="padding: 5px 0; font-size: 12px; color: #666;">
                {{ image.fileName }}
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 产品详情图 -->
      <div v-if="detailForm.productDetailImages && detailForm.productDetailImages.length > 0" style="margin-top: 20px;">
        <h4>产品详情图</h4>
        <el-row :gutter="10">
          <el-col :span="6" v-for="image in detailForm.productDetailImages" :key="image.id">
            <el-card :body-style="{ padding: '10px' }">
              <img :src="image.url" :alt="image.fileName" style="width: 100%; height: 120px; object-fit: cover;" @click="previewImage(image.url)">
              <div style="padding: 5px 0; font-size: 12px; color: #666;">
                {{ image.fileName }}
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 产品视频 -->
      <div v-if="detailForm.productVideos && detailForm.productVideos.length > 0" style="margin-top: 20px;">
        <h4>产品视频</h4>
        <el-row :gutter="10">
          <el-col :span="8" v-for="video in detailForm.productVideos" :key="video.id">
            <el-card :body-style="{ padding: '10px' }">
              <video :src="video.url" controls style="width: 100%; height: 150px;">
                您的浏览器不支持视频播放
              </video>
              <div style="padding: 5px 0; font-size: 12px; color: #666;">
                {{ video.fileName }}
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="detailOpen = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 图片预览对话框 -->
    <el-dialog title="图片预览" :visible.sync="imagePreviewVisible" width="60%" append-to-body>
      <div style="text-align: center;">
        <img :src="previewImageUrl" style="max-width: 100%; max-height: 500px;" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listProductSync,
  getProductSync,
  exportProductSync,
  syncProductData,
  batchSyncProductData
} from "@/api/merchant/productSync";

export default {
  name: "ProductSync",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 产品同步表格数据
      productSyncList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示详情弹出层
      detailOpen: false,
      // 是否显示图片预览弹出层
      imagePreviewVisible: false,
      // 预览图片URL
      previewImageUrl: '',
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        productName: null,
        companyName: null,
        serviceSkillName: null,
        productStatus: null,
        beginTime: null,
        endTime: null
      },
      // 详情表单参数
      detailForm: {}
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询产品同步列表 */
    getList() {
      this.loading = true;
      this.queryParams.beginTime = this.dateRange[0];
      this.queryParams.endTime = this.dateRange[1];
      listProductSync(this.queryParams).then(response => {
        this.productSyncList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(error => {
        console.error('获取产品同步列表失败:', error);
        this.loading = false;
        this.$message.error('获取数据失败，请重试');
      });
    },
    /** 获取状态文本 */
    getStatusText(status) {
      const statusMap = {
        0: '正常',
        1: '停用',
        2: '待审核',
        3: '审核失败'
      };
      return statusMap[status] || '未知状态';
    },
    /** 获取状态标签类型 */
    getStatusTagType(status) {
      const typeMap = {
        0: 'success',
        1: 'danger',
        2: 'warning',
        3: 'danger'
      };
      return typeMap[status] || 'info';
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      const productId = row.id;
      getProductSync(productId).then(response => {
        this.detailForm = response.data;
        this.detailOpen = true;
      }).catch(error => {
        console.error('获取产品详情失败:', error);
        this.$message.error('获取产品详情失败，请重试');
      });
    },
    /** 图片预览 */
    previewImage(imageUrl) {
      this.previewImageUrl = imageUrl;
      this.imagePreviewVisible = true;
    },
    /** 同步单个产品 */
    handleSyncSingle(row) {
      const productIds = row.id.toString();
      this.$modal.confirm('是否确认同步产品"' + row.productName + '"？').then(function() {
        return syncProductData(productIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("同步成功");
      }).catch(() => {});
    },
    /** 同步选中产品 */
    handleSync() {
      const productIds = this.ids.join(",");
      this.$modal.confirm('是否确认同步选中的产品？').then(function() {
        return syncProductData(productIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("同步成功");
      }).catch(() => {});
    },
    /** 批量同步所有产品 */
    handleBatchSync() {
      this.$modal.confirm('是否确认批量同步所有产品？此操作可能需要较长时间。').then(function() {
        return batchSyncProductData();
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("批量同步成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.queryParams.beginTime = this.dateRange[0];
      this.queryParams.endTime = this.dateRange[1];
      this.$modal.confirm('是否确认导出所有产品同步数据项？').then(() => {
        this.loading = true;
        return exportProductSync(this.queryParams);
      }).then(response => {
        this.$download.excel(response, '产品同步数据.xlsx');
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    }
  }
};
</script>

<style scoped>
.mb8 {
  margin-bottom: 8px;
}
</style>
