<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="公司名称" prop="companyUuid">
        <el-select
          v-model="queryParams.companyUuid"
          placeholder="请选择公司"
          clearable
          filterable
          style="width: 200px"
        >
          <el-option
            v-for="company in companyList"
            :key="company.id"
            :label="company.name"
            :value="company.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="流水号" prop="transactionNo">
        <el-input
          v-model="queryParams.transactionNo"
          placeholder="请输入交易流水号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="业务类型" prop="businessType">
        <el-select v-model="queryParams.businessType" placeholder="请选择业务类型" clearable>
          <el-option label="软件购买" value="SOFTWARE_PURCHASE" />
          <el-option label="软件续费" value="SOFTWARE_RENEWAL" />
          <el-option label="账户充值" value="ACCOUNT_RECHARGE" />
          <el-option label="余额充值" value="RECHARGE" />
          <el-option label="购买保险" value="INSURANCE_PURCHASE" />
          <el-option label="保险续费" value="INSURANCE_RENEWAL" />
          <el-option label="培训服务" value="TRAINING_SERVICE" />
          <el-option label="认证服务" value="CERTIFICATION_SERVICE" />
          <el-option label="退款" value="REFUND" />
          <el-option label="佣金结算" value="COMMISSION_SETTLEMENT" />
          <el-option label="账户开设费" value="ACCOUNT_OPENING_FEE" />
        </el-select>
      </el-form-item>
      <el-form-item label="交易类型" prop="transactionType">
        <el-select v-model="queryParams.transactionType" placeholder="请选择交易类型" clearable>
          <el-option label="收入" :value="1" />
          <el-option label="支出" :value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="操作人" prop="operatorName">
        <el-input
          v-model="queryParams.operatorName"
          placeholder="请输入操作人姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="交易状态" prop="transactionStatus">
        <el-select v-model="queryParams.transactionStatus" placeholder="请选择交易状态" clearable>
          <el-option label="待处理" value="PENDING" />
          <el-option label="成功" value="SUCCESS" />
          <el-option label="失败" value="FAILED" />
          <el-option label="已取消" value="CANCELLED" />
        </el-select>
      </el-form-item>
      <el-form-item label="交易时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['finance:transaction:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          :disabled="multiple"
          @click="handleExportSelected"
          v-hasPermi="['finance:transaction:export']"
        >导出选中</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="transactionList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="流水号" align="center" prop="transactionNo" width="180" />
      <el-table-column label="公司名称" align="center" width="150">
        <template slot-scope="scope">
          {{ getCompanyNameByUuid(scope.row.companyUuid) }}
        </template>
      </el-table-column>
      <el-table-column label="业务类型" align="center" prop="businessTypeName" width="120" />
      <el-table-column label="交易类型" align="center" prop="transactionTypeName" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.transactionType === 1 ? 'success' : 'danger'">
            {{ scope.row.transactionTypeName }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="交易金额" align="center" prop="amount" width="120">
        <template slot-scope="scope">
          <span :class="scope.row.transactionType === 1 ? 'text-success' : 'text-danger'">
            {{ scope.row.transactionType === 1 ? '+' : '-' }}{{ scope.row.amount }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="交易前余额" align="center" prop="balanceBefore" width="120" />
      <el-table-column label="交易后余额" align="center" prop="balanceAfter" width="120" />
      <el-table-column label="操作人" align="center" prop="operatorName" width="120" />
      <el-table-column label="交易状态" align="center" prop="transactionStatus" width="100">
        <template slot-scope="scope">
          <el-tag :type="getStatusTagType(scope.row.transactionStatus)">
            {{ getStatusText(scope.row.transactionStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="交易描述" align="center" prop="description" :show-overflow-tooltip="true" />
      <el-table-column label="交易时间" align="center" prop="transactionTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.transactionTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['finance:transaction:query']"
          >详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 流水详情对话框 -->
    <el-dialog title="流水详情" :visible.sync="open" width="800px" append-to-body>
      <el-descriptions :column="2" border v-if="form">
        <el-descriptions-item label="流水号">{{ form.transactionNo }}</el-descriptions-item>
        <el-descriptions-item label="公司名称">{{ getCompanyNameByUuid(form.companyUuid) }}</el-descriptions-item>
        <el-descriptions-item label="业务类型">{{ form.businessTypeName }}</el-descriptions-item>
        <el-descriptions-item label="交易类型">
          <el-tag :type="form.transactionType === 1 ? 'success' : 'danger'">
            {{ form.transactionTypeName }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="交易金额">
          <span :class="form.transactionType === 1 ? 'text-success' : 'text-danger'">
            {{ form.transactionType === 1 ? '+' : '-' }}{{ form.amount }}
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="交易前余额">{{ form.balanceBefore }}</el-descriptions-item>
        <el-descriptions-item label="交易后余额">{{ form.balanceAfter }}</el-descriptions-item>
        <el-descriptions-item label="关联订单号">{{ form.relatedOrderNo || '无' }}</el-descriptions-item>
        <el-descriptions-item label="支付方式">{{ form.payTypeName || form.payType || '无' }}</el-descriptions-item>
        <el-descriptions-item label="外部交易ID">{{ form.externalTransactionId || '无' }}</el-descriptions-item>
        <el-descriptions-item label="操作人">{{ form.operatorName }}</el-descriptions-item>
        <el-descriptions-item label="交易状态">
          <el-tag :type="getStatusTagType(form.transactionStatus)">
            {{ getStatusText(form.transactionStatus) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="交易时间">{{ parseTime(form.transactionTime) }}</el-descriptions-item>
        <el-descriptions-item label="交易描述" :span="2">{{ form.description }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ form.remark || '无' }}</el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listTransaction, getTransaction, exportTransaction } from "@/api/finance/transaction";
import { listCompany } from "@/api/merchant/company";

export default {
  name: "PlatformFlow",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 流水表格数据
      transactionList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 日期范围
      dateRange: [],
      // 公司列表数据
      companyList: [],
      // 公司UUID到名称的映射
      companyMap: {},
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        companyUuid: null,
        transactionNo: null,
        businessType: null,
        transactionType: null,
        operatorName: null,
        transactionStatus: null,
        beginTime: null,
        endTime: null
      },
      // 表单参数
      form: {}
    };
  },
  created() {
    this.getCompanyList();
    this.getList();
  },
  methods: {
    /** 获取公司列表 */
    getCompanyList() {
      listCompany({ pageNum: 1, pageSize: 1000 }).then(response => {
        this.companyList = response.rows || [];
        // 建立UUID到名称的映射关系
        this.companyMap = {};
        this.companyList.forEach(company => {
          this.companyMap[company.id] = company.name;
        });
      }).catch(error => {
        console.error('获取公司列表失败:', error);
        this.$message.error('获取公司列表失败');
      });
    },
    /** 根据UUID获取公司名称 */
    getCompanyNameByUuid(uuid) {
      return this.companyMap[uuid] || uuid || '未知公司';
    },
    /** 获取状态标签类型 */
    getStatusTagType(status) {
      const statusMap = {
        'SUCCESS': 'success',
        'PENDING': 'warning',
        'FAILED': 'danger',
        'CANCELLED': 'info'
      };
      return statusMap[status] || 'info';
    },
    /** 获取状态文本 */
    getStatusText(status) {
      const statusMap = {
        'SUCCESS': '成功',
        'PENDING': '待处理',
        'FAILED': '失败',
        'CANCELLED': '已取消'
      };
      return statusMap[status] || status;
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 查询流水列表 */
    getList() {
      this.loading = true;
      this.queryParams.beginTime = this.dateRange[0];
      this.queryParams.endTime = this.dateRange[1];
      listTransaction(this.queryParams).then(response => {
        this.transactionList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {};
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.reset();
      const transactionId = row.id;
      getTransaction(transactionId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "流水详情";
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      // 获取当前查询条件下的数据并添加公司名称
      this.loading = true;
      const exportParams = { ...this.queryParams };
      exportParams.beginTime = this.dateRange[0];
      exportParams.endTime = this.dateRange[1];

      // 先获取数据，然后添加公司名称字段
      listTransaction(exportParams).then(response => {
        const exportData = response.rows.map(item => ({
          ...item,
          companyName: this.getCompanyNameByUuid(item.companyUuid)
        }));

        this.download('/finance/transaction/export', {
          ...exportParams,
          exportData: JSON.stringify(exportData)
        }, `transaction_${new Date().getTime()}.xlsx`);
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    /** 导出选中按钮操作 */
    handleExportSelected() {
      if (this.ids.length === 0) {
        this.$message.warning('请选择要导出的数据');
        return;
      }

      // 获取选中的数据并添加公司名称
      const selectedData = this.transactionList
        .filter(item => this.ids.includes(item.id))
        .map(item => ({
          ...item,
          companyName: this.getCompanyNameByUuid(item.companyUuid)
        }));

      this.download('/finance/transaction/export', {
        ids: this.ids.join(','),
        exportData: JSON.stringify(selectedData)
      }, `transaction_selected_${new Date().getTime()}.xlsx`);
    }
  }
};
</script>

<style scoped>
.text-success {
  color: #67C23A;
}
.text-danger {
  color: #F56C6C;
}
</style>
