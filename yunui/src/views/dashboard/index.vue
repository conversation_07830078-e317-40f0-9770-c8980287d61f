<template>
  <div class="app-container home">
    <!-- 顶部统计卡片 -->
    <el-row :gutter="30" class="card-row">
      <el-col :xs="24" :sm="12" :md="6" :lg="6">
        <el-card shadow="hover" class="data-card">
          <div class="card-content-wrapper">
            <div class="card-icon blue">
              <i class="el-icon-office-building"></i>
            </div>
            <div class="card-content">
              <div class="card-title">客户企业总数</div>
              <div class="card-value">235</div>
              <div class="card-footer">
                <span class="up"><i class="el-icon-top"></i> 8.5%</span>
                较上月
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6">
        <el-card shadow="hover" class="data-card">
          <div class="card-content-wrapper">
            <div class="card-icon green">
              <i class="el-icon-money"></i>
            </div>
            <div class="card-content">
              <div class="card-title">本月收入</div>
              <div class="card-value">¥125,648</div>
              <div class="card-footer">
                <span class="up"><i class="el-icon-top"></i> 12.2%</span>
                较上月
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6">
        <el-card shadow="hover" class="data-card">
          <div class="card-content-wrapper">
            <div class="card-icon orange">
              <i class="el-icon-user"></i>
            </div>
            <div class="card-content">
              <div class="card-title">近7天新增客户</div>
              <div class="card-value">17</div>
              <div class="card-footer">
                <span class="up"><i class="el-icon-top"></i> 25.3%</span>
                较上周
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6">
        <el-card shadow="hover" class="data-card">
          <div class="card-content-wrapper">
            <div class="card-icon purple">
              <i class="el-icon-s-claim"></i>
            </div>
            <div class="card-content">
              <div class="card-title">待处理工单</div>
              <div class="card-value">23</div>
              <div class="card-footer">
                <span class="down"><i class="el-icon-bottom"></i> 15.1%</span>
                较昨日
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 订阅收入和客户增长趋势 -->
    <el-row :gutter="30" class="chart-row">
      <el-col :xs="24" :lg="12">
        <el-card shadow="hover" class="chart-card">
          <div slot="header" class="clearfix">
            <span>订阅收入趋势（近6个月）</span>
          </div>
          <div ref="revenueChart" class="chart"></div>
        </el-card>
      </el-col>
      <el-col :xs="24" :lg="12">
        <el-card shadow="hover" class="chart-card">
          <div slot="header" class="clearfix">
            <span>客户增长趋势（近6个月）</span>
          </div>
          <div ref="customerGrowthChart" class="chart"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 系统性能和客户分析 -->
    <el-row :gutter="30" class="chart-row">
      <el-col :xs="24" :md="12">
        <el-card shadow="hover" class="chart-card">
          <div slot="header" class="clearfix">
            <span>客户订阅套餐分布</span>
          </div>
          <div ref="packageDistributionChart" class="chart chart-small"></div>
        </el-card>
      </el-col>
      <el-col :xs="24" :md="12">
        <el-card shadow="hover" class="chart-card">
          <div slot="header" class="clearfix">
            <span>系统性能指标</span>
          </div>
          <div ref="systemPerformanceChart" class="chart chart-small"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 客户即将到期和最近工单 -->
    <el-row :gutter="30">
      <el-col :xs="24" :md="12">
        <el-card shadow="hover" class="list-card">
          <div slot="header" class="clearfix">
            <span>即将到期的客户（30天内）</span>
            <el-button style="float: right; padding: 3px 0" type="text">查看全部</el-button>
          </div>
          <el-table :data="expiringCustomers" stripe style="width: 100%">
            <el-table-column prop="company" label="公司名称"></el-table-column>
            <el-table-column prop="package" label="订阅套餐"></el-table-column>
            <el-table-column prop="expiryDate" label="到期日期"></el-table-column>
            <el-table-column prop="status" label="状态" width="120">
              <template slot-scope="scope">
                <el-tag :type="scope.row.status === '已联系' ? 'success' : scope.row.status === '待联系' ? 'warning' : 'danger'">
                  {{ scope.row.status }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      <el-col :xs="24" :md="12">
        <el-card shadow="hover" class="list-card">
          <div slot="header" class="clearfix">
            <span>最近工单</span>
            <el-button style="float: right; padding: 3px 0" type="text">查看全部</el-button>
          </div>
          <el-table :data="recentTickets" stripe style="width: 100%">
            <el-table-column prop="id" label="工单ID" width="100"></el-table-column>
            <el-table-column prop="company" label="客户公司"></el-table-column>
            <el-table-column prop="title" label="标题" show-overflow-tooltip></el-table-column>
            <el-table-column prop="priority" label="优先级" width="100">
              <template slot-scope="scope">
                <el-tag :type="scope.row.priority === '高' ? 'danger' : scope.row.priority === '中' ? 'warning' : 'info'">
                  {{ scope.row.priority }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <!-- 最近活动 -->
    <el-row :gutter="30" class="table-row">
      <el-col :span="24">
        <el-card shadow="hover">
          <div slot="header" class="clearfix">
            <span>最近开通的客户</span>
            <el-button style="float: right; padding: 3px 0" type="text">查看全部</el-button>
          </div>
          <el-table :data="recentCustomers" stripe style="width: 100%">
            <el-table-column prop="company" label="公司名称"></el-table-column>
            <el-table-column prop="contactPerson" label="联系人"></el-table-column>
            <el-table-column prop="package" label="订阅套餐"></el-table-column>
            <el-table-column prop="amount" label="订阅金额"></el-table-column>
            <el-table-column prop="startDate" label="开通时间"></el-table-column>
            <el-table-column prop="duration" label="订阅时长"></el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: "Index",
  data() {
    return {
      // 客户数据
      recentCustomers: [
        { company: '北京家政服务有限公司', contactPerson: '张经理', package: '企业版', amount: '¥12,800', startDate: '2023-05-11', duration: '一年' },
        { company: '上海洁亮家政服务中心', contactPerson: '李总监', package: '专业版', amount: '¥6,800', startDate: '2023-05-09', duration: '一年' },
        { company: '杭州家家乐管理有限公司', contactPerson: '王总', package: '旗舰版', amount: '¥19,800', startDate: '2023-05-08', duration: '两年' },
        { company: '深圳清洁到家服务公司', contactPerson: '赵经理', package: '企业版', amount: '¥12,800', startDate: '2023-05-06', duration: '一年' },
        { company: '成都温馨家政服务公司', contactPerson: '刘经理', package: '专业版', amount: '¥6,800', startDate: '2023-05-05', duration: '一年' }
      ],
      // 即将到期客户
      expiringCustomers: [
        { company: '广州星级家政服务公司', package: '企业版', expiryDate: '2023-05-25', status: '待联系' },
        { company: '武汉家政连锁有限公司', package: '旗舰版', expiryDate: '2023-05-29', status: '待联系' },
        { company: '天津洁居家政集团', package: '专业版', expiryDate: '2023-06-02', status: '已联系' },
        { company: '重庆爱家家政服务中心', package: '企业版', expiryDate: '2023-06-05', status: '已联系' }
      ],
      // 最近工单
      recentTickets: [
        { id: 'TK-2305', company: '北京家政服务有限公司', title: '客户管理模块功能增强需求', priority: '中' },
        { id: 'TK-2304', company: '深圳清洁到家服务公司', title: '系统响应缓慢问题', priority: '高' },
        { id: 'TK-2303', company: '上海洁亮家政服务中心', title: '移动端应用崩溃问题', priority: '高' },
        { id: 'TK-2302', company: '成都温馨家政服务公司', title: '订单数据导出功能建议', priority: '低' }
      ],
      // 图表实例
      charts: {
        revenueChart: null,
        customerGrowthChart: null,
        packageDistributionChart: null,
        systemPerformanceChart: null
      }
    };
  },
  mounted() {
    this.initCharts();
  },
  beforeDestroy() {
    // 销毁图表实例
    Object.values(this.charts).forEach(chart => {
      if (chart) {
        chart.dispose();
      }
    });
    // 移除窗口大小变化监听
    window.removeEventListener('resize', this.resizeCharts);
  },
  methods: {
    initCharts() {
      this.$nextTick(() => {
        // 收入趋势图
        this.charts.revenueChart = echarts.init(this.$refs.revenueChart);
        this.charts.revenueChart.setOption({
          tooltip: {
            trigger: 'axis'
          },
          legend: {
            data: ['总收入', '经常性收入', '一次性收入']
          },
          xAxis: {
            type: 'category',
            data: ['12月', '1月', '2月', '3月', '4月', '5月']
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: '{value}万元'
            }
          },
          series: [
            {
              name: '总收入',
              type: 'line',
              smooth: true,
              data: [92, 105, 114, 120, 132, 145],
              lineStyle: {
                color: '#5B8FF9'
              }
            },
            {
              name: '经常性收入',
              type: 'line',
              smooth: true,
              data: [80, 90, 98, 105, 116, 126],
              lineStyle: {
                color: '#5AD8A6'
              }
            },
            {
              name: '一次性收入',
              type: 'line',
              smooth: true,
              data: [12, 15, 16, 15, 16, 19],
              lineStyle: {
                color: '#F6BD16'
              }
            }
          ]
        });

        // 客户增长趋势图
        this.charts.customerGrowthChart = echarts.init(this.$refs.customerGrowthChart);
        this.charts.customerGrowthChart.setOption({
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          legend: {
            data: ['新增客户', '总客户数']
          },
          xAxis: {
            type: 'category',
            data: ['12月', '1月', '2月', '3月', '4月', '5月']
          },
          yAxis: [
            {
              type: 'value',
              name: '新增客户',
              position: 'left'
            },
            {
              type: 'value',
              name: '总客户数',
              position: 'right'
            }
          ],
          series: [
            {
              name: '新增客户',
              type: 'bar',
              data: [15, 18, 22, 20, 25, 28],
              itemStyle: {
                color: '#5B8FF9'
              }
            },
            {
              name: '总客户数',
              type: 'line',
              yAxisIndex: 1,
              data: [145, 163, 185, 205, 230, 258],
              lineStyle: {
                color: '#F6BD16'
              },
              symbol: 'circle',
              symbolSize: 8
            }
          ]
        });

        // 客户订阅套餐分布
        this.charts.packageDistributionChart = echarts.init(this.$refs.packageDistributionChart);
        this.charts.packageDistributionChart.setOption({
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
          },
          legend: {
            orient: 'vertical',
            right: 10,
            top: 'center',
            data: ['基础版', '专业版', '企业版', '旗舰版', '定制版']
          },
          series: [
            {
              name: '订阅套餐',
              type: 'pie',
              radius: ['45%', '70%'],
              avoidLabelOverlap: false,
              label: {
                show: false,
                position: 'center'
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: '12',
                  fontWeight: 'bold'
                }
              },
              labelLine: {
                show: false
              },
              data: [
                {value: 58, name: '基础版'},
                {value: 85, name: '专业版'},
                {value: 65, name: '企业版'},
                {value: 23, name: '旗舰版'},
                {value: 4, name: '定制版'}
              ]
            }
          ]
        });

        // 系统性能指标
        this.charts.systemPerformanceChart = echarts.init(this.$refs.systemPerformanceChart);
        this.charts.systemPerformanceChart.setOption({
          tooltip: {
            trigger: 'axis'
          },
          radar: {
            indicator: [
              { name: '系统可用性', max: 100 },
              { name: '响应时间', max: 100 },
              { name: '数据处理量', max: 100 },
              { name: 'API响应', max: 100 },
              { name: '存储使用率', max: 100 },
              { name: '系统稳定性', max: 100 }
            ],
            shape: 'circle'
          },
          series: [
            {
              name: '系统性能',
              type: 'radar',
              data: [
                {
                  value: [95, 82, 78, 90, 65, 93],
                  name: '本月',
                  areaStyle: {
                    color: 'rgba(91, 143, 249, 0.6)'
                  },
                  lineStyle: {
                    color: '#5B8FF9'
                  }
                },
                {
                  value: [90, 75, 65, 85, 60, 88],
                  name: '上月',
                  areaStyle: {
                    color: 'rgba(90, 216, 166, 0.6)'
                  },
                  lineStyle: {
                    color: '#5AD8A6'
                  }
                }
              ]
            }
          ]
        });

        // 监听窗口变化，调整图表大小
        window.addEventListener('resize', this.resizeCharts);
      });
    },
    // 调整图表大小
    resizeCharts() {
      Object.values(this.charts).forEach(chart => {
        if (chart) {
          chart.resize();
        }
      });
    }
  }
};
</script>

<style scoped lang="scss">
.home {
  padding-bottom: 20px;
  
  .card-row {
    margin-bottom: 20px;
  }
  
  .data-card {
    height: 120px;
    margin-bottom: 20px;
    
    .card-content-wrapper {
      display: flex;
      align-items: center;
      height: 100%;
      padding: 10px 15px;
    }
    
    .card-icon {
      min-width: 70px;
      height: 70px;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;
      
      i {
        font-size: 32px;
        color: #fff;
      }
      
      &.blue {
        background: linear-gradient(to right, #1890ff, #36cbcb);
      }
      
      &.green {
        background: linear-gradient(to right, #52c41a, #b7eb8f);
      }
      
      &.orange {
        background: linear-gradient(to right, #fa8c16, #ffd666);
      }
      
      &.purple {
        background: linear-gradient(to right, #722ed1, #b37feb);
      }
    }
    
    .card-content {
      flex: 1;
      
      .card-title {
        font-size: 14px;
        color: #909399;
        margin-bottom: 8px;
      }
      
      .card-value {
        font-size: 24px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 8px;
      }
      
      .card-footer {
        font-size: 12px;
        color: #909399;
        
        .up {
          color: #52c41a;
          margin-right: 5px;
        }
        
        .down {
          color: #f56c6c;
          margin-right: 5px;
        }
      }
    }
  }
  
  .chart-row {
    margin-bottom: 20px;
  }
  
  .chart-card, .list-card {
    margin-bottom: 20px;
  }
  
  .chart {
    height: 300px;
    width: 100%;
  }
  
  .chart-small {
    height: 250px;
  }
  
  .table-row {
    margin-bottom: 20px;
  }
}
</style>
  