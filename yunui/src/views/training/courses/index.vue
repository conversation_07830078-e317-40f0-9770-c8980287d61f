<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="课程标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入课程标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="分类" prop="categoryId">
        <el-select v-model="queryParams.categoryId" placeholder="请选择分类" clearable>
          <el-option
            v-for="category in categoryOptions"
            :key="category.id"
            :label="category.name"
            :value="category.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="内容类型" prop="contentType">
        <el-select v-model="queryParams.contentType" placeholder="请选择内容类型" clearable>
          <el-option label="视频" :value="1" />
          <el-option label="文章" :value="2" />
          <el-option label="图片" :value="3" />
          <el-option label="音频" :value="4" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="发布" :value="1" />
          <el-option label="下架" :value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['training:courses:add']"
        >新增课程</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-folder"
          size="mini"
          @click="handleCategoryManage"
          v-hasPermi="['training:categories:list']"
        >分类管理</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['training:courses:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['training:courses:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="coursesList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="课程标题" align="center" prop="title" :show-overflow-tooltip="true" />
      <el-table-column label="分类" align="center" prop="categoryName" />
      <el-table-column label="内容类型" align="center" prop="contentTypeName" />
      <el-table-column label="封面图片" align="center" prop="coverImage" width="100">
        <template slot-scope="scope">
          <el-image
            v-if="scope.row.coverImage"
            style="width: 60px; height: 40px"
            :src="scope.row.coverImage"
            :preview-src-list="[scope.row.coverImage]"
            fit="cover"
          />
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="浏览次数" align="center" prop="views" />
      <el-table-column label="是否推荐" align="center" prop="isFeatured">
        <template slot-scope="scope">
          <el-tag :type="scope.row.isFeatured === 1 ? 'success' : 'info'">
            {{ scope.row.isFeatured === 1 ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
            {{ scope.row.status === 1 ? '发布' : '下架' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建者" align="center" prop="creatorName" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime || scope.row.createdAt, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['training:courses:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['training:courses:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改课程对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="课程标题" prop="title">
              <el-input v-model="form.title" placeholder="请输入课程标题" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="分类" prop="categoryId">
              <el-cascader
                v-model="form.categoryId"
                :options="categoryTreeOptions"
                :props="{ checkStrictly: true, value: 'id', label: 'name', children: 'children', emitPath: false }"
                placeholder="请选择分类"
                clearable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="内容类型" prop="contentType">
              <el-select v-model="form.contentType" placeholder="请选择内容类型">
                <el-option label="视频" :value="1" />
                <el-option label="文章" :value="2" />
                <el-option label="图片" :value="3" />
                <el-option label="音频" :value="4" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序权重" prop="sortOrder">
              <el-input-number v-model="form.sortOrder" :min="0" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="是否推荐" prop="isFeatured">
              <el-radio-group v-model="form.isFeatured">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio :label="1">发布</el-radio>
                <el-radio :label="0">下架</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="封面图片" prop="coverImage">
          <image-upload v-model="form.coverImage" :limit="1" />
        </el-form-item>
        <el-form-item label="课程描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入课程描述" />
        </el-form-item>
        <el-form-item label="内容数据" prop="contentData">
          <!-- 视频类型上传 -->
          <div v-if="form.contentType === 1">
            <file-upload
              v-model="videoUploadValue"
              :limit="1"
              :file-type="['mp4', 'avi', 'rmvb', 'mov', 'wmv', 'flv', 'mkv']"
              :file-size="100"
              @input="handleVideoUpload"
            />
            <div style="margin-top: 10px; color: #909399; font-size: 12px;">
              支持格式：mp4、avi、rmvb、mov、wmv、flv、mkv，大小不超过100MB
            </div>

            <!-- 视频预览区域 -->
            <div v-if="videoUploadValue" class="video-preview-container" style="margin-top: 15px;">
              <div class="video-preview-header">
                <span style="font-weight: bold; color: #409EFF;">
                  <i class="el-icon-video-camera"></i>
                  视频预览
                </span>
              </div>
              <div class="video-preview-content">
                <video
                  ref="videoPreview"
                  :src="videoUploadValue"
                  controls
                  preload="metadata"
                  class="video-player"
                  @loadstart="handleVideoLoadStart"
                  @loadeddata="handleVideoLoaded"
                  @error="handleVideoError"
                >
                  您的浏览器不支持视频播放。
                </video>
                <div v-if="videoLoading" class="video-loading">
                  <i class="el-icon-loading"></i>
                  <span style="margin-left: 8px;">正在加载视频...</span>
                </div>
                <div v-if="videoError" class="video-error">
                  <i class="el-icon-warning"></i>
                  <span style="margin-left: 8px;">视频加载失败，请检查文件格式或网络连接</span>
                </div>
              </div>
            </div>
          </div>
          <!-- 图片类型上传 -->
          <div v-else-if="form.contentType === 3">
            <image-upload
              v-model="imageUploadValue"
              :limit="5"
              @input="handleImageUpload"
            />
            <div style="margin-top: 10px; color: #909399; font-size: 12px;">
              支持格式：jpg、jpeg、png、gif，单张大小不超过5MB
            </div>
          </div>
          <!-- 文章和音频类型保持原有输入框 -->
          <div v-else>
            <el-input v-model="contentDataStr" type="textarea" placeholder="请输入内容数据JSON格式" />
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 分类管理对话框 -->
    <el-dialog title="分类管理" :visible.sync="categoryDialogVisible" width="1000px" append-to-body>
      <div style="margin-bottom: 20px;">
        <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAddCategory">新增根分类</el-button>
        <el-button type="success" icon="el-icon-refresh" size="small" @click="getCategoryTreeList">刷新</el-button>
      </div>

      <el-table
        v-loading="categoryLoading"
        :data="categoryTreeList"
        style="width: 100%"
        row-key="id"
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
        :expand-row-keys="expandedKeys"
        @expand-change="handleExpandChange"
      >
        <el-table-column label="分类名称" prop="name" min-width="200">
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="描述" prop="description" show-overflow-tooltip min-width="150" />
        <el-table-column label="排序" prop="sortOrder" width="80" />
        <el-table-column label="状态" width="80">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" prop="createTime" width="160">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="handleAddSubCategory(scope.row)">添加子分类</el-button>
            <el-button size="mini" type="text" @click="handleEditCategory(scope.row)">编辑</el-button>
            <el-button size="mini" type="text" style="color: #f56c6c" @click="handleDeleteCategory(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div slot="footer" class="dialog-footer">
        <el-button @click="categoryDialogVisible = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 新增/编辑分类对话框 -->
    <el-dialog :title="categoryFormTitle" :visible.sync="categoryFormVisible" width="600px" append-to-body>
      <el-form ref="categoryForm" :model="categoryForm" :rules="categoryRules" label-width="100px">
        <el-form-item label="父分类" prop="parentId">
          <el-cascader
            v-model="categoryForm.parentId"
            :options="categoryTreeOptions"
            :props="{ checkStrictly: true, value: 'id', label: 'name', children: 'children', emitPath: false }"
            placeholder="请选择父分类（不选择则为根分类）"
            clearable
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="categoryForm.name" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="分类图标" prop="icon">
          <el-input v-model="categoryForm.icon" placeholder="请输入分类图标（可选）" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="categoryForm.description" type="textarea" placeholder="请输入分类描述" />
        </el-form-item>
        <el-form-item label="排序权重" prop="sortOrder">
          <el-input-number v-model="categoryForm.sortOrder" :min="0" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="categoryForm.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitCategoryForm">确 定</el-button>
        <el-button @click="cancelCategoryForm">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listTrainingCourses, getTrainingCourses, delTrainingCourses, addTrainingCourses, updateTrainingCourses } from "@/api/training/courses";
import {
  getTrainingCategoriesTree,
  listTrainingCategories,
  addTrainingCategories,
  updateTrainingCategories,
  delTrainingCategories,
  getTrainingCategories
} from "@/api/training/categories";
import ImageUpload from "@/components/ImageUpload";
import FileUpload from "@/components/FileUpload";

export default {
  name: "TrainingCourses",
  components: {
    ImageUpload,
    FileUpload
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 课程表格数据
      coursesList: [],
      // 分类选项
      categoryOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: null,
        categoryId: null,
        contentType: null,
        status: null
      },
      // 表单参数
      form: {},
      // 内容数据字符串
      contentDataStr: "",
      // 视频上传值
      videoUploadValue: "",
      // 图片上传值
      imageUploadValue: "",
      // 视频预览状态
      videoLoading: false,
      videoError: false,
      // 表单校验
      rules: {
        title: [
          { required: true, message: "课程标题不能为空", trigger: "blur" }
        ],
        contentType: [
          { required: true, message: "内容类型不能为空", trigger: "change" }
        ]
      },
      // 分类管理相关
      categoryDialogVisible: false,
      categoryFormVisible: false,
      categoryFormTitle: "",
      categoryLoading: false,
      categoryList: [],
      categoryTreeList: [],
      categoryTreeOptions: [],
      expandedKeys: [],
      categoryForm: {},
      categoryRules: {
        name: [
          { required: true, message: "分类名称不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getCategoryOptions();
  },
  methods: {
    /** 查询课程列表 */
    getList() {
      this.loading = true;
      listTrainingCourses(this.queryParams).then(response => {
        // 处理课程数据，添加内容类型名称
        const processedCourses = (response.rows || []).map(course => {
          // 处理内容类型
          const contentTypeMap = {
            1: '视频',
            2: '文章',
            3: '图片',
            4: '音频'
          };
          course.contentTypeName = contentTypeMap[course.contentType] || '未知';

          return course;
        });

        this.coursesList = processedCourses;
        this.total = response.total || 0;
        this.loading = false;
      }).catch(error => {
        console.error('获取课程列表失败:', error);
        this.loading = false;
      });
    },
    /** 获取分类选项 */
    getCategoryOptions() {
      getTrainingCategoriesTree().then(response => {
        console.log('分类API响应数据:', response);
        this.categoryTreeOptions = response.data || [];
        this.categoryOptions = this.flattenTree(response.data || []);
      }).catch(error => {
        console.error('获取分类列表失败:', error);
        this.categoryTreeOptions = [];
        this.categoryOptions = [];
      });
    },
    /** 扁平化树形结构 */
    flattenTree(tree, result = []) {
      tree.forEach(node => {
        result.push({ id: node.id, name: node.name });
        if (node.children && node.children.length > 0) {
          this.flattenTree(node.children, result);
        }
      });
      return result;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        title: null,
        description: null,
        coverImage: null,
        categoryId: null,
        contentType: null,
        contentData: null,
        sortOrder: 0,
        isFeatured: 0,
        status: 1
      };
      this.contentDataStr = "";
      this.videoUploadValue = "";
      this.imageUploadValue = "";
      // 重置视频预览状态
      this.videoLoading = false;
      this.videoError = false;
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加课程";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const courseId = row.id || this.ids
      getTrainingCourses(courseId).then(response => {
        this.form = response.data;
        this.contentDataStr = JSON.stringify(this.form.contentData || {}, null, 2);

        // 处理内容数据的显示
        if (this.form.contentData) {
          if (this.form.contentType === 1 && this.form.contentData.url) {
            // 视频类型
            this.videoUploadValue = this.form.contentData.url;
          } else if (this.form.contentType === 3 && this.form.contentData.url) {
            // 图片类型
            this.imageUploadValue = this.form.contentData.url;
          }
        }

        this.open = true;
        this.title = "修改课程";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 处理内容数据
          if (this.form.contentType === 1 || this.form.contentType === 3) {
            // 视频或图片类型，使用上传组件的数据
            const uploadValue = this.form.contentType === 1 ? this.videoUploadValue : this.imageUploadValue;
            if (uploadValue) {
              this.form.contentData = {
                url: uploadValue,
                type: this.form.contentType === 1 ? 'video' : 'image'
              };
            }
          } else {
            // 文章或音频类型，使用JSON文本
            try {
              this.form.contentData = this.contentDataStr ? JSON.parse(this.contentDataStr) : null;
            } catch (e) {
              this.$modal.msgError("内容数据格式错误，请输入有效的JSON格式");
              return;
            }
          }

          if (this.form.id != null) {
            updateTrainingCourses(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addTrainingCourses(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },

    /** 处理视频上传 */
    handleVideoUpload(value) {
      this.videoUploadValue = value;
      // 重置视频预览状态
      this.videoLoading = false;
      this.videoError = false;
    },

    /** 处理图片上传 */
    handleImageUpload(value) {
      this.imageUploadValue = value;
    },

    /** 视频开始加载 */
    handleVideoLoadStart() {
      this.videoLoading = true;
      this.videoError = false;
    },

    /** 视频加载完成 */
    handleVideoLoaded() {
      this.videoLoading = false;
      this.videoError = false;
    },

    /** 视频加载错误 */
    handleVideoError() {
      this.videoLoading = false;
      this.videoError = true;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const courseIds = row.id || this.ids;
      this.$modal.confirm('是否确认删除课程编号为"' + courseIds + '"的数据项？').then(function() {
        return delTrainingCourses(courseIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },

    /** 分类管理按钮操作 */
    handleCategoryManage() {
      this.categoryDialogVisible = true;
      this.getCategoryTreeList();
    },

    /** 获取分类树形列表 */
    getCategoryTreeList() {
      this.categoryLoading = true;
      getTrainingCategoriesTree().then(response => {
        this.categoryTreeList = response.data || [];
        this.categoryLoading = false;
      }).catch(error => {
        console.error('获取分类树形列表失败:', error);
        this.categoryLoading = false;
      });
    },

    /** 获取分类列表 */
    getCategoryList() {
      this.categoryLoading = true;
      listTrainingCategories({}).then(response => {
        this.categoryList = response.rows || [];
        this.categoryLoading = false;
      }).catch(error => {
        console.error('获取分类列表失败:', error);
        this.categoryLoading = false;
      });
    },

    /** 树形表格展开/收起 */
    handleExpandChange(row, expandedRows) {
      // 可以在这里处理展开/收起事件
    },

    /** 新增根分类按钮操作 */
    handleAddCategory() {
      this.resetCategoryForm();
      this.categoryFormTitle = "新增根分类";
      this.categoryForm.parentId = null;
      this.categoryFormVisible = true;
    },

    /** 新增子分类按钮操作 */
    handleAddSubCategory(row) {
      this.resetCategoryForm();
      this.categoryFormTitle = `新增子分类（父分类：${row.name}）`;
      this.categoryForm.parentId = row.id;
      this.categoryFormVisible = true;
    },

    /** 编辑分类按钮操作 */
    handleEditCategory(row) {
      this.resetCategoryForm();
      this.categoryFormTitle = "编辑分类";
      this.categoryForm = { ...row };
      this.categoryFormVisible = true;
    },

    /** 删除分类按钮操作 */
    handleDeleteCategory(row) {
      // 检查是否有子分类
      const hasSubCategories = row.children && row.children.length > 0;
      if (hasSubCategories) {
        this.$modal.msgError("该分类下存在子分类，无法删除！请先删除子分类。");
        return;
      }

      // 检查分类下是否有课程
      const hasCoursesInCategory = this.coursesList.some(course => course.categoryId === row.id);
      if (hasCoursesInCategory) {
        this.$modal.msgError("该分类下存在课程，无法删除！请先删除或移动分类下的课程。");
        return;
      }

      this.$modal.confirm('是否确认删除分类"' + row.name + '"？').then(() => {
        return delTrainingCategories(row.id);
      }).then(() => {
        this.getCategoryTreeList();
        this.getCategoryOptions(); // 刷新分类选项
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },

    /** 重置分类表单 */
    resetCategoryForm() {
      this.categoryForm = {
        id: null,
        name: null,
        description: null,
        icon: null,
        parentId: null,
        sortOrder: 0,
        status: 1
      };
      if (this.$refs.categoryForm) {
        this.$refs.categoryForm.resetFields();
      }
    },

    /** 分类表单提交 */
    submitCategoryForm() {
      this.$refs["categoryForm"].validate(valid => {
        if (valid) {
          if (this.categoryForm.id != null) {
            // 编辑分类
            updateTrainingCategories(this.categoryForm).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.categoryFormVisible = false;
              this.getCategoryTreeList();
              this.getCategoryOptions(); // 刷新分类选项
            });
          } else {
            // 新增分类
            addTrainingCategories(this.categoryForm).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.categoryFormVisible = false;
              this.getCategoryTreeList();
              this.getCategoryOptions(); // 刷新分类选项
            });
          }
        }
      });
    },

    /** 取消分类表单 */
    cancelCategoryForm() {
      this.categoryFormVisible = false;
      this.resetCategoryForm();
    }
  }
};
</script>

<style scoped>
/* 视频预览样式 */
.video-preview-container {
  border: 1px solid #DCDFE6;
  border-radius: 6px;
  padding: 15px;
  background-color: #FAFAFA;
}

.video-preview-header {
  margin-bottom: 10px;
  padding-bottom: 8px;
  border-bottom: 1px solid #E4E7ED;
}

.video-preview-content {
  position: relative;
  text-align: center;
}

.video-player {
  width: 100%;
  max-width: 600px;
  height: auto;
  max-height: 400px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.video-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(255, 255, 255, 0.9);
  padding: 10px 15px;
  border-radius: 4px;
  color: #409EFF;
  font-size: 14px;
}

.video-error {
  color: #F56C6C;
  font-size: 14px;
  margin-top: 10px;
}

.video-loading i {
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
