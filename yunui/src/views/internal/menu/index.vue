<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="菜单名称" prop="menuName">
        <el-input
          v-model="queryParams.menuName"
          placeholder="请输入菜单名称"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="菜单状态"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="客户端类型" prop="clientType">
        <el-select
          v-model="queryParams.clientType"
          placeholder="客户端类型"
          clearable
          style="width: 200px"
        >
          <el-option label="系统网页端" :value="0" />
          <el-option label="门店端" :value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:internal:menu:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-sort"
          size="mini"
          @click="toggleExpandAll"
        >展开/折叠</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table
      v-if="refreshTable"
      v-loading="loading"
      :data="menuList"
      ref="menuTable"
      row-key="menuId"
      :default-expand-all="isExpandAll"
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
      :row-class-name="tableRowClassName"
      highlight-current-row
      border
      :row-style="{height: '45px'}"
    >
      <el-table-column label="菜单名称" prop="menuName" :show-overflow-tooltip="true" width="160">
        <template slot-scope="scope">
          <span v-if="scope.row.isNew === 1" class="new-tag">New</span>
          <span>{{ scope.row.menuName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="客户端类型" prop="clientType" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.clientType === 0" type="success">系统网页端</el-tag>
          <el-tag v-else-if="scope.row.clientType === 1" type="warning">门店端</el-tag>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="图标" align="center" prop="icon" width="100">
        <template slot-scope="scope">
          <svg-icon :icon-class="scope.row.icon" />
        </template>
      </el-table-column>
      <el-table-column prop="orderNum" label="排序" width="60"></el-table-column>
      <el-table-column prop="perms" label="权限标识" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="component" label="组件路径" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column label="类型" prop="menuType" width="80">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.menuType === 'M'" type="primary">目录</el-tag>
          <el-tag v-else-if="scope.row.menuType === 'C'" type="success">菜单</el-tag>
          <el-tag v-else-if="scope.row.menuType === 'F'" type="warning">按钮</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="可见" prop="visible" width="80">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.visible === '0'" type="success">显示</el-tag>
          <el-tag v-else type="info">隐藏</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="缓存" prop="isCache" width="80">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.isCache === 0" type="success">缓存</el-tag>
          <el-tag v-else type="info">不缓存</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="80">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            active-value="0"
            inactive-value="1"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:internal:menu:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-plus"
            @click="handleAdd(scope.row)"
            v-hasPermi="['system:internal:menu:add']"
          >新增</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:internal:menu:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改菜单对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="680px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="上级菜单" prop="parentId">
              <treeselect
                v-model="form.parentId"
                :options="menuOptions"
                :normalizer="normalizer"
                :show-count="true"
                placeholder="选择上级菜单"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="菜单类型" prop="menuType">
              <el-radio-group v-model="form.menuType">
                <el-radio label="M">目录</el-radio>
                <el-radio label="C">菜单</el-radio>
                <el-radio label="F">按钮</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="客户端类型" prop="clientType">
              <el-radio-group v-model="form.clientType">
                <el-radio :label="0">系统网页端</el-radio>
                <el-radio :label="1">门店端</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" v-if="form.menuType != 'F'">
            <el-form-item label="菜单图标" prop="icon">
              <el-popover
                placement="bottom-start"
                width="460"
                trigger="click"
                @show="$refs['iconSelect'].reset()"
              >
                <IconSelect ref="iconSelect" @selected="selected" :active-icon="form.icon" />
                <el-input slot="reference" v-model="form.icon" placeholder="点击选择图标" readonly>
                  <svg-icon
                    v-if="form.icon"
                    slot="prefix"
                    :icon-class="form.icon"
                    style="width: 25px;"
                  />
                  <i v-else slot="prefix" class="el-icon-search el-input__icon" />
                </el-input>
              </el-popover>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="菜单名称" prop="menuName">
              <el-input v-model="form.menuName" placeholder="请输入菜单名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="显示排序" prop="orderNum">
              <el-input-number v-model="form.orderNum" controls-position="right" :min="0" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12" v-if="form.menuType != 'F'">
            <el-form-item label="是否新上" prop="isNew">
              <el-radio-group v-model="form.isNew">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.menuType != 'F'">
            <el-form-item prop="path">
              <span slot="label">
                <el-tooltip content="访问的路由地址，如：`user`，如外网地址需内链访问则以`http(s)://`开头" placement="top">
                <i class="el-icon-question"></i>
                </el-tooltip>
                路由地址
              </span>
              <el-input v-model="form.path" placeholder="请输入路由地址" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12" v-if="form.menuType == 'C'">
            <el-form-item prop="component">
              <span slot="label">
                <el-tooltip content="访问的组件路径，如：`system/user/index`，默认在`views`目录下" placement="top">
                <i class="el-icon-question"></i>
                </el-tooltip>
                组件路径
              </span>
              <el-input v-model="form.component" placeholder="请输入组件路径" />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.menuType !== 'M'">
            <el-form-item prop="perms">
              <el-input v-model="form.perms" placeholder="请输入权限标识" maxlength="100" />
              <span slot="label">
                <el-tooltip content="控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasPermi('system:user:list')`)" placement="top">
                <i class="el-icon-question"></i>
                </el-tooltip>
                权限字符
              </span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12" v-if="form.menuType == 'C'">
            <el-form-item prop="isCache">
              <span slot="label">
                <el-tooltip content="选择是则会被`keep-alive`缓存，需要匹配组件的`name`和地址保持一致" placement="top">
                <i class="el-icon-question"></i>
                </el-tooltip>
                是否缓存
              </span>
              <el-radio-group v-model="form.isCache">
                <el-radio :label="0">缓存</el-radio>
                <el-radio :label="1">不缓存</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12" v-if="form.menuType != 'F'">
            <el-form-item prop="visible">
              <span slot="label">
                <el-tooltip content="选择隐藏则路由将不会出现在侧边栏，但仍然可以访问" placement="top">
                <i class="el-icon-question"></i>
                </el-tooltip>
                显示状态
              </span>
              <el-radio-group v-model="form.visible">
                <el-radio
                  v-for="dict in dict.type.sys_show_hide"
                  :key="dict.value"
                  :label="dict.value"
                >{{dict.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="status">
              <span slot="label">
                <el-tooltip content="选择停用则路由将不会出现在侧边栏，也不能被访问" placement="top">
                <i class="el-icon-question"></i>
                </el-tooltip>
                菜单状态
              </span>
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in dict.type.sys_normal_disable"
                  :key="dict.value"
                  :label="dict.value"
                >{{dict.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listInternalMenu, getInternalMenu, delInternalMenu, addInternalMenu, updateInternalMenu } from "@/api/system/internalMenu";
import { internalMenuTreeselect } from "@/api/system/internalMenu";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import IconSelect from "@/components/IconSelect";
import { handleTree } from "@/utils/ruoyi";

export default {
  name: "InternalMenu",
  dicts: ['sys_show_hide', 'sys_normal_disable'],
  components: { Treeselect, IconSelect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 菜单表格数据
      menuList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否展开，默认全部折叠
      isExpandAll: false,
      // 重新渲染表格状态
      refreshTable: true,
      // 菜单树选项
      menuOptions: [],
      // 查询参数
      queryParams: {
        menuName: undefined,
        visible: undefined,
        status: undefined,
        clientType: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        menuType: [
          { required: true, message: "菜单类型不能为空", trigger: "change" }
        ],
        menuName: [
          { required: true, message: "菜单名称不能为空", trigger: "blur" }
        ],
        orderNum: [
          { required: true, message: "菜单顺序不能为空", trigger: "blur" }
        ],
        path: [
          { required: true, message: "路由地址不能为空", trigger: "blur" }
        ],
        clientType: [
          { required: true, message: "客户端类型不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 表格行的类名
    tableRowClassName({row}) {
      // 添加行的调试类名，以便检查结构
      const hasChildrenClass = row.hasChildren ? 'has-children' : 'no-children';
      const rowLevel = row.parentId === 0 || !row.parentId ? 'level-1' : 'level-2';
      return `${hasChildrenClass} ${rowLevel}`;
    },
    // 选择图标
    selected(name) {
      this.form.icon = name;
    },
    /** 查询菜单列表 */
    getList() {
      this.loading = true;
      listInternalMenu(this.queryParams).then(response => {
        console.log("原始菜单数据:", response.data);
        
        // 检查接口返回的数据是否已经是树形结构
        const hasTreeStructure = response.data && response.data.length > 0 && 
                                 response.data.some(item => item.children && item.children.length > 0);
        
        console.log("接口返回的数据已经是树形结构:", hasTreeStructure);
        
        if (hasTreeStructure) {
          // 如果数据已经是树形结构，直接使用
          this.menuList = response.data;
          console.log("直接使用接口返回的树形结构");
        } else {
          // 只有在数据不是树形结构时才使用handleTree
          this.menuList = handleTree(response.data || [], "menuId", "parentId");
          console.log("使用handleTree处理扁平数据");
        }
        
        this.loading = false;
        
        // 手动展开所有节点
        this.$nextTick(() => {
          this.expandAll(this.menuList);
        });
      });
    },
    /** 递归展开所有树节点 */
    expandAll(rows) {
      if (!rows || !this.$refs.menuTable) return;
      rows.forEach(row => {
        this.$refs.menuTable.toggleRowExpansion(row, true);
        if (row.children && row.children.length > 0) {
          this.expandAll(row.children);
        }
      });
    },
    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id || node.menuId,
        label: node.label || node.menuName,
        children: node.children
      };
    },
    /** 查询菜单下拉树结构 */
    getTreeselect() {
      internalMenuTreeselect({clientType: this.form.clientType}).then(response => {
        console.log("原始树选择数据:", response.data);
        this.menuOptions = [];
        const menu = { id: 0, label: '主类目', children: [] };
        
        // 直接使用接口返回的树形结构
        if (response.data && response.data.length > 0) {
          // 转换后端返回的字段为treeselect需要的格式
          const convertNodeFormat = (nodes) => {
            return nodes.map(node => {
              const result = {
                id: node.menuId,
                label: node.menuName
              };
              
              if (node.children && node.children.length > 0) {
                result.children = convertNodeFormat(node.children);
              }
              
              return result;
            });
          };
          
          menu.children = convertNodeFormat(response.data);
        } else {
          // 如果接口没有返回树形结构，回退到之前的处理方式
          menu.children = handleTree(response.data || [], "menuId", "parentId");
        }
        
        console.log("处理后的树选择数据:", menu);
        this.menuOptions.push(menu);
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        menuId: undefined,
        parentId: 0,
        menuName: undefined,
        icon: undefined,
        menuType: "M",
        orderNum: 1,
        isCache: 0,
        visible: "0",
        status: "0",
        clientType: 0,
        isNew: 0
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 展开/折叠操作 */
    toggleExpandAll() {
      this.refreshTable = false;
      this.isExpandAll = !this.isExpandAll;
      this.$nextTick(() => {
        this.refreshTable = true;
        
        // 当isExpandAll为true时，手动展开所有节点
        if (this.isExpandAll) {
          this.$nextTick(() => {
            this.expandAll(this.menuList);
          });
        }
      });
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset();
      this.getTreeselect();
      if (row != null && row.menuId) {
        this.form.parentId = row.menuId;
        this.form.clientType = row.clientType;
      }
      this.open = true;
      this.title = "添加菜单";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.getTreeselect();
      getInternalMenu(row.menuId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改菜单";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.menuId != undefined) {
            updateInternalMenu(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addInternalMenu(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const menuIds = row.menuId || this.ids;
      this.$modal.confirm('是否确认删除菜单编号为"' + menuIds + '"的数据项?').then(function() {
        return delInternalMenu(menuIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 选择条目触发 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.menuId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 修改状态 */
    handleStatusChange(row) {
      let text = row.status === "0" ? "启用" : "停用";
      this.$modal.confirm('确认要"' + text + '""' + row.menuName + '"菜单吗?').then(function() {
        return updateInternalMenu(row);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(function() {
        row.status = row.status === "0" ? "1" : "0";
      });
    }
  }
};
</script>

<style scoped>
.el-input-number {
  width: 100%;
}

.new-tag {
  background-color: #f56c6c;
  color: white;
  padding: 2px 5px;
  border-radius: 3px;
  font-size: 12px;
  margin-right: 5px;
  position: relative;
  top: -1px;
}

/* 强制显示树形表格的展开箭头 */
::v-deep .el-table__expand-icon {
  visibility: visible !important;
  transform: rotate(0deg) !important;
}

::v-deep .el-table__expand-icon--expanded {
  transform: rotate(90deg) !important;
}

::v-deep .el-table__expand-icon .el-icon-arrow-right {
  font-size: 14px;
  color: #409EFF !important;
  font-weight: bold;
}

::v-deep .el-table__indent {
  padding-left: 25px !important;
}

/* 调试样式 */
.has-children {
  background-color: rgba(64, 158, 255, 0.1);
}

.level-1 {
  font-weight: bold;
}
</style> 