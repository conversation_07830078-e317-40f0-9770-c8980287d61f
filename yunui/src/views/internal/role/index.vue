<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="角色名称" prop="roleName">
        <el-input
          v-model="queryParams.roleName"
          placeholder="请输入角色名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="权限字符" prop="roleKey">
        <el-input
          v-model="queryParams.roleKey"
          placeholder="请输入权限字符"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="角色状态"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:internalRole:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:internalRole:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:internalRole:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table 
      v-loading="loading" 
      :data="roleList" 
      row-key="roleId"
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="角色名称" prop="roleName" :show-overflow-tooltip="true" width="150" />
      <el-table-column label="角色编号" prop="roleId" width="120" />
      <el-table-column label="父角色" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.parentId === 0 ? '顶级角色' : scope.row.parentName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="权限字符" prop="roleKey" :show-overflow-tooltip="true" width="150" />
      <el-table-column label="显示顺序" prop="orderNum" width="100" />
      <el-table-column label="状态" align="center" width="100">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            active-value="0"
            inactive-value="1"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:internalRole:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-plus"
            @click="handleAddChild(scope.row)"
            v-hasPermi="['system:internalRole:add']"
          >添加子角色</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:internalRole:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改角色配置对话框 -->
    <el-dialog 
      :title="title" 
      :visible.sync="open" 
      width="500px" 
      append-to-body
      @close="cancel"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item v-if="form.roleId" label="上级角色" prop="parentId">
          <treeselect 
            v-model="form.parentId" 
            :options="roleOptions"
            :normalizer="normalizer"
            :show-count="true"
            placeholder="选择上级角色"
          />
        </el-form-item>
        <el-form-item v-else-if="title === '添加子角色'" label="上级角色" prop="parentId">
          <treeselect 
            v-model="form.parentId"
            :options="roleOptions"
            :normalizer="normalizer"
            :show-count="true"
            :disable-branch-nodes="true"
            :disabled="true"
            value-consists-of="LEAF_PRIORITY"
            placeholder="选择上级角色"
          />
        </el-form-item>
        <el-form-item v-else-if="!form.roleId && form.parentId !== 0 && form.parentId !== null" label="上级角色" prop="parentId">
          <treeselect 
            v-model="form.parentId" 
            :options="roleOptions"
            :normalizer="normalizer"
            :show-count="true"
            placeholder="选择上级角色"
          />
        </el-form-item>
        <el-form-item label="角色名称" prop="roleName">
          <el-input v-model="form.roleName" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item prop="roleKey">
          <span slot="label">
            <el-tooltip content="控制器中定义的权限字符" placement="top">
              <i class="el-icon-question"></i>
            </el-tooltip>
            权限字符
          </span>
          <el-input v-model="form.roleKey" placeholder="请输入权限字符" />
        </el-form-item>
        <el-form-item label="角色顺序" prop="orderNum">
          <el-input-number v-model="form.orderNum" controls-position="right" :min="0" />
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in dict.type.sys_normal_disable"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="菜单权限">
          <el-checkbox v-model="menuExpand" @change="handleCheckedTreeExpand($event, 'menu')">展开/折叠</el-checkbox>
          <el-checkbox v-model="menuNodeAll" @change="handleCheckedTreeNodeAll($event, 'menu')">全选/全不选</el-checkbox>
          <el-checkbox v-model="form.menuCheckStrictly" @change="handleCheckedTreeConnect($event, 'menu')">父子联动</el-checkbox>
          <el-tree
            class="tree-border"
            :data="menuOptions"
            show-checkbox
            ref="menu"
            node-key="id"
            :check-strictly="!form.menuCheckStrictly"
            empty-text="加载中，请稍候"
            :props="defaultProps"
          ></el-tree>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listInternalRole, getInternalRole, delInternalRole, addInternalRole, updateInternalRole, changeInternalRoleStatus } from "@/api/system/internalRole";
import { treeselect as menuTreeselect, internalMenuTreeselect, internalRoleMenuTreeselect } from "@/api/system/menu";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "InternalRole",
  dicts: ['sys_normal_disable'],
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 角色表格数据
      roleList: [],
      // 角色树选项
      roleOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      menuExpand: false,
      menuNodeAll: false,
      // 日期范围
      dateRange: [],
      // 菜单列表
      menuOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        roleName: undefined,
        roleKey: undefined,
        status: undefined
      },
      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label"
      },
      // 表单校验
      rules: {
        roleName: [
          { required: true, message: "角色名称不能为空", trigger: "blur" }
        ],
        roleKey: [
          { required: true, message: "权限字符不能为空", trigger: "blur" }
        ],
        orderNum: [
          { required: true, message: "角色顺序不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询角色列表 */
    getList() {
      this.loading = true;
      listInternalRole(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
          // 处理原始数据，确保parentName字段正确设置
          const roleData = response.rows;
          
          // 创建角色ID到角色名称的映射
          const roleMap = {};
          roleData.forEach(role => {
            roleMap[role.roleId] = role.roleName;
          });
          
          // 为每个角色设置parentName
          roleData.forEach(role => {
            if (role.parentId && role.parentId !== 0) {
              role.parentName = roleMap[role.parentId] || '';
            }
          });
          
          // 将角色列表转换为树形结构
          this.roleList = this.handleTree(roleData, "roleId", "parentId");
          this.total = response.total;
          this.loading = false;
          
          // 默认展开所有节点
          this.$nextTick(() => {
            this.expandAll(this.roleList);
          });
        }
      );
    },
    /** 递归展开所有树节点 */
    expandAll(rows) {
      if (!rows || !this.$refs.roleTable) return;
      rows.forEach(row => {
        this.$refs.roleTable.toggleRowExpansion(row, true);
        if (row.children && row.children.length > 0) {
          this.expandAll(row.children);
        }
      });
    },
    /** 查询菜单树结构 */
    getMenuTreeselect() {
      internalMenuTreeselect().then(response => {
        console.log("获取内部菜单树成功:", response);
        // 直接使用menus字段而不是data
        this.menuOptions = response.menus;
      }).catch(error => {
        console.error("获取内部菜单树失败:", error);
      });
    },
    /** 查询角色树结构 */
    getRoleTreeselect() {
      listInternalRole().then(response => {
        // 添加顶级节点
        const rootNode = { roleId: 0, roleName: "顶级角色", children: [] };
        const treeData = this.handleTree(response.rows, "roleId", "parentId");
        rootNode.children = treeData;
        this.roleOptions = [rootNode];
      });
    },
    // 所有菜单节点数据
    getMenuAllCheckedKeys() {
      // 目前被选中的菜单节点
      let checkedKeys = this.$refs.menu.getCheckedKeys();
      // 半选中的菜单节点
      let halfCheckedKeys = this.$refs.menu.getHalfCheckedKeys();
      checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);
      return checkedKeys;
    },
    /** 根据角色ID查询菜单树结构 */
    getRoleMenuTreeselect(roleId) {
      return internalRoleMenuTreeselect(roleId).then(response => {
        console.log("获取角色菜单树成功:", response);
        // 确保menuOptions是正确的格式
        if (response.menus && !Array.isArray(response.menus)) {
          // 如果返回的不是数组，尝试转换为数组格式
          this.menuOptions = [response.menus];
        } else {
          this.menuOptions = response.menus || [];
        }
        return response;
      }).catch(error => {
        console.error("获取角色菜单树失败:", error);
        return Promise.reject(error);
      });
    },
    // 角色状态修改
    handleStatusChange(row) {
      let text = row.status === "0" ? "启用" : "停用";
      this.$modal.confirm('确认要"' + text + '""' + row.roleName + '"角色吗？').then(function() {
        return changeInternalRoleStatus(row.roleId, row.status);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch((error) => {
        console.error("状态修改失败:", error);
        // 回滚状态
        row.status = row.status === "0" ? "1" : "0";
        this.$modal.msgError("状态修改失败: " + (error?.message || "未知错误"));
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      if (this.$refs.menu != undefined) {
        this.$refs.menu.setCheckedKeys([]);
      }
      this.menuExpand = false,
      this.menuNodeAll = false,
      this.form = {
        roleId: null,
        roleName: null,
        roleKey: null,
        orderNum: 0,
        status: "0",
        menuIds: [],
        parentId: 0,
        menuCheckStrictly: true,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.roleId)
      this.single = selection.length!=1
      this.multiple = !selection.length
    },
    // 树权限（展开/折叠）
    handleCheckedTreeExpand(value, type) {
      if (type == 'menu') {
        let treeList = this.menuOptions;
        for (let i = 0; i < treeList.length; i++) {
          this.$refs.menu.store.nodesMap[treeList[i].id].expanded = value;
        }
      }
    },
    // 树权限（全选/全不选）
    handleCheckedTreeNodeAll(value, type) {
      if (type == 'menu') {
        this.$refs.menu.setCheckedNodes(value ? this.menuOptions: []);
      }
    },
    // 树权限（父子联动）
    handleCheckedTreeConnect(value, type) {
      if (type == 'menu') {
        this.form.menuCheckStrictly = value ? true: false;
      }
    },
    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id || node.roleId || node.menuId,
        label: node.label || node.roleName || node.menuName,
        children: node.children
      };
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.getMenuTreeselect();
      this.getRoleTreeselect();
      
      // 默认设置为顶级角色
      this.form.parentId = 0;
      
      // 确保菜单树已经加载并且表单初始化完成
      this.$nextTick(() => {
        if (!this.menuOptions || this.menuOptions.length === 0) {
          internalMenuTreeselect().then(response => {
            console.log("二次加载内部菜单树成功:", response);
            // 直接使用menus字段而不是data
            this.menuOptions = response.menus;
          }).catch(error => {
            console.error("二次加载内部菜单树失败:", error);
          });
        }
      });
      this.open = true;
      this.title = "添加角色";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.getRoleTreeselect();
      const roleId = row.roleId || this.ids;
      this.open = true;
      this.title = "修改角色";
      
      // 直接从row中获取基础数据，避免等待API返回
      if (row.roleName) {
        this.form = {
          roleId: row.roleId,
          roleName: row.roleName,
          roleKey: row.roleKey,
          parentId: row.parentId || 0,
          orderNum: row.orderNum,
          status: row.status,
          remark: row.remark,
          menuCheckStrictly: true
        };
      }
      
      // 获取菜单权限数据
      const roleMenu = this.getRoleMenuTreeselect(roleId);
      
      // 获取完整的角色数据
      getInternalRole(roleId).then(response => {
        console.log("角色详情数据:", response.data);
        // 更新表单数据
        if (response.data) {
          // 确保必要的字段都存在
          this.form = {
            ...this.form,
            ...response.data,
            menuCheckStrictly: true
          };
        }
        
        this.$nextTick(() => {
          roleMenu.then(res => {
            let checkedKeys = res.checkedKeys;
            if (Array.isArray(checkedKeys)) {
              checkedKeys.forEach((v) => {
                this.$nextTick(() => {
                  this.$refs.menu.setChecked(v, true, false);
                });
              });
            }
          }).catch(err => {
            console.error("获取菜单树错误:", err);
          });
        });
      }).catch(err => {
        console.error("获取角色详情错误:", err);
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      console.log("提交表单数据:", this.form);
      this.$refs["form"].validate(valid => {
        console.log("表单验证结果:", valid);
        if (valid) {
          // 获取所有选中的菜单ID
          this.form.menuIds = this.getMenuAllCheckedKeys();
          console.log("选中的菜单ID:", this.form.menuIds);
          
          if (this.form.roleId != undefined) {
            // 添加必要的字段
            this.form.updateTime = new Date(); // 添加更新时间
            
            // 确保所有必要的字段存在
            const submitData = {
              roleId: this.form.roleId,
              parentId: this.form.parentId || 0,
              roleName: this.form.roleName,
              roleKey: this.form.roleKey,
              orderNum: this.form.orderNum || 0,
              status: this.form.status || "0",
              remark: this.form.remark,
              menuIds: this.form.menuIds,
              updateTime: this.form.updateTime
            };
            
            console.log("准备提交修改请求:", submitData);
            updateInternalRole(submitData).then(response => {
              console.log("修改成功响应:", response);
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).catch(error => {
              console.error("修改请求失败:", error);
              this.$modal.msgError("修改失败: " + (error.message || "未知错误"));
            });
          } else {
            // 准备新增数据
            const submitData = {
              parentId: this.form.parentId || 0,
              roleName: this.form.roleName,
              roleKey: this.form.roleKey,
              orderNum: this.form.orderNum || 0,
              status: this.form.status || "0",
              remark: this.form.remark,
              menuIds: this.form.menuIds
            };
            
            console.log("准备提交新增请求:", submitData);
            addInternalRole(submitData).then(response => {
              console.log("新增成功响应:", response);
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).catch(error => {
              console.error("新增请求失败:", error);
              this.$modal.msgError("新增失败: " + (error.message || "未知错误"));
            });
          }
        } else {
          console.warn("表单验证不通过");
          // 尝试获取验证错误信息
          setTimeout(() => {
            const errorFields = this.$refs["form"].fields.filter(field => field.validateState === "error");
            if (errorFields.length > 0) {
              console.log("验证错误字段:", errorFields.map(f => f.prop + ": " + f.validateMessage));
              this.$modal.msgWarning(errorFields[0].validateMessage);
            }
          }, 100);
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const roleIds = row.roleId || this.ids;
      this.$modal.confirm('是否确认删除角色编号为"' + roleIds + '"的数据项？').then(function() {
        // 确保参数格式一致，根据API实现修正为正确格式
        return delInternalRole(roleIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch((error) => {
        console.error("删除失败:", error);
        this.$modal.msgError("删除失败: " + (error?.message || "未知错误"));
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/internalRole/export', {
        ...this.queryParams
      }, `internalRole_${new Date().getTime()}.xlsx`)
    },
    /** 添加子角色按钮操作 */
    handleAddChild(row) {
      this.reset();
      
      // 先加载角色树
      listInternalRole().then(response => {
        // 构建角色树
        const rootNode = { roleId: 0, roleName: "顶级角色", children: [] };
        const treeData = this.handleTree(response.rows, "roleId", "parentId");
        rootNode.children = treeData;
        this.roleOptions = [rootNode];
        
        // 确保角色树加载完后，再设置父角色ID
        this.form.parentId = row.roleId;
        
        // 加载菜单树
        this.getMenuTreeselect();
        
        // 打开对话框
        this.open = true;
        this.title = "添加子角色";
      }).catch(error => {
        console.error("加载角色树失败:", error);
        this.$modal.msgError("加载角色数据失败");
      });
    }
  }
};
</script>

<style lang="scss" scoped>
/* 自定义树形表格的展开/折叠箭头样式 */
::v-deep .el-table__expand-icon {
  font-size: 14px;
  height: 18px;
  width: 18px;
  margin-right: 4px;
  border-radius: 2px;
  
  /* 修改箭头旋转中心和角度 */
  .el-icon-arrow-right {
    color: #909399;
    font-weight: normal;
    opacity: 0.7;
    transition: all 0.2s ease-in-out;
    transform-origin: center;
  }
  
  /* 展开状态下的箭头样式 */
  &.el-table__expand-icon--expanded {
    transform: none;
    .el-icon-arrow-right {
      transform: rotate(90deg);
    }
  }
}

/* 悬停时的样式变化 */
::v-deep .el-table__row:hover .el-table__expand-icon .el-icon-arrow-right {
  color: #606266;
  opacity: 1;
}

/* 确保箭头与内容对齐 */
::v-deep .el-table__indent {
  padding-left: 0;
  margin-right: 0;
}
</style> 