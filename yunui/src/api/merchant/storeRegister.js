import request from '@/utils/request'

// 查询商户列表
export function listStore(query) {
  return request({
    url: '/merchant/experienceTable/list',
    method: 'get',
    params: query
  })
}

// 查询商户详细
export function getStore(id) {
  return request({
    url: '/merchant/experienceTable/' + id,
    method: 'get'
  })
}

// 新增商户
export function addStore(data) {
  return request({
    url: '/merchant/experienceTable',
    method: 'post',
    data: data
  })
}

// 修改商户
export function updateStore(data) {
  return request({
    url: '/merchant/experienceTable',
    method: 'put',
    data: data
  })
}

// 删除商户
export function delStore(ids) {
  return request({
    url: '/merchant/experienceTable/' + ids,
    method: 'delete'
  })
}

// 更新商户状态
export function updateStoreStatus(id) {
  return request({
    url: '/merchant/experienceTable/updateStatus/' + id,
    method: 'post'
  })
}

// 审核商户
export function examineStore(id) {
  return request({
    url: '/merchant/experienceTable/examine/' + id,
    method: 'post'
  })
}

// 员工审核商户
export function examineEmployee(id) {
  return request({
    url: '/merchant/experienceTable/examineEmployee/' + id,
    method: 'post'
  })
}

// 获取商户跟进记录列表
export function followRecords(merchantId) {
  return request({
    url: '/merchant/experienceTable/followUpRecords/' + merchantId,
    method: 'get'
  })
}

// 新增商户跟进记录
export function addFollowUpRecord(data) {
  return request({
    url: '/merchant/experienceTable/followUpRecord',
    method: 'post',
    data: data
  })
}
