import request from '@/utils/request'

// 查询公司列表
export function listCompany(query) {
  return request({
    url: '/merchant/company/list',
    method: 'get',
    params: query
  })
}

// 查询公司详细
export function getCompany(id) {
  return request({
    url: '/merchant/company/' + id,
    method: 'get'
  })
}

// 新增公司
export function addCompany(data) {
  return request({
    url: '/merchant/company',
    method: 'post',
    data: data
  })
}

// 修改公司
export function updateCompany(data) {
  return request({
    url: '/merchant/company',
    method: 'put',
    data: data
  })
}

// 删除公司
export function delCompany(ids) {
  return request({
    url: '/merchant/company/' + ids,
    method: 'delete'
  })
}

// 公司充值
export function rechargeCompany(companyId, data) {
  return request({
    url: '/merchant/company/' + companyId + '/recharge',
    method: 'post',
    data: data
  })
}

// 获取公司门店详情
export function getCompanyStoresDetail(companyId) {
  return request({
    url: '/merchant/company/' + companyId + '/stores',
    method: 'get'
  })
}

// 获取可用的软件版本列表
export function getAvailableVersions() {
  return request({
    url: '/merchant/company/available-versions',
    method: 'get'
  })
}

// 获取公司关联的版本信息
export function getCompanyVersions(companyId) {
  return request({
    url: '/merchant/company/' + companyId + '/versions',
    method: 'get'
  })
}

// 为公司新增版本关联
export function addCompanyVersion(companyId, data) {
  return request({
    url: '/merchant/company/' + companyId + '/versions',
    method: 'post',
    data: data
  })
}

// 获取公司版本详细信息（包含到期时间）
export function getCompanyVersionsDetail(companyId) {
  return request({
    url: '/merchant/company/' + companyId + '/versions/detail',
    method: 'get'
  })
}

// 批量更新公司版本到期时间（续费）
export function renewCompanyVersions(companyId, data) {
  return request({
    url: '/merchant/company/' + companyId + '/versions/renew',
    method: 'put',
    data: data
  })
}

// 批量续费多个公司的版本
export function batchRenewCompanyVersions(data) {
  return request({
    url: '/merchant/company/batch/versions/renew',
    method: 'post',
    data: data
  })
}

// 撤销批量续费操作
export function undoBatchRenewCompanyVersions(data) {
  return request({
    url: '/merchant/company/batch/versions/renew/undo',
    method: 'post',
    data: data
  })
}
