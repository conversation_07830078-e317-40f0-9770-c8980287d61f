import request from '@/utils/request'

// 查询门店列表
export function listStore(query) {
  return request({
    url: '/merchant/store/list',
    method: 'get',
    params: query
  })
}

// 查询所有公司列表（不分页）
export function listAllCompanies() {
  return request({
    url: '/merchant/company/list',
    method: 'get',
    params: {
      pageNum: 1,
      pageSize: 1000 // 获取大量数据，实际使用中可以考虑专门的不分页接口
    }
  })
}

// 查询门店详细
export function getStore(id) {
  return request({
    url: '/merchant/store/' + id,
    method: 'get'
  })
}

// 新增门店
export function addStore(data) {
  return request({
    url: '/merchant/store',
    method: 'post',
    data: data
  })
}

// 修改门店
export function updateStore(data) {
  return request({
    url: '/merchant/store',
    method: 'put',
    data: data
  })
}

// 删除门店
export function delStore(ids) {
  return request({
    url: '/merchant/store/' + ids,
    method: 'delete'
  })
}
