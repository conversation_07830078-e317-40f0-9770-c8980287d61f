import request from '@/utils/request'

// 查询客户列表
export function listCcuser(query) {
  return request({
    url: '/merchant/ccuser/list',
    method: 'get',
    params: query
  })
}

// 查询客户详细
export function getCcuser(id) {
  return request({
    url: '/merchant/ccuser/' + id,
    method: 'get'
  })
}

// 新增客户
export function addCcuser(data) {
  return request({
    url: '/merchant/ccuser',
    method: 'post',
    data: data
  })
}

// 修改客户
export function updateCcuser(data) {
  return request({
    url: '/merchant/ccuser',
    method: 'put',
    data: data
  })
}

// 删除客户
export function delCcuser(ids) {
  return request({
    url: '/merchant/ccuser/' + ids,
    method: 'delete'
  })
}
