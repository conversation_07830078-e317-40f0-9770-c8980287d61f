import request from '@/utils/request'

// 查询产品同步列表
export function listProductSync(query) {
  return request({
    url: '/merchant/productSync/list',
    method: 'get',
    params: query
  })
}

// 查询产品同步详细
export function getProductSync(productId) {
  return request({
    url: '/merchant/productSync/' + productId,
    method: 'get'
  })
}

// 导出产品同步列表
export function exportProductSync(query) {
  return request({
    url: '/merchant/productSync/export',
    method: 'post',
    data: query,
    responseType: 'blob'
  })
}

// 获取产品同步统计信息
export function getProductSyncStatistics() {
  return request({
    url: '/public/productSync/statistics',
    method: 'get',
    headers: {
      'isToken': false  // 不发送token
    }
  })
}

// 同步产品数据
export function syncProductData(productIds) {
  return request({
    url: '/merchant/productSync/sync',
    method: 'post',
    data: {
      productIds: productIds
    }
  })
}

// 批量同步所有产品数据
export function batchSyncProductData() {
  return request({
    url: '/merchant/productSync/batch-sync',
    method: 'post'
  })
}

// 获取所有产品同步数据（不分页）
export function getAllProductSync(query) {
  return request({
    url: '/merchant/productSync/all',
    method: 'get',
    params: query
  })
}
