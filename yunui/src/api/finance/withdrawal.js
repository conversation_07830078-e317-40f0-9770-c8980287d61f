import request from '@/utils/request'

// 查询提现申请列表
export function listWithdrawal(query) {
  return request({
    url: '/finance/withdrawal/list',
    method: 'get',
    params: query
  })
}

// 查询提现申请详细
export function getWithdrawal(withdrawalId) {
  return request({
    url: '/finance/withdrawal/' + withdrawalId,
    method: 'get'
  })
}

// 创建提现申请
export function createWithdrawal(data) {
  return request({
    url: '/finance/withdrawal/apply',
    method: 'post',
    data: data
  })
}

// 审核提现申请
export function reviewWithdrawal(withdrawalId, data) {
  return request({
    url: '/finance/withdrawal/' + withdrawalId + '/review',
    method: 'post',
    data: data
  })
}

// 处理提现申请
export function processWithdrawal(withdrawalId, data) {
  return request({
    url: '/finance/withdrawal/' + withdrawalId + '/process',
    method: 'post',
    data: data
  })
}

// 取消提现申请
export function cancelWithdrawal(withdrawalId) {
  return request({
    url: '/finance/withdrawal/' + withdrawalId + '/cancel',
    method: 'post'
  })
}

// 导出提现申请
export function exportWithdrawal(query) {
  return request({
    url: '/finance/withdrawal/export',
    method: 'get',
    params: query
  })
}
