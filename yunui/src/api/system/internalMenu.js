import request from '@/utils/request'

// 查询内部菜单列表
export function listInternalMenu(query) {
  return request({
    url: '/internal/menu/list',
    method: 'get',
    params: query
  })
}

// 查询内部菜单详细
export function getInternalMenu(menuId) {
  return request({
    url: '/internal/menu/' + menuId,
    method: 'get'
  })
}

// 查询内部菜单下拉树结构
export function internalMenuTreeselect(query) {
  return request({
    url: '/internal/menu/treeselect',
    method: 'get',
    params: query
  })
}

// 根据角色ID查询菜单下拉树结构
export function internalRoleMenuTreeselect(roleId, query) {
  return request({
    url: '/internal/menu/roleMenuTreeselect/' + roleId,
    method: 'get',
    params: query
  })
}

// 新增内部菜单
export function addInternalMenu(data) {
  return request({
    url: '/internal/menu',
    method: 'post',
    data: data
  })
}

// 修改内部菜单
export function updateInternalMenu(data) {
  return request({
    url: '/internal/menu',
    method: 'put',
    data: data
  })
}

// 删除内部菜单
export function delInternalMenu(menuId) {
  return request({
    url: '/internal/menu/' + menuId,
    method: 'delete'
  })
} 