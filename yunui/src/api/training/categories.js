import request from '@/utils/request'

// 查询培训分类列表
export function listTrainingCategories(query) {
  return request({
    url: '/training/categories/list',
    method: 'get',
    params: query
  })
}

// 查询培训分类树形结构
export function getTrainingCategoriesTree() {
  return request({
    url: '/training/categories/tree',
    method: 'get'
  })
}

// 查询培训分类详细
export function getTrainingCategories(categoryId) {
  return request({
    url: '/training/categories/' + categoryId,
    method: 'get'
  })
}

// 新增培训分类
export function addTrainingCategories(data) {
  return request({
    url: '/training/categories',
    method: 'post',
    data: data
  })
}

// 修改培训分类
export function updateTrainingCategories(data) {
  return request({
    url: '/training/categories',
    method: 'put',
    data: data
  })
}

// 删除培训分类
export function delTrainingCategories(categoryIds) {
  return request({
    url: '/training/categories/' + categoryIds,
    method: 'delete'
  })
}
