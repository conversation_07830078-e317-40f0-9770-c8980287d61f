import request from '@/utils/request'

// 查询订单列表
export function listOrder(query) {
  return request({
    url: '/order/list',
    method: 'get',
    params: query
  })
}

// 查询订单详细
export function getOrder(orderId) {
  return request({
    url: '/order/' + orderId,
    method: 'get'
  })
}

// 新增订单
export function addOrder(data) {
  return request({
    url: '/order',
    method: 'post',
    data: data
  })
}

// 修改订单
export function updateOrder(data) {
  return request({
    url: '/order',
    method: 'put',
    data: data
  })
}

// 更新订单备注
export function updateOrderRemark(data) {
  return request({
    url: '/order/remark',
    method: 'put',
    data: data
  })
}

// 删除订单（实际调用取消订单接口）
export function delOrder(orderIds) {
  return request({
    url: '/order/cancel/' + orderIds,
    method: 'put'
  })
}

// 取消订单
export function cancelOrder(orderIds) {
  return request({
    url: '/order/cancel/' + orderIds,
    method: 'put'
  })
}

// 导出订单
export function exportOrder(query) {
  return request({
    url: '/order/export',
    method: 'post',
    data: query
  })
}

// 获取可选派单人员
export function getAvailableDispatchStaff(orderId) {
  return request({
    url: '/order/dispatch-staff/' + orderId,
    method: 'get'
  })
}

// 更新订单派单人员
export function updateOrderDispatchStaff(data) {
  const formData = new FormData();
  formData.append('order_number', data.order_number);
  formData.append('staff_id', data.staff_id);

  // 如果有服务时间参数，也添加到FormData中
  if (data.service_date) {
    formData.append('service_date', data.service_date);
  }
  if (data.service_hour) {
    formData.append('service_hour', data.service_hour);
  }

  return request({
    url: '/order/dispatch-staff',
    method: 'put',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 获取门店名称列表
export function getStoreNames() {
  return request({
    url: '/order/store-names',
    method: 'get'
  })
}

// 获取订单统计信息
export function getOrderStatistics(query) {
  return request({
    url: '/order/statistics',
    method: 'get',
    params: query
  })
}
